import React, { useState, useEffect } from 'react';
import { Form, Select, Slider, Checkbox, Button, Input, Collapse, Radio, Space, Divider } from 'antd';
import { FilterOutlined, ReloadOutlined } from '@ant-design/icons';
import { useSelector, useDispatch } from 'react-redux';
import { getAllCategories } from '../../redux/slices/categorySlice';

const { Option } = Select;
const { Panel } = Collapse;
const { Search } = Input;

const ProductFilter = ({ onFilter, initialFilters = {} }) => {
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const { categories } = useSelector((state) => state.category);
  const [priceRange, setPriceRange] = useState([0, 10000]);

  // 获取所有分类
  useEffect(() => {
    dispatch(getAllCategories());
  }, [dispatch]);

  // 重置筛选条件
  const handleReset = () => {
    form.resetFields();
    setPriceRange([0, 10000]);
    onFilter({});
  };

  // 提交筛选条件
  const handleFinish = (values) => {
    const filters = {
      ...values,
      minPrice: priceRange[0],
      maxPrice: priceRange[1],
    };
    onFilter(filters);
  };

  // 处理价格范围变化
  const handlePriceChange = (value) => {
    setPriceRange(value);
  };

  return (
    <div className="product-filter">
      <Collapse
        defaultActiveKey={['1']}
        expandIconPosition="right"
      >
        <Panel header="筛选条件" key="1" extra={<FilterOutlined />}>
          <Form
            form={form}
            layout="vertical"
            onFinish={handleFinish}
            initialValues={initialFilters}
          >
            {/* 关键词搜索 */}
            <Form.Item name="keyword" label="关键词">
              <Search placeholder="搜索商品" allowClear />
            </Form.Item>

            <Divider style={{ margin: '12px 0' }} />

            {/* 分类筛选 */}
            <Form.Item name="category" label="分类">
              <Select placeholder="选择分类" allowClear>
                {categories.map((category) => (
                  <Option key={category._id} value={category._id}>
                    {category.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            {/* 价格范围 */}
            <Form.Item label="价格范围">
              <Slider
                range
                min={0}
                max={10000}
                value={priceRange}
                onChange={handlePriceChange}
                tipFormatter={(value) => `¥${value}`}
              />
              <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: '8px' }}>
                <span>¥{priceRange[0]}</span>
                <span>¥{priceRange[1]}</span>
              </div>
            </Form.Item>

            {/* 评分筛选 */}
            <Form.Item name="rating" label="最低评分">
              <Radio.Group>
                <Space direction="vertical">
                  <Radio value={4}>4星及以上</Radio>
                  <Radio value={3}>3星及以上</Radio>
                  <Radio value={2}>2星及以上</Radio>
                  <Radio value={1}>1星及以上</Radio>
                  <Radio value={0}>全部评分</Radio>
                </Space>
              </Radio.Group>
            </Form.Item>

            {/* 库存状态 */}
            <Form.Item name="inStock" valuePropName="checked">
              <Checkbox>只显示有货商品</Checkbox>
            </Form.Item>

            {/* 排序方式 */}
            <Form.Item name="sortBy" label="排序方式">
              <Select placeholder="排序方式" allowClear>
                <Option value="price_asc">价格从低到高</Option>
                <Option value="price_desc">价格从高到低</Option>
                <Option value="rating_desc">评分从高到低</Option>
                <Option value="newest">最新上架</Option>
                <Option value="popular">销量优先</Option>
              </Select>
            </Form.Item>

            {/* 按钮组 */}
            <Form.Item>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <Button type="primary" htmlType="submit">
                  应用筛选
                </Button>
                <Button icon={<ReloadOutlined />} onClick={handleReset}>
                  重置
                </Button>
              </div>
            </Form.Item>
          </Form>
        </Panel>
      </Collapse>
    </div>
  );
};

export default ProductFilter;