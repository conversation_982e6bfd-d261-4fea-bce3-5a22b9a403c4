import React, { useState } from 'react';
import { List, Card, Button, Typography, Tag, Modal, Popconfirm } from 'antd';
import { EditOutlined, DeleteOutlined, CheckCircleOutlined, PlusOutlined } from '@ant-design/icons';
import AddressForm from './AddressForm';

const { Text, Title } = Typography;

const AddressList = ({
  addresses = [],
  onAdd,
  onUpdate,
  onDelete,
  onSetDefault,
  loading = false,
}) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [editingAddress, setEditingAddress] = useState(null);

  // 打开添加地址模态框
  const showAddModal = () => {
    setEditingAddress(null);
    setModalVisible(true);
  };

  // 打开编辑地址模态框
  const showEditModal = (address) => {
    setEditingAddress(address);
    setModalVisible(true);
  };

  // 关闭模态框
  const handleCancel = () => {
    setModalVisible(false);
    setEditingAddress(null);
  };

  // 处理表单提交
  const handleSubmit = (values) => {
    if (editingAddress) {
      onUpdate && onUpdate({ ...values, _id: editingAddress._id });
    } else {
      onAdd && onAdd(values);
    }
    setModalVisible(false);
  };

  return (
    <div className="address-list">
      <div className="address-list-header" style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '16px' }}>
        <Title level={4}>我的收货地址</Title>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={showAddModal}
          disabled={loading}
        >
          添加新地址
        </Button>
      </div>

      {addresses.length > 0 ? (
        <List
          grid={{ gutter: 16, xs: 1, sm: 1, md: 2, lg: 2, xl: 3, xxl: 3 }}
          dataSource={addresses}
          renderItem={(address) => (
            <List.Item>
              <Card
                className={`address-card ${address.isDefault ? 'default-address' : ''}`}
                style={{
                  borderColor: address.isDefault ? '#52c41a' : '#d9d9d9',
                }}
                actions={[
                  <Button
                    key="edit"
                    type="text"
                    icon={<EditOutlined />}
                    onClick={() => showEditModal(address)}
                  >
                    编辑
                  </Button>,
                  <Popconfirm
                    key="delete"
                    title="确定要删除此地址吗？"
                    onConfirm={() => onDelete && onDelete(address._id)}
                    okText="确定"
                    cancelText="取消"
                    disabled={address.isDefault}
                  >
                    <Button
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                      disabled={address.isDefault}
                    >
                      删除
                    </Button>
                  </Popconfirm>,
                  !address.isDefault && (
                    <Button
                      key="setDefault"
                      type="text"
                      icon={<CheckCircleOutlined />}
                      onClick={() => onSetDefault && onSetDefault(address._id)}
                    >
                      设为默认
                    </Button>
                  ),
                ]}
              >
                <div className="address-card-content">
                  {address.isDefault && (
                    <Tag color="success" style={{ marginBottom: '8px' }}>
                      默认地址
                    </Tag>
                  )}
                  <div className="address-name-phone" style={{ marginBottom: '8px' }}>
                    <Text strong style={{ marginRight: '16px' }}>
                      {address.name}
                    </Text>
                    <Text>{address.phone}</Text>
                  </div>
                  <div className="address-detail">
                    <Text>
                      {address.province} {address.city} {address.district} {address.address}
                    </Text>
                    {address.postalCode && (
                      <div style={{ marginTop: '4px' }}>
                        <Text type="secondary">邮编: {address.postalCode}</Text>
                      </div>
                    )}
                  </div>
                </div>
              </Card>
            </List.Item>
          )}
        />
      ) : (
        <div className="no-address" style={{ textAlign: 'center', padding: '24px' }}>
          <Text type="secondary">暂无收货地址</Text>
          <div style={{ marginTop: '16px' }}>
            <Button type="primary" icon={<PlusOutlined />} onClick={showAddModal}>
              添加新地址
            </Button>
          </div>
        </div>
      )}

      <Modal
        title={editingAddress ? '编辑地址' : '添加新地址'}
        open={modalVisible}
        onCancel={handleCancel}
        footer={null}
        destroyOnClose
      >
        <AddressForm
          initialValues={editingAddress || {}}
          onSubmit={handleSubmit}
          loading={loading}
        />
      </Modal>
    </div>
  );
};

export default AddressList;