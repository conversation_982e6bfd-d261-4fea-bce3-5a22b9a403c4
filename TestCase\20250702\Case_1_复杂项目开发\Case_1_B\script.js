// 用户管理
const users = [];

// 商品数据
const products = [
    { id: 1, name: "商品1", price: 100 },
    { id: 2, name: "商品2", price: 200 },
    { id: 3, name: "商品3", price: 300 }
];

// 购物车
let cart = [];

// 订单
let orders = [];

// DOM 加载完成后执行
document.addEventListener('DOMContentLoaded', () => {
    // 渲染商品列表
    renderProducts();

    // 登录表单提交
    document.getElementById('loginForm').addEventListener('submit', (e) => {
        e.preventDefault();
        alert('登录成功');
    });

    // 注册表单提交
    document.getElementById('registerForm').addEventListener('submit', (e) => {
        e.preventDefault();
        alert('注册成功');
    });

    // 结算按钮点击
    document.getElementById('checkout').addEventListener('click', () => {
        if (cart.length === 0) {
            alert('购物车为空');
            return;
        }
        // 模拟支付
        alert('支付成功');
        // 生成订单
        const order = {
            id: orders.length + 1,
            items: [...cart],
            total: cart.reduce((sum, item) => sum + item.price, 0),
            date: new Date().toLocaleString()
        };
        orders.push(order);
        cart = [];
        renderCart();
        renderOrders();
    });
});

// 渲染商品列表
function renderProducts() {
    const productList = document.querySelector('.product-list');
    productList.innerHTML = products.map(product => 
        `<div class="product">
            <h3>${product.name}</h3>
            <p>价格: ${product.price}元</p>
            <button onclick="addToCart(${product.id})">加入购物车</button>
        </div>`
    ).join('');
}

// 加入购物车
function addToCart(productId) {
    const product = products.find(p => p.id === productId);
    if (product) {
        cart.push(product);
        renderCart();
    }
}

// 渲染购物车
function renderCart() {
    const cartItems = document.querySelector('.cart-items');
    cartItems.innerHTML = cart.map(item => 
        `<div class="cart-item">
            <h3>${item.name}</h3>
            <p>价格: ${item.price}元</p>
        </div>`
    ).join('');
}

// 渲染订单
function renderOrders() {
    const orderList = document.querySelector('.order-list');
    orderList.innerHTML = orders.map(order => 
        `<div class="order">
            <h3>订单号: ${order.id}</h3>
            <p>总价: ${order.total}元</p>
            <p>日期: ${order.date}</p>
        </div>`
    ).join('');
}