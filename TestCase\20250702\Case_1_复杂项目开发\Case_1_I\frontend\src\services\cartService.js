import apiClient from './apiClient';

// 获取购物车
const getCart = async (token) => {
  const response = await apiClient.get('/cart', {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

// 添加商品到购物车
const addToCart = async (productId, quantity, token) => {
  const response = await apiClient.post(
    '/cart/add',
    { productId, quantity },
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    }
  );
  return response.data;
};

// 更新购物车商品数量
const updateCartItem = async (productId, quantity, token) => {
  const response = await apiClient.put(
    '/cart/update',
    { productId, quantity },
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    }
  );
  return response.data;
};

// 从购物车移除商品
const removeFromCart = async (productId, token) => {
  const response = await apiClient.delete(`/cart/remove/${productId}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

// 清空购物车
const clearCart = async (token) => {
  const response = await apiClient.delete('/cart/clear', {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

const cartService = {
  getCart,
  addToCart,
  updateCartItem,
  removeFromCart,
  clearCart,
};

export default cartService;