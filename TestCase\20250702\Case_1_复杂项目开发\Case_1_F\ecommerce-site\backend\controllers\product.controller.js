const Product = require('../models/product.model');
const { validationResult } = require('express-validator');

// @desc    获取所有商品
// @route   GET /api/products
// @access  公开
exports.getProducts = async (req, res) => {
  try {
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;
    const sort = {};

    // 排序
    if (req.query.sortBy) {
      const parts = req.query.sortBy.split(':');
      sort[parts[0]] = parts[1] === 'desc' ? -1 : 1;
    } else {
      sort.createdAt = -1; // 默认按创建时间倒序
    }

    // 构建查询条件
    const query = {};
    
    // 按类别过滤
    if (req.query.category) {
      query.category = req.query.category;
    }
    
    // 按价格范围过滤
    if (req.query.minPrice || req.query.maxPrice) {
      query.price = {};
      if (req.query.minPrice) query.price.$gte = parseFloat(req.query.minPrice);
      if (req.query.maxPrice) query.price.$lte = parseFloat(req.query.maxPrice);
    }
    
    // 按名称或描述搜索
    if (req.query.search) {
      query.$text = { $search: req.query.search };
    }

    // 执行查询
    const total = await Product.countDocuments(query);
    
    let products = await Product.find(query)
      .populate('category', 'name')
      .sort(sort)
      .skip(startIndex)
      .limit(limit);
    
    // 构建分页信息
    const pagination = {};
    
    if (endIndex < total) {
      pagination.next = {
        page: page + 1,
        limit
      };
    }
    
    if (startIndex > 0) {
      pagination.prev = {
        page: page - 1,
        limit
      };
    }
    
    res.status(200).json({
      success: true,
      count: products.length,
      pagination,
      total,
      data: products
    });
  } catch (err) {
    console.error('获取商品列表失败:', err);
    res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
  }
};

// @desc    获取单个商品
// @route   GET /api/products/:id
// @access  公开
exports.getProduct = async (req, res) => {
  try {
    const product = await Product.findById(req.params.id).populate('category', 'name');
    
    if (!product) {
      return res.status(404).json({ success: false, message: '未找到该商品' });
    }
    
    res.status(200).json({ success: true, data: product });
  } catch (err) {
    console.error('获取商品详情失败:', err);
    res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
  }
};

// @desc    创建商品
// @route   POST /api/products
// @access  私有 (仅限管理员)
exports.createProduct = async (req, res) => {
  try {
    // 验证请求数据
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }

    // 创建新商品
    const product = await Product.create(req.body);
    
    res.status(201).json({ success: true, data: product });
  } catch (err) {
    console.error('创建商品失败:', err);
    res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
  }
};

// @desc    更新商品
// @route   PUT /api/products/:id
// @access  私有 (仅限管理员)
exports.updateProduct = async (req, res) => {
  try {
    let product = await Product.findById(req.params.id);
    
    if (!product) {
      return res.status(404).json({ success: false, message: '未找到该商品' });
    }
    
    // 更新商品
    product = await Product.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
      runValidators: true
    });
    
    res.status(200).json({ success: true, data: product });
  } catch (err) {
    console.error('更新商品失败:', err);
    res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
  }
};

// @desc    删除商品
// @route   DELETE /api/products/:id
// @access  私有 (仅限管理员)
exports.deleteProduct = async (req, res) => {
  try {
    const product = await Product.findById(req.params.id);
    
    if (!product) {
      return res.status(404).json({ success: false, message: '未找到该商品' });
    }
    
    await product.deleteOne();
    
    res.status(200).json({ success: true, message: '商品已成功删除' });
  } catch (err) {
    console.error('删除商品失败:', err);
    res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
  }
};

// @desc    获取精选商品
// @route   GET /api/products/featured
// @access  公开
exports.getFeaturedProducts = async (req, res) => {
  try {
    const limit = parseInt(req.query.limit, 10) || 5;
    
    const products = await Product.find({ isFeatured: true })
      .populate('category', 'name')
      .limit(limit);
    
    res.status(200).json({ success: true, count: products.length, data: products });
  } catch (err) {
    console.error('获取精选商品失败:', err);
    res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
  }
}; 