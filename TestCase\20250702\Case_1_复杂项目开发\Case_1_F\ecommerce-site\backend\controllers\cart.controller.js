const Cart = require('../models/cart.model');
const Product = require('../models/product.model');

// @desc    获取用户购物车
// @route   GET /api/cart
// @access  私有
exports.getCart = async (req, res) => {
  try {
    let cart = await Cart.findOne({ user: req.user.id })
      .populate('items.product', 'name images price stock');
    
    if (!cart) {
      // 如果用户没有购物车，创建一个新的
      cart = new Cart({
        user: req.user.id,
        items: [],
        totalPrice: 0
      });
      await cart.save();
    }
    
    res.status(200).json({ success: true, data: cart });
  } catch (err) {
    console.error('获取购物车失败:', err);
    res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
  }
};

// @desc    添加商品到购物车
// @route   POST /api/cart
// @access  私有
exports.addToCart = async (req, res) => {
  try {
    const { productId, quantity = 1 } = req.body;
    
    // 验证参数
    if (!productId) {
      return res.status(400).json({ success: false, message: '请提供商品ID' });
    }
    
    // 检查商品是否存在
    const product = await Product.findById(productId);
    if (!product) {
      return res.status(404).json({ success: false, message: '未找到该商品' });
    }
    
    // 检查库存
    if (product.stock < quantity) {
      return res.status(400).json({ success: false, message: '商品库存不足' });
    }
    
    // 查找用户购物车，没有则创建
    let cart = await Cart.findOne({ user: req.user.id });
    if (!cart) {
      cart = new Cart({
        user: req.user.id,
        items: [],
        totalPrice: 0
      });
    }
    
    // 检查商品是否已在购物车中
    const itemIndex = cart.items.findIndex(item => 
      item.product.toString() === productId
    );
    
    if (itemIndex > -1) {
      // 更新购物车中已有商品的数量
      const newQuantity = cart.items[itemIndex].quantity + quantity;
      
      // 再次检查库存
      if (product.stock < newQuantity) {
        return res.status(400).json({ success: false, message: '商品库存不足' });
      }
      
      cart.items[itemIndex].quantity = newQuantity;
    } else {
      // 添加新商品到购物车
      cart.items.push({
        product: productId,
        quantity,
        price: product.price
      });
    }
    
    await cart.save();
    
    // 获取完整的购物车信息
    cart = await Cart.findById(cart._id).populate('items.product', 'name images price stock');
    
    res.status(200).json({ success: true, message: '商品已添加到购物车', data: cart });
  } catch (err) {
    console.error('添加到购物车失败:', err);
    res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
  }
};

// @desc    更新购物车商品数量
// @route   PUT /api/cart/:itemId
// @access  私有
exports.updateCartItem = async (req, res) => {
  try {
    const { quantity } = req.body;
    const { itemId } = req.params;
    
    // 验证参数
    if (!quantity || quantity < 1) {
      return res.status(400).json({ success: false, message: '请提供有效的数量' });
    }
    
    // 查找用户购物车
    let cart = await Cart.findOne({ user: req.user.id });
    if (!cart) {
      return res.status(404).json({ success: false, message: '未找到购物车' });
    }
    
    // 查找购物车项
    const itemIndex = cart.items.findIndex(item => item._id.toString() === itemId);
    if (itemIndex === -1) {
      return res.status(404).json({ success: false, message: '未找到购物车项' });
    }
    
    // 检查库存
    const product = await Product.findById(cart.items[itemIndex].product);
    if (!product) {
      return res.status(404).json({ success: false, message: '未找到相关商品' });
    }
    
    if (product.stock < quantity) {
      return res.status(400).json({ success: false, message: '商品库存不足' });
    }
    
    // 更新购物车项数量
    cart.items[itemIndex].quantity = quantity;
    
    await cart.save();
    
    // 获取完整的购物车信息
    cart = await Cart.findById(cart._id).populate('items.product', 'name images price stock');
    
    res.status(200).json({ success: true, message: '购物车已更新', data: cart });
  } catch (err) {
    console.error('更新购物车失败:', err);
    res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
  }
};

// @desc    从购物车移除商品
// @route   DELETE /api/cart/:itemId
// @access  私有
exports.removeCartItem = async (req, res) => {
  try {
    const { itemId } = req.params;
    
    // 查找用户购物车
    let cart = await Cart.findOne({ user: req.user.id });
    if (!cart) {
      return res.status(404).json({ success: false, message: '未找到购物车' });
    }
    
    // 查找购物车项
    const itemIndex = cart.items.findIndex(item => item._id.toString() === itemId);
    if (itemIndex === -1) {
      return res.status(404).json({ success: false, message: '未找到购物车项' });
    }
    
    // 移除购物车项
    cart.items.splice(itemIndex, 1);
    
    await cart.save();
    
    // 获取完整的购物车信息
    cart = await Cart.findById(cart._id).populate('items.product', 'name images price stock');
    
    res.status(200).json({ success: true, message: '商品已从购物车移除', data: cart });
  } catch (err) {
    console.error('从购物车移除商品失败:', err);
    res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
  }
};

// @desc    清空购物车
// @route   DELETE /api/cart
// @access  私有
exports.clearCart = async (req, res) => {
  try {
    // 查找用户购物车
    let cart = await Cart.findOne({ user: req.user.id });
    if (!cart) {
      return res.status(404).json({ success: false, message: '未找到购物车' });
    }
    
    // 清空购物车项
    cart.items = [];
    cart.totalPrice = 0;
    
    await cart.save();
    
    res.status(200).json({ success: true, message: '购物车已清空', data: cart });
  } catch (err) {
    console.error('清空购物车失败:', err);
    res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
  }
}; 