#!/bin/bash

# 电商网站启动脚本

echo "🚀 启动电商网站..."

# 检查是否安装了 Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js 16+ 版本"
    exit 1
fi

# 检查是否安装了 MongoDB
if ! command -v mongod &> /dev/null; then
    echo "⚠️  MongoDB 未安装，请确保 MongoDB 正在运行或使用 Docker"
fi

# 创建环境配置文件
echo "📝 创建环境配置文件..."

# 后端环境配置
if [ ! -f "backend/.env" ]; then
    cp backend/.env.example backend/.env
    echo "✅ 已创建 backend/.env 文件，请根据需要修改配置"
fi

# 前端环境配置
if [ ! -f "frontend/.env" ]; then
    cp frontend/.env.example frontend/.env
    echo "✅ 已创建 frontend/.env 文件，请根据需要修改配置"
fi

# 安装后端依赖
echo "📦 安装后端依赖..."
cd backend
if [ ! -d "node_modules" ]; then
    npm install
fi

# 启动后端服务
echo "🔧 启动后端服务..."
npm run dev &
BACKEND_PID=$!

# 等待后端启动
sleep 5

# 安装前端依赖
echo "📦 安装前端依赖..."
cd ../frontend
if [ ! -d "node_modules" ]; then
    npm install
fi

# 启动前端服务
echo "🎨 启动前端服务..."
npm start &
FRONTEND_PID=$!

echo "✅ 启动完成！"
echo ""
echo "🌐 前端地址: http://localhost:3000"
echo "🔗 后端地址: http://localhost:3001"
echo "📚 API文档: http://localhost:3001/health"
echo ""
echo "按 Ctrl+C 停止服务"

# 等待用户中断
wait

# 清理进程
echo "🛑 正在停止服务..."
kill $BACKEND_PID 2>/dev/null
kill $FRONTEND_PID 2>/dev/null
echo "✅ 服务已停止"
