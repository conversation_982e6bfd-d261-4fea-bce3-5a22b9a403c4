const asyncHandler = require('express-async-handler');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const Order = require('../models/orderModel');

// @desc    Process payment with Stripe
// @route   POST /api/payments/stripe
// @access  Private
const processStripePayment = asyncHandler(async (req, res) => {
  const { orderId, paymentMethodId } = req.body;

  const order = await Order.findById(orderId);

  if (!order) {
    res.status(404);
    throw new Error('Order not found');
  }

  try {
    // Create payment intent with Stripe
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(order.totalPrice * 100), // Stripe requires amount in cents
      currency: 'usd',
      payment_method: paymentMethodId,
      confirm: true,
      description: `Order ${order._id}`,
      metadata: {
        orderId: order._id.toString(),
        userId: req.user._id.toString(),
      },
    });

    // Update order payment status
    order.isPaid = true;
    order.paidAt = Date.now();
    order.paymentResult = {
      id: paymentIntent.id,
      status: paymentIntent.status,
      update_time: new Date().toISOString(),
      email_address: req.user.email,
    };

    const updatedOrder = await order.save();

    res.json({
      success: true,
      message: 'Payment successful',
      order: updatedOrder,
    });
  } catch (error) {
    res.status(400);
    throw new Error(`Payment failed: ${error.message}`);
  }
});

// @desc    Get Stripe payment config
// @route   GET /api/payments/stripe/config
// @access  Public
const getStripeConfig = asyncHandler(async (req, res) => {
  res.json({
    publishableKey: process.env.STRIPE_PUBLISHABLE_KEY || 'pk_test_yourpublishablekey',
  });
});

module.exports = {
  processStripePayment,
  getStripeConfig,
};