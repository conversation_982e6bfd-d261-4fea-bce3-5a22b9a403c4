import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import {
  Layout,
  Menu,
  Button,
  Input,
  Badge,
  Avatar,
  Dropdown,
  Space,
  Drawer,
} from 'antd';
import {
  ShoppingCartOutlined,
  UserOutlined,
  SearchOutlined,
  MenuOutlined,
  HomeOutlined,
  AppstoreOutlined,
  LogoutOutlined,
  SettingOutlined,
  ShoppingOutlined,
} from '@ant-design/icons';
import { logout } from '../../redux/slices/authSlice';
import { toggleMobileMenu, setSearchVisible } from '../../redux/slices/uiSlice';

const { Header } = Layout;
const { Search } = Input;

const AppHeader = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { userInfo } = useSelector((state) => state.auth);
  const { cartItems } = useSelector((state) => state.cart);
  const { mobileMenuOpen, searchVisible } = useSelector((state) => state.ui);
  const [searchValue, setSearchValue] = useState('');

  // 计算购物车商品总数
  const cartItemCount = cartItems.reduce((acc, item) => acc + item.quantity, 0);

  // 处理搜索
  const handleSearch = (value) => {
    if (value.trim()) {
      navigate(`/products?keyword=${encodeURIComponent(value.trim())}`);
      dispatch(setSearchVisible(false));
    }
  };

  // 处理登出
  const handleLogout = () => {
    dispatch(logout());
    navigate('/');
  };

  // 用户下拉菜单
  const userMenu = (
    <Menu>
      <Menu.Item key="profile" icon={<UserOutlined />}>
        <Link to="/profile">个人中心</Link>
      </Menu.Item>
      <Menu.Item key="orders" icon={<ShoppingOutlined />}>
        <Link to="/orders">我的订单</Link>
      </Menu.Item>
      <Menu.Item key="addresses" icon={<HomeOutlined />}>
        <Link to="/addresses">地址管理</Link>
      </Menu.Item>
      {userInfo && userInfo.role === 'admin' && (
        <Menu.Item key="admin" icon={<SettingOutlined />}>
          <Link to="/admin/dashboard">管理后台</Link>
        </Menu.Item>
      )}
      <Menu.Divider />
      <Menu.Item key="logout" icon={<LogoutOutlined />} onClick={handleLogout}>
        退出登录
      </Menu.Item>
    </Menu>
  );

  // 移动端菜单
  const mobileMenu = (
    <Drawer
      title="菜单"
      placement="left"
      onClose={() => dispatch(toggleMobileMenu())}
      open={mobileMenuOpen}
    >
      <Menu mode="vertical">
        <Menu.Item key="home" icon={<HomeOutlined />}>
          <Link to="/">首页</Link>
        </Menu.Item>
        <Menu.Item key="products" icon={<AppstoreOutlined />}>
          <Link to="/products">商品</Link>
        </Menu.Item>
        <Menu.Item key="cart" icon={<ShoppingCartOutlined />}>
          <Link to="/cart">购物车</Link>
        </Menu.Item>
        {userInfo ? (
          <>
            <Menu.Item key="profile" icon={<UserOutlined />}>
              <Link to="/profile">个人中心</Link>
            </Menu.Item>
            <Menu.Item key="orders" icon={<ShoppingOutlined />}>
              <Link to="/orders">我的订单</Link>
            </Menu.Item>
            <Menu.Item key="addresses" icon={<HomeOutlined />}>
              <Link to="/addresses">地址管理</Link>
            </Menu.Item>
            {userInfo.role === 'admin' && (
              <Menu.Item key="admin" icon={<SettingOutlined />}>
                <Link to="/admin/dashboard">管理后台</Link>
              </Menu.Item>
            )}
            <Menu.Item key="logout" icon={<LogoutOutlined />} onClick={handleLogout}>
              退出登录
            </Menu.Item>
          </>
        ) : (
          <>
            <Menu.Item key="login">
              <Link to="/login">登录</Link>
            </Menu.Item>
            <Menu.Item key="register">
              <Link to="/register">注册</Link>
            </Menu.Item>
          </>
        )}
      </Menu>
    </Drawer>
  );

  // 搜索抽屉
  const searchDrawer = (
    <Drawer
      title="搜索"
      placement="top"
      onClose={() => dispatch(setSearchVisible(false))}
      open={searchVisible}
      height={120}
    >
      <Search
        placeholder="搜索商品"
        enterButton="搜索"
        size="large"
        value={searchValue}
        onChange={(e) => setSearchValue(e.target.value)}
        onSearch={handleSearch}
      />
    </Drawer>
  );

  return (
    <>
      <Header className="app-header">
        <div className="header-left">
          <Button
            type="text"
            icon={<MenuOutlined />}
            onClick={() => dispatch(toggleMobileMenu())}
            className="menu-button"
          />
          <div className="logo">
            <Link to="/">电商网站</Link>
          </div>
          <div className="desktop-menu">
            <Menu mode="horizontal" className="main-menu">
              <Menu.Item key="home">
                <Link to="/">首页</Link>
              </Menu.Item>
              <Menu.Item key="products">
                <Link to="/products">商品</Link>
              </Menu.Item>
            </Menu>
          </div>
        </div>
        <div className="header-right">
          <Button
            type="text"
            icon={<SearchOutlined />}
            onClick={() => dispatch(setSearchVisible(true))}
            className="search-button"
          />
          <Link to="/cart">
            <Badge count={cartItemCount} showZero>
              <Button type="text" icon={<ShoppingCartOutlined />} />
            </Badge>
          </Link>
          {userInfo ? (
            <Dropdown overlay={userMenu} trigger={['click']}>
              <Space className="user-dropdown">
                <Avatar
                  src={userInfo.avatar}
                  icon={!userInfo.avatar && <UserOutlined />}
                />
                <span className="username">{userInfo.name}</span>
              </Space>
            </Dropdown>
          ) : (
            <Space>
              <Button type="link">
                <Link to="/login">登录</Link>
              </Button>
              <Button type="primary">
                <Link to="/register">注册</Link>
              </Button>
            </Space>
          )}
        </div>
      </Header>
      {mobileMenu}
      {searchDrawer}
    </>
  );
};

export default AppHeader;