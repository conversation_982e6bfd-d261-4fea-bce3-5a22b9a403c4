import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { message } from 'antd';
import userService from '../../services/userService';
import { Address } from './authSlice';

interface UserState {
  addresses: Address[];
  loading: boolean;
  error: string | null;
}

const initialState: UserState = {
  addresses: [],
  loading: false,
  error: null,
};

// 异步actions
export const fetchAddresses = createAsyncThunk(
  'user/fetchAddresses',
  async (_, { rejectWithValue }) => {
    try {
      const response = await userService.getAddresses();
      return response.addresses;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const addAddress = createAsyncThunk(
  'user/addAddress',
  async (addressData: Omit<Address, '_id'>, { rejectWithValue }) => {
    try {
      const response = await userService.addAddress(addressData);
      message.success('地址添加成功');
      return response.addresses;
    } catch (error: any) {
      message.error(error.message || '地址添加失败');
      return rejectWithValue(error.message);
    }
  }
);

export const updateAddress = createAsyncThunk(
  'user/updateAddress',
  async ({ addressId, addressData }: { addressId: string; addressData: Omit<Address, '_id'> }, { rejectWithValue }) => {
    try {
      const response = await userService.updateAddress(addressId, addressData);
      message.success('地址更新成功');
      return response.addresses;
    } catch (error: any) {
      message.error(error.message || '地址更新失败');
      return rejectWithValue(error.message);
    }
  }
);

export const deleteAddress = createAsyncThunk(
  'user/deleteAddress',
  async (addressId: string, { rejectWithValue }) => {
    try {
      const response = await userService.deleteAddress(addressId);
      message.success('地址删除成功');
      return response.addresses;
    } catch (error: any) {
      message.error(error.message || '地址删除失败');
      return rejectWithValue(error.message);
    }
  }
);

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setAddresses: (state, action: PayloadAction<Address[]>) => {
      state.addresses = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // 获取地址列表
      .addCase(fetchAddresses.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAddresses.fulfilled, (state, action) => {
        state.loading = false;
        state.addresses = action.payload;
        state.error = null;
      })
      .addCase(fetchAddresses.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // 添加地址
      .addCase(addAddress.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(addAddress.fulfilled, (state, action) => {
        state.loading = false;
        state.addresses = action.payload;
        state.error = null;
      })
      .addCase(addAddress.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // 更新地址
      .addCase(updateAddress.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateAddress.fulfilled, (state, action) => {
        state.loading = false;
        state.addresses = action.payload;
        state.error = null;
      })
      .addCase(updateAddress.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // 删除地址
      .addCase(deleteAddress.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteAddress.fulfilled, (state, action) => {
        state.loading = false;
        state.addresses = action.payload;
        state.error = null;
      })
      .addCase(deleteAddress.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearError, setAddresses } = userSlice.actions;
export default userSlice.reducer;
