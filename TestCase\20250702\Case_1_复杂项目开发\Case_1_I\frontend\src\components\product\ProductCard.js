import React from 'react';
import { Card, Badge, Typography, Button } from 'antd';
import { ShoppingCartOutlined, HeartOutlined, HeartFilled } from '@ant-design/icons';
import { Link } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import Price from '../common/Price';
import Rating from '../common/Rating';
import { addToCart } from '../../redux/slices/cartSlice';

const { Meta } = Card;
const { Text } = Typography;

const ProductCard = ({ product, onAddToWishlist, inWishlist = false }) => {
  const dispatch = useDispatch();
  const { userInfo } = useSelector((state) => state.auth);

  // 处理添加到购物车
  const handleAddToCart = () => {
    dispatch(addToCart({ productId: product._id, quantity: 1 }));
  };

  // 处理添加到收藏夹
  const handleAddToWishlist = () => {
    if (userInfo) {
      onAddToWishlist && onAddToWishlist(product._id);
    } else {
      // 如果未登录，跳转到登录页面
      window.location.href = '/login?redirect=/wishlist';
    }
  };

  // 计算折扣
  const discount = product.originalPrice > product.price
    ? `${Math.round((1 - product.price / product.originalPrice) * 100)}% OFF`
    : null;

  return (
    <Badge.Ribbon text={discount} color="red" style={{ display: discount ? 'block' : 'none' }}>
      <Card
        hoverable
        cover={
          <Link to={`/product/${product._id}`}>
            <img
              alt={product.name}
              src={product.images && product.images.length > 0 ? product.images[0] : '/placeholder.png'}
              style={{ height: '200px', objectFit: 'cover' }}
            />
          </Link>
        }
        actions={[
          <Button
            key="addToCart"
            type="link"
            icon={<ShoppingCartOutlined />}
            onClick={handleAddToCart}
          >
            加入购物车
          </Button>,
          inWishlist ? (
            <Button
              key="removeFromWishlist"
              type="link"
              icon={<HeartFilled style={{ color: '#ff4d4f' }} />}
              onClick={handleAddToWishlist}
            >
              已收藏
            </Button>
          ) : (
            <Button
              key="addToWishlist"
              type="link"
              icon={<HeartOutlined />}
              onClick={handleAddToWishlist}
            >
              收藏
            </Button>
          ),
        ]}
      >
        <Link to={`/product/${product._id}`}>
          <Meta
            title={product.name}
            description={
              <>
                <div className="product-price">
                  <Price
                    price={product.price}
                    originalPrice={product.originalPrice}
                    discount={discount}
                  />
                </div>
                <div className="product-rating">
                  <Rating value={product.rating} disabled />
                  <Text type="secondary" style={{ marginLeft: '8px' }}>
                    ({product.numReviews})
                  </Text>
                </div>
              </>
            }
          />
        </Link>
      </Card>
    </Badge.Ribbon>
  );
};

export default ProductCard;