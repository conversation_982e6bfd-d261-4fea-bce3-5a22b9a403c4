import React from 'react';
import { Layout, Row, Col, Typography, Divider } from 'antd';
import { Link } from 'react-router-dom';

const { Footer } = Layout;
const { Title, Paragraph } = Typography;

const FooterComponent = () => {
  return (
    <Footer style={{ background: '#f0f2f5', padding: '40px 0 24px' }}>
      <div className="container">
        <Row gutter={[24, 24]}>
          <Col xs={24} sm={12} md={8} lg={6}>
            <Title level={4}>关于我们</Title>
            <Paragraph>
              电商网站是一个全功能的在线购物平台，提供丰富的商品选择和便捷的购物体验。
            </Paragraph>
          </Col>
          
          <Col xs={24} sm={12} md={8} lg={6}>
            <Title level={4}>购物指南</Title>
            <ul style={{ listStyle: 'none', padding: 0 }}>
              <li><Link to="/">注册新用户</Link></li>
              <li><Link to="/">购物流程</Link></li>
              <li><Link to="/">常见问题</Link></li>
              <li><Link to="/">支付方式</Link></li>
            </ul>
          </Col>
          
          <Col xs={24} sm={12} md={8} lg={6}>
            <Title level={4}>售后服务</Title>
            <ul style={{ listStyle: 'none', padding: 0 }}>
              <li><Link to="/">退换货政策</Link></li>
              <li><Link to="/">退换货流程</Link></li>
              <li><Link to="/">投诉建议</Link></li>
            </ul>
          </Col>
          
          <Col xs={24} sm={12} md={8} lg={6}>
            <Title level={4}>联系我们</Title>
            <Paragraph>
              客服电话：400-123-4567<br />
              服务时间：09:00-22:00<br />
              电子邮箱：<EMAIL>
            </Paragraph>
          </Col>
        </Row>
        
        <Divider style={{ margin: '24px 0' }} />
        
        <div className="text-center">
          <p>© 2023 电商网站 版权所有</p>
          <p>
            <Link to="/">隐私政策</Link> | 
            <Link to="/">用户协议</Link> | 
            <Link to="/">网站地图</Link>
          </p>
        </div>
      </div>
    </Footer>
  );
};

export default FooterComponent; 