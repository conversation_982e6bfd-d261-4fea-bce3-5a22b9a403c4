import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { message } from 'antd';
import orderService from '../../services/orderService';

export interface OrderItem {
  product: string;
  sku: string;
  name: string;
  image: string;
  specs: Array<{ name: string; value: string }>;
  price: number;
  quantity: number;
  subtotal: number;
}

export interface ShippingAddress {
  name: string;
  phone: string;
  province: string;
  city: string;
  district: string;
  detail: string;
}

export interface Order {
  _id: string;
  orderNo: string;
  user: {
    _id: string;
    username: string;
    email: string;
  };
  items: OrderItem[];
  shippingAddress: ShippingAddress;
  totalAmount: number;
  shippingFee: number;
  discountAmount: number;
  finalAmount: number;
  status: 'pending' | 'paid' | 'shipped' | 'delivered' | 'completed' | 'cancelled' | 'refunded';
  paymentMethod: 'alipay' | 'wechat' | 'bank_card';
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  paymentId?: string;
  paidAt?: string;
  shippedAt?: string;
  deliveredAt?: string;
  completedAt?: string;
  cancelledAt?: string;
  refundedAt?: string;
  trackingNumber?: string;
  courier?: string;
  remark?: string;
  createdAt: string;
  updatedAt: string;
}

interface OrderState {
  orders: Order[];
  currentOrder: Order | null;
  loading: boolean;
  error: string | null;
  pagination: {
    current: number;
    pageSize: number;
    total: number;
    pages: number;
  };
}

const initialState: OrderState = {
  orders: [],
  currentOrder: null,
  loading: false,
  error: null,
  pagination: {
    current: 1,
    pageSize: 10,
    total: 0,
    pages: 0,
  },
};

// 异步actions
export const createOrder = createAsyncThunk(
  'order/createOrder',
  async (orderData: any, { rejectWithValue }) => {
    try {
      const response = await orderService.createOrder(orderData);
      message.success('订单创建成功');
      return response.order;
    } catch (error: any) {
      message.error(error.message || '订单创建失败');
      return rejectWithValue(error.message);
    }
  }
);

export const fetchOrders = createAsyncThunk(
  'order/fetchOrders',
  async (params: any, { rejectWithValue }) => {
    try {
      const response = await orderService.getOrders(params);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const fetchOrderByNo = createAsyncThunk(
  'order/fetchOrderByNo',
  async (orderNo: string, { rejectWithValue }) => {
    try {
      const response = await orderService.getOrderByNo(orderNo);
      return response.order;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const cancelOrder = createAsyncThunk(
  'order/cancelOrder',
  async (orderNo: string, { rejectWithValue }) => {
    try {
      const response = await orderService.cancelOrder(orderNo);
      message.success('订单取消成功');
      return response.order;
    } catch (error: any) {
      message.error(error.message || '订单取消失败');
      return rejectWithValue(error.message);
    }
  }
);

export const confirmOrder = createAsyncThunk(
  'order/confirmOrder',
  async (orderNo: string, { rejectWithValue }) => {
    try {
      const response = await orderService.confirmOrder(orderNo);
      message.success('确认收货成功');
      return response.order;
    } catch (error: any) {
      message.error(error.message || '确认收货失败');
      return rejectWithValue(error.message);
    }
  }
);

const orderSlice = createSlice({
  name: 'order',
  initialState,
  reducers: {
    clearCurrentOrder: (state) => {
      state.currentOrder = null;
    },
    clearError: (state) => {
      state.error = null;
    },
    updateOrderStatus: (state, action: PayloadAction<{ orderNo: string; status: string; paymentStatus?: string }>) => {
      const { orderNo, status, paymentStatus } = action.payload;
      
      // 更新订单列表中的订单状态
      const orderIndex = state.orders.findIndex(order => order.orderNo === orderNo);
      if (orderIndex !== -1) {
        state.orders[orderIndex].status = status as any;
        if (paymentStatus) {
          state.orders[orderIndex].paymentStatus = paymentStatus as any;
        }
      }
      
      // 更新当前订单状态
      if (state.currentOrder && state.currentOrder.orderNo === orderNo) {
        state.currentOrder.status = status as any;
        if (paymentStatus) {
          state.currentOrder.paymentStatus = paymentStatus as any;
        }
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // 创建订单
      .addCase(createOrder.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createOrder.fulfilled, (state, action) => {
        state.loading = false;
        state.currentOrder = action.payload;
        state.error = null;
      })
      .addCase(createOrder.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // 获取订单列表
      .addCase(fetchOrders.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchOrders.fulfilled, (state, action) => {
        state.loading = false;
        state.orders = action.payload.orders;
        state.pagination = action.payload.pagination;
        state.error = null;
      })
      .addCase(fetchOrders.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // 获取订单详情
      .addCase(fetchOrderByNo.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchOrderByNo.fulfilled, (state, action) => {
        state.loading = false;
        state.currentOrder = action.payload;
        state.error = null;
      })
      .addCase(fetchOrderByNo.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // 取消订单
      .addCase(cancelOrder.fulfilled, (state, action) => {
        const updatedOrder = action.payload;
        
        // 更新订单列表
        const orderIndex = state.orders.findIndex(order => order.orderNo === updatedOrder.orderNo);
        if (orderIndex !== -1) {
          state.orders[orderIndex] = updatedOrder;
        }
        
        // 更新当前订单
        if (state.currentOrder && state.currentOrder.orderNo === updatedOrder.orderNo) {
          state.currentOrder = updatedOrder;
        }
      })
      // 确认收货
      .addCase(confirmOrder.fulfilled, (state, action) => {
        const updatedOrder = action.payload;
        
        // 更新订单列表
        const orderIndex = state.orders.findIndex(order => order.orderNo === updatedOrder.orderNo);
        if (orderIndex !== -1) {
          state.orders[orderIndex] = updatedOrder;
        }
        
        // 更新当前订单
        if (state.currentOrder && state.currentOrder.orderNo === updatedOrder.orderNo) {
          state.currentOrder = updatedOrder;
        }
      });
  },
});

export const { clearCurrentOrder, clearError, updateOrderStatus } = orderSlice.actions;
export default orderSlice.reducer;
