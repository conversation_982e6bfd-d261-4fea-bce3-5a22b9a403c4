import React from 'react';
import { Card, Typography, Steps, Divider, Row, Col, List, Image, Tag, Button, Popconfirm } from 'antd';
import { Link } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { cancelOrder } from '../../redux/slices/orderSlice';
import { formatPrice, formatDate, formatOrderStatus, formatPaymentStatus } from '../../utils/formatUtils';

const { Text, Title, Paragraph } = Typography;
const { Step } = Steps;

const OrderDetail = ({ order }) => {
  const dispatch = useDispatch();
  const { loading } = useSelector((state) => state.order);

  // 获取订单状态步骤
  const getOrderStep = (status) => {
    switch (status) {
      case 'pending':
        return 0;
      case 'processing':
        return 1;
      case 'shipped':
        return 2;
      case 'delivered':
        return 3;
      case 'cancelled':
        return 4;
      default:
        return 0;
    }
  };

  // 获取订单状态标签颜色
  const getStatusTagColor = (status) => {
    switch (status) {
      case 'pending':
        return 'orange';
      case 'processing':
        return 'blue';
      case 'shipped':
        return 'cyan';
      case 'delivered':
        return 'green';
      case 'cancelled':
        return 'red';
      default:
        return 'default';
    }
  };

  // 获取支付状态标签颜色
  const getPaymentStatusTagColor = (status) => {
    switch (status) {
      case 'pending':
        return 'orange';
      case 'completed':
        return 'green';
      case 'failed':
        return 'red';
      case 'refunded':
        return 'purple';
      default:
        return 'default';
    }
  };

  // 处理取消订单
  const handleCancelOrder = () => {
    dispatch(cancelOrder(order._id));
  };

  // 判断订单是否可以取消
  const canCancelOrder = () => {
    return order.status === 'pending' || order.status === 'processing';
  };

  return (
    <div className="order-detail">
      {/* 订单状态 */}
      <Card className="order-status" style={{ marginBottom: '24px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
          <Title level={4}>订单状态</Title>
          <Tag color={getStatusTagColor(order.status)}>{formatOrderStatus(order.status)}</Tag>
        </div>

        <Steps current={getOrderStep(order.status)} status={order.status === 'cancelled' ? 'error' : 'process'}>
          <Step title="待处理" description="订单已提交" />
          <Step title="处理中" description="商家已接单" />
          <Step title="已发货" description="商品已发出" />
          <Step title="已送达" description="订单已完成" />
          {order.status === 'cancelled' && <Step title="已取消" description="订单已取消" status="error" />}
        </Steps>

        {canCancelOrder() && (
          <div style={{ marginTop: '16px', textAlign: 'right' }}>
            <Popconfirm
              title="确定要取消此订单吗？"
              onConfirm={handleCancelOrder}
              okText="确定"
              cancelText="取消"
            >
              <Button danger loading={loading}>取消订单</Button>
            </Popconfirm>
          </div>
        )}
      </Card>

      {/* 订单信息 */}
      <Card className="order-info" title="订单信息" style={{ marginBottom: '24px' }}>
        <Row gutter={[16, 16]}>
          <Col span={8}>
            <Text type="secondary">订单编号：</Text>
            <Text strong>{order._id}</Text>
          </Col>
          <Col span={8}>
            <Text type="secondary">下单时间：</Text>
            <Text>{formatDate(order.createdAt)}</Text>
          </Col>
          <Col span={8}>
            <Text type="secondary">支付方式：</Text>
            <Text>{order.paymentMethod}</Text>
          </Col>
          <Col span={8}>
            <Text type="secondary">支付状态：</Text>
            <Tag color={getPaymentStatusTagColor(order.paymentStatus)}>
              {formatPaymentStatus(order.paymentStatus)}
            </Tag>
          </Col>
          {order.paidAt && (
            <Col span={8}>
              <Text type="secondary">支付时间：</Text>
              <Text>{formatDate(order.paidAt)}</Text>
            </Col>
          )}
          {order.deliveredAt && (
            <Col span={8}>
              <Text type="secondary">送达时间：</Text>
              <Text>{formatDate(order.deliveredAt)}</Text>
            </Col>
          )}
        </Row>
      </Card>

      {/* 收货信息 */}
      <Card className="shipping-info" title="收货信息" style={{ marginBottom: '24px' }}>
        <Row gutter={[16, 16]}>
          <Col span={8}>
            <Text type="secondary">收货人：</Text>
            <Text>{order.shippingAddress.name}</Text>
          </Col>
          <Col span={8}>
            <Text type="secondary">联系电话：</Text>
            <Text>{order.shippingAddress.phone}</Text>
          </Col>
          <Col span={24}>
            <Text type="secondary">收货地址：</Text>
            <Text>
              {order.shippingAddress.province} {order.shippingAddress.city} {order.shippingAddress.district} {order.shippingAddress.address}
            </Text>
          </Col>
          {order.trackingNumber && (
            <Col span={24}>
              <Text type="secondary">物流单号：</Text>
              <Text copyable>{order.trackingNumber}</Text>
            </Col>
          )}
        </Row>
      </Card>

      {/* 商品信息 */}
      <Card className="order-items" title="商品信息" style={{ marginBottom: '24px' }}>
        <List
          itemLayout="horizontal"
          dataSource={order.orderItems}
          renderItem={(item) => (
            <List.Item>
              <div style={{ display: 'flex', width: '100%' }}>
                <div style={{ marginRight: '16px' }}>
                  <Link to={`/product/${item.product._id}`}>
                    <Image
                      src={item.product.images && item.product.images.length > 0 ? item.product.images[0] : '/placeholder.png'}
                      alt={item.product.name}
                      width={80}
                      height={80}
                      style={{ objectFit: 'cover' }}
                      preview={false}
                    />
                  </Link>
                </div>
                <div style={{ flex: 1 }}>
                  <Link to={`/product/${item.product._id}`}>
                    <Text strong>{item.product.name}</Text>
                  </Link>
                  <div style={{ color: '#888', fontSize: '12px', marginTop: '4px' }}>
                    {item.product.description && item.product.description.substring(0, 50)}...
                  </div>
                </div>
                <div style={{ width: '80px', textAlign: 'center' }}>
                  <Text>¥{item.price.toFixed(2)}</Text>
                </div>
                <div style={{ width: '80px', textAlign: 'center' }}>
                  <Text>x{item.quantity}</Text>
                </div>
                <div style={{ width: '100px', textAlign: 'right' }}>
                  <Text strong>¥{(item.price * item.quantity).toFixed(2)}</Text>
                </div>
              </div>
            </List.Item>
          )}
        />

        <Divider style={{ margin: '16px 0' }} />

        <div style={{ textAlign: 'right' }}>
          <div style={{ marginBottom: '8px' }}>
            <Text style={{ marginRight: '8px' }}>商品总价:</Text>
            <Text>¥{order.itemsPrice.toFixed(2)}</Text>
          </div>
          <div style={{ marginBottom: '8px' }}>
            <Text style={{ marginRight: '8px' }}>运费:</Text>
            <Text>¥{order.shippingPrice.toFixed(2)}</Text>
          </div>
          <div style={{ marginBottom: '8px' }}>
            <Text style={{ marginRight: '8px' }}>税费:</Text>
            <Text>¥{order.taxPrice.toFixed(2)}</Text>
          </div>
          <div>
            <Text style={{ marginRight: '8px', fontSize: '16px' }} strong>实付金额:</Text>
            <Text style={{ fontSize: '16px', color: '#f5222d' }} strong>¥{order.totalPrice.toFixed(2)}</Text>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default OrderDetail;