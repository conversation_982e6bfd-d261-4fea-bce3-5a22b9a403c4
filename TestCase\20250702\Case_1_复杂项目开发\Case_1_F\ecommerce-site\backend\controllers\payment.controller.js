const Order = require('../models/order.model');

// @desc    创建支付会话
// @route   POST /api/payment/create-payment-session
// @access  私有
exports.createPaymentSession = async (req, res) => {
  try {
    const { orderId } = req.body;
    
    if (!orderId) {
      return res.status(400).json({
        success: false,
        message: '请提供订单ID'
      });
    }
    
    const order = await Order.findById(orderId);
    
    if (!order) {
      return res.status(404).json({
        success: false,
        message: '未找到该订单'
      });
    }
    
    // 验证用户只能支付自己的订单
    if (order.user.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: '无权限支付此订单'
      });
    }
    
    // 检查订单是否已支付
    if (order.isPaid) {
      return res.status(400).json({
        success: false,
        message: '订单已支付'
      });
    }
    
    // 检查订单状态
    if (order.status === '已取消') {
      return res.status(400).json({
        success: false,
        message: '订单已取消，无法支付'
      });
    }
    
    // 在实际项目中，这里会调用支付网关的API创建支付会话
    // 这里模拟创建支付会话，返回支付链接
    const paymentSession = {
      id: `ps_${Date.now()}`,
      orderId: order._id,
      amount: order.totalPrice,
      currency: 'CNY',
      status: 'pending',
      paymentUrl: `https://example.com/pay/${order._id}`,
      expiresAt: new Date(Date.now() + 30 * 60 * 1000) // 30分钟后过期
    };
    
    res.status(200).json({
      success: true,
      message: '支付会话创建成功',
      data: paymentSession
    });
  } catch (err) {
    console.error('创建支付会话失败:', err);
    res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
  }
};

// @desc    模拟支付结果
// @route   POST /api/payment/simulate-payment
// @access  私有
exports.simulatePayment = async (req, res) => {
  try {
    const { orderId, paymentStatus } = req.body;
    
    if (!orderId || !paymentStatus) {
      return res.status(400).json({
        success: false,
        message: '请提供订单ID和支付状态'
      });
    }
    
    const order = await Order.findById(orderId);
    
    if (!order) {
      return res.status(404).json({
        success: false,
        message: '未找到该订单'
      });
    }
    
    // 验证用户只能支付自己的订单
    if (order.user.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: '无权限支付此订单'
      });
    }
    
    // 检查订单是否已支付
    if (order.isPaid) {
      return res.status(400).json({
        success: false,
        message: '订单已支付'
      });
    }
    
    // 检查支付状态
    if (paymentStatus === 'success') {
      // 更新订单支付状态
      order.isPaid = true;
      order.paidAt = Date.now();
      order.status = '待发货';
      order.paymentResult = {
        id: `pay_${Date.now()}`,
        status: 'success',
        updateTime: new Date().toISOString(),
        email: req.user.email
      };
      
      await order.save();
      
      return res.status(200).json({
        success: true,
        message: '支付成功',
        data: {
          orderId: order._id,
          paymentStatus: 'success',
          paymentTime: order.paidAt
        }
      });
    } else if (paymentStatus === 'failed') {
      return res.status(200).json({
        success: false,
        message: '支付失败',
        data: {
          orderId: order._id,
          paymentStatus: 'failed'
        }
      });
    } else {
      return res.status(400).json({
        success: false,
        message: '无效的支付状态'
      });
    }
  } catch (err) {
    console.error('模拟支付失败:', err);
    res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
  }
};

// @desc    获取支付方法列表
// @route   GET /api/payment/methods
// @access  公开
exports.getPaymentMethods = (req, res) => {
  // 返回支持的支付方式
  const paymentMethods = [
    { id: '支付宝', name: '支付宝', description: '使用支付宝进行支付' },
    { id: '微信支付', name: '微信支付', description: '使用微信支付进行支付' },
    { id: '银行卡', name: '银行卡', description: '使用银行卡进行支付' },
    { id: '货到付款', name: '货到付款', description: '货物送达后进行支付' }
  ];
  
  res.status(200).json({
    success: true,
    data: paymentMethods
  });
}; 