const mongoose = require('mongoose');

const OrderItemSchema = new mongoose.Schema({
  product: {
    type: mongoose.Schema.ObjectId,
    ref: 'Product',
    required: true
  },
  name: {
    type: String,
    required: true
  },
  quantity: {
    type: Number,
    required: true,
    min: [1, '数量必须至少为1']
  },
  price: {
    type: Number,
    required: true
  },
  image: {
    type: String
  }
});

const OrderSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  orderItems: [OrderItemSchema],
  shippingAddress: {
    fullName: String,
    address: String,
    city: String,
    postalCode: String,
    country: String,
    phoneNumber: String
  },
  paymentMethod: {
    type: String,
    required: [true, '请选择支付方式'],
    enum: ['支付宝', '微信支付', '银行卡', '货到付款']
  },
  paymentResult: {
    id: String,
    status: String,
    updateTime: String,
    email: String
  },
  itemsPrice: {
    type: Number,
    required: true,
    default: 0
  },
  shippingPrice: {
    type: Number,
    required: true,
    default: 0
  },
  taxPrice: {
    type: Number,
    required: true,
    default: 0
  },
  totalPrice: {
    type: Number,
    required: true,
    default: 0
  },
  isPaid: {
    type: Boolean,
    default: false
  },
  paidAt: {
    type: Date
  },
  isDelivered: {
    type: Boolean,
    default: false
  },
  deliveredAt: {
    type: Date
  },
  status: {
    type: String,
    required: true,
    enum: ['待付款', '待发货', '已发货', '已完成', '已取消'],
    default: '待付款'
  },
  trackingNumber: {
    type: String
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// 前置钩子 - 计算总价格
OrderSchema.pre('save', function(next) {
  if (this.isNew || this.isModified('orderItems')) {
    // 计算商品总价
    this.itemsPrice = this.orderItems.reduce((total, item) => {
      return total + (item.price * item.quantity);
    }, 0);
    
    // 计算总价 = 商品总价 + 运费 + 税费
    this.totalPrice = this.itemsPrice + this.shippingPrice + this.taxPrice;
  }
  
  next();
});

// 方法 - 更新订单状态
OrderSchema.methods.updateStatus = function(newStatus) {
  this.status = newStatus;
  
  if (newStatus === '已发货') {
    this.isDelivered = true;
    this.deliveredAt = Date.now();
  } else if (newStatus === '已完成') {
    this.isDelivered = true;
    if (!this.deliveredAt) {
      this.deliveredAt = Date.now();
    }
  }
  
  return this.save();
};

module.exports = mongoose.model('Order', OrderSchema); 