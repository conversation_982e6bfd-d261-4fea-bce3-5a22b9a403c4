import React, { useState, useEffect, useContext } from 'react';
import { usePara<PERSON>, Link, useNavigate } from 'react-router-dom';
import {
  Row,
  Col,
  Image,
  Typography,
  Button,
  InputNumber,
  Card,
  Tabs,
  Tag,
  Breadcrumb,
  Rate,
  Divider,
  message,
  Spin,
  Empty
} from 'antd';
import {
  ShoppingCartOutlined,
  HeartOutlined,
  ShareAltOutlined,
  CheckCircleFilled,
  CloseCircleFilled
} from '@ant-design/icons';
import axios from 'axios';
import { CartContext } from '../context/CartContext';

const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;

const ProductDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { addToCart } = useContext(CartContext);
  
  const [product, setProduct] = useState(null);
  const [relatedProducts, setRelatedProducts] = useState([]);
  const [quantity, setQuantity] = useState(1);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // 获取商品详情
  useEffect(() => {
    const fetchProductDetails = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await axios.get(`/products/${id}`);
        
        if (response.data.success) {
          setProduct(response.data.data);
          setQuantity(1); // 重置数量
          
          // 获取相关商品 (同类别的其他商品)
          try {
            const relatedResponse = await axios.get('/products', {
              params: {
                category: response.data.data.category._id,
                limit: 4,
                exclude: id
              }
            });
            
            if (relatedResponse.data.success) {
              setRelatedProducts(relatedResponse.data.data);
            }
          } catch (err) {
            console.error('获取相关商品失败:', err);
          }
        }
      } catch (err) {
        console.error('获取商品详情失败:', err);
        setError('获取商品详情失败，请稍后再试');
      } finally {
        setLoading(false);
      }
    };
    
    fetchProductDetails();
  }, [id]);
  
  // 处理数量变化
  const handleQuantityChange = (value) => {
    // 确保不小于1且不大于库存
    if (value < 1) {
      setQuantity(1);
    } else if (product && value > product.stock) {
      setQuantity(product.stock);
      message.warning(`该商品最多只能购买 ${product.stock} 件`);
    } else {
      setQuantity(value);
    }
  };
  
  // 处理添加到购物车
  const handleAddToCart = async () => {
    if (product.stock <= 0) {
      message.error('该商品已售罄，无法加入购物车');
      return;
    }
    
    const success = await addToCart(id, quantity);
    if (success) {
      message.success(`已将 ${quantity} 件 ${product.name} 加入购物车`);
    }
  };
  
  // 处理立即购买
  const handleBuyNow = async () => {
    if (product.stock <= 0) {
      message.error('该商品已售罄，无法购买');
      return;
    }
    
    const success = await addToCart(id, quantity);
    if (success) {
      navigate('/cart');
    }
  };
  
  // 计算折扣百分比
  const getDiscountPercent = () => {
    if (product.originalPrice && product.originalPrice > product.price) {
      return Math.round((1 - product.price / product.originalPrice) * 100);
    }
    return 0;
  };
  
  if (loading) {
    return (
      <div className="text-center" style={{ padding: '100px 0' }}>
        <Spin size="large" tip="加载中..." />
      </div>
    );
  }
  
  if (error || !product) {
    return (
      <div className="text-center" style={{ padding: '100px 0' }}>
        <Empty
          description={error || "未找到商品"}
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
        <Button type="primary" onClick={() => navigate('/products')}>
          返回商品列表
        </Button>
      </div>
    );
  }
  
  const discountPercent = getDiscountPercent();
  
  return (
    <div>
      {/* 面包屑导航 */}
      <Breadcrumb className="mb-3">
        <Breadcrumb.Item>
          <Link to="/">首页</Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item>
          <Link to="/products">商品列表</Link>
        </Breadcrumb.Item>
        {product.category && (
          <Breadcrumb.Item>
            <Link to={`/products?category=${product.category._id}`}>
              {product.category.name}
            </Link>
          </Breadcrumb.Item>
        )}
        <Breadcrumb.Item>{product.name}</Breadcrumb.Item>
      </Breadcrumb>
      
      <Row gutter={[32, 24]}>
        {/* 商品图片 */}
        <Col xs={24} md={12}>
          <Image.PreviewGroup>
            <Image
              src={product.images && product.images.length > 0 
                ? product.images[0] 
                : 'https://via.placeholder.com/500x500?text=' + product.name}
              alt={product.name}
              style={{ width: '100%', maxHeight: '500px', objectFit: 'contain' }}
            />
            {product.images && product.images.length > 1 && (
              <div style={{ marginTop: '16px', display: 'flex', gap: '8px', overflowX: 'auto' }}>
                {product.images.map((image, index) => (
                  <Image
                    key={index}
                    src={image}
                    alt={`${product.name}-${index}`}
                    style={{ width: '80px', height: '80px', objectFit: 'cover' }}
                  />
                ))}
              </div>
            )}
          </Image.PreviewGroup>
        </Col>
        
        {/* 商品信息 */}
        <Col xs={24} md={12}>
          <Title level={2}>{product.name}</Title>
          
          <div className="mb-2">
            <Rate disabled defaultValue={product.rating} />
            <span style={{ marginLeft: '8px' }}>
              {product.rating.toFixed(1)} ({product.numReviews} 条评价)
            </span>
            <span style={{ marginLeft: '16px' }}>已售 {product.sold} 件</span>
          </div>
          
          <div className="mb-3" style={{ background: '#f5f5f5', padding: '16px', borderRadius: '4px' }}>
            {product.isOnSale && product.originalPrice ? (
              <>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <Text type="secondary">原价: </Text>
                  <Text delete style={{ marginLeft: '8px', fontSize: '14px' }}>
                    ¥{product.originalPrice.toFixed(2)}
                  </Text>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', marginTop: '8px' }}>
                  <Text style={{ fontSize: '16px' }}>现价: </Text>
                  <Text style={{ marginLeft: '8px', color: '#ff4d4f', fontSize: '24px', fontWeight: 'bold' }}>
                    ¥{product.price.toFixed(2)}
                  </Text>
                  {discountPercent > 0 && (
                    <Tag color="red" style={{ marginLeft: '8px' }}>
                      {discountPercent}% 折扣
                    </Tag>
                  )}
                </div>
              </>
            ) : (
              <div>
                <Text style={{ fontSize: '16px' }}>价格: </Text>
                <Text style={{ marginLeft: '8px', color: '#ff4d4f', fontSize: '24px', fontWeight: 'bold' }}>
                  ¥{product.price.toFixed(2)}
                </Text>
              </div>
            )}
          </div>
          
          <div className="mb-3">
            <Text>库存状态: </Text>
            {product.stock > 0 ? (
              <Text type="success" strong>
                <CheckCircleFilled style={{ marginRight: '4px' }} />
                有货 ({product.stock} 件)
              </Text>
            ) : (
              <Text type="danger" strong>
                <CloseCircleFilled style={{ marginRight: '4px' }} />
                已售罄
              </Text>
            )}
          </div>
          
          <Divider />
          
          <div className="mb-3">
            <div style={{ marginBottom: '16px' }}>
              <Text style={{ marginRight: '16px' }}>数量:</Text>
              <InputNumber
                min={1}
                max={product.stock}
                value={quantity}
                onChange={handleQuantityChange}
                disabled={product.stock <= 0}
              />
            </div>
            
            <div style={{ display: 'flex', gap: '16px' }}>
              <Button
                type="primary"
                icon={<ShoppingCartOutlined />}
                size="large"
                onClick={handleAddToCart}
                disabled={product.stock <= 0}
              >
                加入购物车
              </Button>
              <Button
                type="danger"
                size="large"
                onClick={handleBuyNow}
                disabled={product.stock <= 0}
              >
                立即购买
              </Button>
            </div>
          </div>
          
          <Divider />
          
          <div className="mb-3">
            <div style={{ display: 'flex', gap: '24px' }}>
              <Button type="text" icon={<HeartOutlined />}>
                收藏
              </Button>
              <Button type="text" icon={<ShareAltOutlined />}>
                分享
              </Button>
            </div>
          </div>
        </Col>
      </Row>
      
      {/* 商品详情 */}
      <Card className="mt-3">
        <Tabs defaultActiveKey="description">
          <TabPane tab="商品详情" key="description">
            <div style={{ padding: '16px 0' }}>
              <Paragraph>{product.description}</Paragraph>
            </div>
          </TabPane>
          <TabPane tab="规格参数" key="specs">
            <div style={{ padding: '16px 0' }}>
              <p>暂无规格参数信息</p>
            </div>
          </TabPane>
          <TabPane tab="用户评价" key="reviews">
            <div style={{ padding: '16px 0' }}>
              <p>暂无评价信息</p>
            </div>
          </TabPane>
        </Tabs>
      </Card>
      
      {/* 相关商品推荐 */}
      {relatedProducts.length > 0 && (
        <div className="mt-3">
          <Title level={3}>相关商品推荐</Title>
          <Row gutter={[16, 16]}>
            {relatedProducts.map(relatedProduct => (
              <Col key={relatedProduct._id} xs={12} sm={8} md={6}>
                <Card
                  hoverable
                  cover={
                    <img 
                      alt={relatedProduct.name} 
                      src={relatedProduct.images && relatedProduct.images.length > 0 
                        ? relatedProduct.images[0] 
                        : 'https://via.placeholder.com/300'}
                      style={{ height: '200px', objectFit: 'cover' }}
                    />
                  }
                  onClick={() => navigate(`/products/${relatedProduct._id}`)}
                >
                  <Card.Meta
                    title={relatedProduct.name}
                    description={
                      <Text type="danger">¥{relatedProduct.price.toFixed(2)}</Text>
                    }
                  />
                </Card>
              </Col>
            ))}
          </Row>
        </div>
      )}
    </div>
  );
};

export default ProductDetail; 