:root {
  --primary-color: #1a1a2e;
  --secondary-color: #16213e;
  --accent-color: #0f3460;
  --highlight-color: #e94560;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
}

main {
  min-height: 80vh;
}

.rating span {
  margin: 0.1rem;
}

.rating svg {
  color: var(--warning-color);
}

.product-title {
  height: 2.5em;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-card {
  transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.btn-primary {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
}

.btn-primary:hover {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
}

.btn-outline-primary {
  color: var(--accent-color);
  border-color: var(--accent-color);
}

.btn-outline-primary:hover {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
}

.carousel-item-next,
.carousel-item-prev,
.carousel-item.active {
  display: flex;
}

.carousel-caption {
  position: absolute;
  top: 0;
  padding: 10px;
  box-sizing: border-box;
}

.carousel-caption h2 {
  color: #fff;
}

.carousel img {
  height: 300px;
  padding: 30px;
  margin: 40px;
  border-radius: 50%;
  margin-left: auto;
  margin-right: auto;
}

.carousel a {
  margin: 0 auto;
}

.navbar {
  background-color: var(--primary-color);
}

.navbar-brand, .navbar-nav .nav-link {
  color: var(--light-color);
}

.navbar-brand:hover, .navbar-nav .nav-link:hover {
  color: var(--highlight-color);
}

.navbar-toggler {
  border-color: var(--light-color);
}

.navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba(255, 255, 255, 0.55)' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

footer {
  background-color: var(--primary-color);
  color: var(--light-color);
  padding: 20px 0;
}

.checkout-steps {
  display: flex;
  justify-content: center;
  margin: 20px 0;
}

.checkout-steps > div {
  border-bottom: 0.2rem solid #a0a0a0;
  color: #a0a0a0;
  flex: 1;
  padding: 0.5rem;
  font-weight: bold;
  text-align: center;
}

.checkout-steps > div.active {
  border-bottom: 0.2rem solid var(--highlight-color);
  color: var(--highlight-color);
}

.form-control:focus {
  border-color: var(--accent-color);
  box-shadow: 0 0 0 0.25rem rgba(15, 52, 96, 0.25);
}

.badge-success {
  background-color: var(--success-color);
}

.badge-danger {
  background-color: var(--danger-color);
}

.badge-warning {
  background-color: var(--warning-color);
}

.badge-info {
  background-color: var(--info-color);
}

.table-responsive {
  margin: 20px 0;
}

.product-image {
  max-height: 500px;
  object-fit: contain;
}

.review {
  margin-top: 30px;
}

.review h2 {
  font-size: 24px;
  background: var(--light-color);
  padding: 10px;
  border: 1px solid #ddd;
}

.review button {
  margin-top: 10px;
}

.carousel-caption h2 {
  font-size: 1.4rem;
}

@media (max-width: 900px) {
  .carousel-caption h2 {
    font-size: 1.2rem;
  }
}

@media (max-width: 768px) {
  .carousel-caption h2 {
    font-size: 1rem;
  }
}