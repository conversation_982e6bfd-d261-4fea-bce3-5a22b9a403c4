import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Typography,
  Tag,
  Modal,
  Descriptions,
  Divider,
  List,
  Avatar,
  message,
  Select,
  Tooltip,
} from 'antd';
import {
  EyeOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  CarOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import { Link } from 'react-router-dom';
import { formatPrice, formatDate, formatOrderStatus } from '../../utils/formatUtils';

const { Title, Text } = Typography;
const { Option } = Select;

const OrderManagement = () => {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [currentOrder, setCurrentOrder] = useState(null);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 获取订单列表
  const fetchOrders = async (page = 1, pageSize = 10) => {
    setLoading(true);
    try {
      // 这里应该调用实际的API
      // const response = await getOrders({ page, pageSize });
      // setOrders(response.orders);
      // setPagination({
      //   current: response.page,
      //   pageSize: response.pageSize,
      //   total: response.total,
      // });
      
      // 模拟数据
      setTimeout(() => {
        const statuses = ['pending', 'processing', 'shipped', 'delivered', 'cancelled'];
        const paymentMethods = ['alipay', 'wechat', 'creditCard'];
        
        const mockOrders = Array.from({ length: 20 }, (_, i) => {
          const status = statuses[Math.floor(Math.random() * statuses.length)];
          const orderDate = new Date(Date.now() - Math.floor(Math.random() * 30) * 86400000);
          const isPaid = Math.random() > 0.2;
          const paidAt = isPaid ? new Date(orderDate.getTime() + Math.floor(Math.random() * 2) * 86400000) : null;
          const isDelivered = status === 'shipped' || status === 'delivered';
          const deliveredAt = isDelivered ? new Date(paidAt.getTime() + Math.floor(Math.random() * 5) * 86400000) : null;
          
          const itemCount = Math.floor(Math.random() * 5) + 1;
          const items = Array.from({ length: itemCount }, (_, j) => ({
            _id: `item-${i}-${j}`,
            product: {
              _id: `product-${j}`,
              name: `测试商品 ${j}`,
              image: `https://picsum.photos/id/${(i * 10 + j) % 100}/200/200`,
              price: Math.floor(Math.random() * 1000) + 10,
            },
            quantity: Math.floor(Math.random() * 3) + 1,
            price: Math.floor(Math.random() * 1000) + 10,
          }));
          
          const itemsPrice = items.reduce((sum, item) => sum + item.price * item.quantity, 0);
          const shippingPrice = itemsPrice > 99 ? 0 : 10;
          const totalPrice = itemsPrice + shippingPrice;
          
          return {
            _id: `order-${i}`,
            user: {
              _id: `user-${i % 10}`,
              name: `用户${i % 10}`,
              email: `user${i % 10}@example.com`,
            },
            orderItems: items,
            shippingAddress: {
              address: `测试地址${i}号`,
              city: '上海市',
              postalCode: '200000',
              country: '中国',
              recipient: `收件人${i}`,
              phone: `1381234${i.toString().padStart(4, '0')}`,
            },
            paymentMethod: paymentMethods[Math.floor(Math.random() * paymentMethods.length)],
            itemsPrice,
            shippingPrice,
            totalPrice,
            isPaid,
            paidAt,
            isDelivered,
            deliveredAt,
            status,
            createdAt: orderDate.toISOString(),
          };
        });
        
        setOrders(mockOrders);
        setPagination({
          current: page,
          pageSize: pageSize,
          total: 100,
        });
        setLoading(false);
      }, 500);
    } catch (error) {
      message.error('获取订单列表失败');
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOrders();
  }, []);

  // 处理表格分页变化
  const handleTableChange = (pagination) => {
    fetchOrders(pagination.current, pagination.pageSize);
  };

  // 查看订单详情
  const showOrderDetail = (order) => {
    setCurrentOrder(order);
    setDetailModalVisible(true);
  };

  // 更新订单状态
  const updateOrderStatus = async (orderId, status) => {
    try {
      // 这里应该调用实际的API
      // await updateOrder(orderId, { status });
      
      // 模拟更新
      setOrders(orders.map(order => 
        order._id === orderId ? { ...order, status } : order
      ));
      
      if (currentOrder && currentOrder._id === orderId) {
        setCurrentOrder({ ...currentOrder, status });
      }
      
      message.success('订单状态更新成功');
    } catch (error) {
      message.error(`更新失败: ${error.message}`);
    }
  };

  // 标记为已发货
  const markAsShipped = async (orderId) => {
    try {
      // 这里应该调用实际的API
      // await updateOrder(orderId, { isDelivered: true, deliveredAt: new Date(), status: 'shipped' });
      
      // 模拟更新
      const now = new Date();
      setOrders(orders.map(order => 
        order._id === orderId ? { 
          ...order, 
          isDelivered: true, 
          deliveredAt: now.toISOString(),
          status: 'shipped'
        } : order
      ));
      
      if (currentOrder && currentOrder._id === orderId) {
        setCurrentOrder({ 
          ...currentOrder, 
          isDelivered: true, 
          deliveredAt: now.toISOString(),
          status: 'shipped'
        });
      }
      
      message.success('订单已标记为已发货');
    } catch (error) {
      message.error(`操作失败: ${error.message}`);
    }
  };

  // 取消订单
  const cancelOrder = async (orderId) => {
    try {
      // 这里应该调用实际的API
      // await updateOrder(orderId, { status: 'cancelled' });
      
      // 模拟更新
      setOrders(orders.map(order => 
        order._id === orderId ? { ...order, status: 'cancelled' } : order
      ));
      
      if (currentOrder && currentOrder._id === orderId) {
        setCurrentOrder({ ...currentOrder, status: 'cancelled' });
      }
      
      message.success('订单已取消');
    } catch (error) {
      message.error(`操作失败: ${error.message}`);
    }
  };

  // 获取订单状态标签颜色
  const getStatusTagColor = (status) => {
    switch (status) {
      case 'pending':
        return 'gold';
      case 'processing':
        return 'blue';
      case 'shipped':
        return 'cyan';
      case 'delivered':
        return 'green';
      case 'cancelled':
        return 'red';
      default:
        return 'default';
    }
  };

  // 获取支付方式显示文本
  const getPaymentMethodText = (method) => {
    switch (method) {
      case 'alipay':
        return '支付宝';
      case 'wechat':
        return '微信支付';
      case 'creditCard':
        return '信用卡';
      default:
        return method;
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '订单号',
      dataIndex: '_id',
      key: '_id',
      render: (id) => <Link to={`/order/${id}`}>{id}</Link>,
    },
    {
      title: '用户',
      dataIndex: 'user',
      key: 'user',
      render: (user) => user?.name || '未知用户',
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => formatDate(date),
    },
    {
      title: '订单状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusTagColor(status)}>
          {formatOrderStatus(status)}
        </Tag>
      ),
    },
    {
      title: '支付状态',
      key: 'paymentStatus',
      render: (_, record) => (
        <Tag color={record.isPaid ? 'green' : 'red'}>
          {record.isPaid ? '已支付' : '未支付'}
        </Tag>
      ),
    },
    {
      title: '配送状态',
      key: 'deliveryStatus',
      render: (_, record) => (
        <Tag color={record.isDelivered ? 'green' : 'orange'}>
          {record.isDelivered ? '已发货' : '未发货'}
        </Tag>
      ),
    },
    {
      title: '总金额',
      dataIndex: 'totalPrice',
      key: 'totalPrice',
      render: (price) => <Text strong>{formatPrice(price)}</Text>,
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="primary"
            icon={<EyeOutlined />}
            size="small"
            onClick={() => showOrderDetail(record)}
          >
            查看
          </Button>
          
          {record.status !== 'cancelled' && record.status !== 'delivered' && (
            <Select
              defaultValue="更新状态"
              style={{ width: 120 }}
              onChange={(value) => updateOrderStatus(record._id, value)}
              size="small"
            >
              <Option value="pending">待处理</Option>
              <Option value="processing">处理中</Option>
              <Option value="shipped">已发货</Option>
              <Option value="delivered">已送达</Option>
              <Option value="cancelled">已取消</Option>
            </Select>
          )}
          
          {record.isPaid && !record.isDelivered && record.status !== 'cancelled' && (
            <Tooltip title="标记为已发货">
              <Button
                type="primary"
                icon={<CarOutlined />}
                size="small"
                onClick={() => markAsShipped(record._id)}
              >
                发货
              </Button>
            </Tooltip>
          )}
          
          {(record.status === 'pending' || record.status === 'processing') && (
            <Tooltip title="取消订单">
              <Button
                type="primary"
                danger
                icon={<CloseCircleOutlined />}
                size="small"
                onClick={() => cancelOrder(record._id)}
              >
                取消
              </Button>
            </Tooltip>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div className="order-management">
      <div className="page-header" style={{ marginBottom: '16px' }}>
        <Title level={2}>订单管理</Title>
      </div>

      <Table
        columns={columns}
        dataSource={orders}
        rowKey="_id"
        pagination={pagination}
        loading={loading}
        onChange={handleTableChange}
      />

      <Modal
        title="订单详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {currentOrder && (
          <div>
            <Descriptions title="订单信息" bordered column={2}>
              <Descriptions.Item label="订单号">{currentOrder._id}</Descriptions.Item>
              <Descriptions.Item label="创建时间">{formatDate(currentOrder.createdAt)}</Descriptions.Item>
              <Descriptions.Item label="用户">{currentOrder.user.name}</Descriptions.Item>
              <Descriptions.Item label="邮箱">{currentOrder.user.email}</Descriptions.Item>
              <Descriptions.Item label="支付方式">{getPaymentMethodText(currentOrder.paymentMethod)}</Descriptions.Item>
              <Descriptions.Item label="订单状态">
                <Tag color={getStatusTagColor(currentOrder.status)}>
                  {formatOrderStatus(currentOrder.status)}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="支付状态">
                <Tag color={currentOrder.isPaid ? 'green' : 'red'}>
                  {currentOrder.isPaid ? '已支付' : '未支付'}
                </Tag>
                {currentOrder.isPaid && (
                  <div>支付时间: {formatDate(currentOrder.paidAt)}</div>
                )}
              </Descriptions.Item>
              <Descriptions.Item label="配送状态">
                <Tag color={currentOrder.isDelivered ? 'green' : 'orange'}>
                  {currentOrder.isDelivered ? '已发货' : '未发货'}
                </Tag>
                {currentOrder.isDelivered && (
                  <div>发货时间: {formatDate(currentOrder.deliveredAt)}</div>
                )}
              </Descriptions.Item>
            </Descriptions>

            <Divider orientation="left">收货信息</Divider>
            <Descriptions bordered column={2}>
              <Descriptions.Item label="收件人">{currentOrder.shippingAddress.recipient}</Descriptions.Item>
              <Descriptions.Item label="联系电话">{currentOrder.shippingAddress.phone}</Descriptions.Item>
              <Descriptions.Item label="收货地址" span={2}>
                {`${currentOrder.shippingAddress.country} ${currentOrder.shippingAddress.city} ${currentOrder.shippingAddress.address} ${currentOrder.shippingAddress.postalCode}`}
              </Descriptions.Item>
            </Descriptions>

            <Divider orientation="left">商品信息</Divider>
            <List
              itemLayout="horizontal"
              dataSource={currentOrder.orderItems}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={<Avatar shape="square" size={64} src={item.product.image} />}
                    title={<Link to={`/product/${item.product._id}`}>{item.product.name}</Link>}
                    description={`单价: ${formatPrice(item.price)} × ${item.quantity}件`}
                  />
                  <div>
                    <Text strong>{formatPrice(item.price * item.quantity)}</Text>
                  </div>
                </List.Item>
              )}
            />

            <Divider />
            <div style={{ textAlign: 'right' }}>
              <div>商品总价: {formatPrice(currentOrder.itemsPrice)}</div>
              <div>运费: {formatPrice(currentOrder.shippingPrice)}</div>
              <div style={{ marginTop: '8px' }}>
                <Text strong style={{ fontSize: '16px' }}>
                  订单总计: {formatPrice(currentOrder.totalPrice)}
                </Text>
              </div>
            </div>

            <Divider />
            <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
              {currentOrder.status !== 'cancelled' && currentOrder.status !== 'delivered' && (
                <Space>
                  <Select
                    value={currentOrder.status}
                    style={{ width: 120 }}
                    onChange={(value) => updateOrderStatus(currentOrder._id, value)}
                  >
                    <Option value="pending">待处理</Option>
                    <Option value="processing">处理中</Option>
                    <Option value="shipped">已发货</Option>
                    <Option value="delivered">已送达</Option>
                    <Option value="cancelled">已取消</Option>
                  </Select>
                  
                  <Button
                    type="primary"
                    onClick={() => updateOrderStatus(currentOrder._id, currentOrder.status)}
                  >
                    更新状态
                  </Button>
                  
                  {currentOrder.isPaid && !currentOrder.isDelivered && currentOrder.status !== 'cancelled' && (
                    <Button
                      type="primary"
                      icon={<CarOutlined />}
                      onClick={() => markAsShipped(currentOrder._id)}
                    >
                      标记为已发货
                    </Button>
                  )}
                  
                  {(currentOrder.status === 'pending' || currentOrder.status === 'processing') && (
                    <Button
                      type="primary"
                      danger
                      icon={<CloseCircleOutlined />}
                      onClick={() => cancelOrder(currentOrder._id)}
                    >
                      取消订单
                    </Button>
                  )}
                </Space>
              )}
              
              <Button 
                style={{ marginLeft: 8 }} 
                onClick={() => setDetailModalVisible(false)}
              >
                关闭
              </Button>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default OrderManagement;