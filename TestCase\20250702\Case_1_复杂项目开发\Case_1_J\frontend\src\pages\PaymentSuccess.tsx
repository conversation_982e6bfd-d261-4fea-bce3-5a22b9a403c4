import React, { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Result, Button, Card, Typography, Space, Divider } from 'antd';
import { CheckCircleOutlined, ShoppingOutlined, FileTextOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;

const PaymentSuccess: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const orderNo = (location.state as any)?.orderNo;

  useEffect(() => {
    // 如果没有订单号，重定向到首页
    if (!orderNo) {
      navigate('/', { replace: true });
    }
  }, [orderNo, navigate]);

  const handleViewOrder = () => {
    if (orderNo) {
      navigate(`/orders/${orderNo}`);
    } else {
      navigate('/orders');
    }
  };

  const handleContinueShopping = () => {
    navigate('/products');
  };

  const handleBackHome = () => {
    navigate('/');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          {/* 成功提示 */}
          <Result
            status="success"
            title="支付成功！"
            subTitle={orderNo ? `订单 ${orderNo} 已支付成功，我们将尽快为您处理订单` : '支付已完成'}
            icon={<CheckCircleOutlined className="text-green-500" />}
          />

          {/* 后续操作 */}
          <Card className="mt-6">
            <Title level={4} className="text-center mb-6">
              接下来您可以
            </Title>
            
            <Space direction="vertical" size="large" className="w-full">
              <Button
                type="primary"
                size="large"
                icon={<FileTextOutlined />}
                onClick={handleViewOrder}
                block
              >
                查看订单详情
              </Button>
              
              <Button
                size="large"
                icon={<ShoppingOutlined />}
                onClick={handleContinueShopping}
                block
              >
                继续购物
              </Button>
              
              <Button
                size="large"
                onClick={handleBackHome}
                block
              >
                返回首页
              </Button>
            </Space>
          </Card>

          {/* 温馨提示 */}
          <Card className="mt-6" title="温馨提示">
            <Space direction="vertical" size="small">
              <Text className="text-sm text-gray-600">
                <CheckCircleOutlined className="text-green-500 mr-2" />
                您的订单已进入处理流程，我们会尽快安排发货
              </Text>
              <Text className="text-sm text-gray-600">
                <CheckCircleOutlined className="text-green-500 mr-2" />
                您可以在"我的订单"中查看订单状态和物流信息
              </Text>
              <Text className="text-sm text-gray-600">
                <CheckCircleOutlined className="text-green-500 mr-2" />
                如有任何问题，请联系客服：400-123-4567
              </Text>
            </Space>
          </Card>

          {/* 服务承诺 */}
          <Card className="mt-6" title="我们的服务承诺">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="text-center p-4">
                <div className="text-3xl mb-2">🚚</div>
                <Title level={5}>快速发货</Title>
                <Text className="text-sm text-gray-600">
                  24小时内发货
                </Text>
              </div>
              
              <div className="text-center p-4">
                <div className="text-3xl mb-2">📦</div>
                <Title level={5}>安全包装</Title>
                <Text className="text-sm text-gray-600">
                  专业包装保护
                </Text>
              </div>
              
              <div className="text-center p-4">
                <div className="text-3xl mb-2">↩️</div>
                <Title level={5}>7天退换</Title>
                <Text className="text-sm text-gray-600">
                  无理由退换货
                </Text>
              </div>
              
              <div className="text-center p-4">
                <div className="text-3xl mb-2">🎧</div>
                <Title level={5}>贴心客服</Title>
                <Text className="text-sm text-gray-600">
                  24小时在线服务
                </Text>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default PaymentSuccess;
