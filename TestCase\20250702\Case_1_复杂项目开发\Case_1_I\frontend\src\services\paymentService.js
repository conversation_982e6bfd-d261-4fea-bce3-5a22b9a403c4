import apiClient from './apiClient';

// 获取支付方式列表
const getPaymentMethods = async () => {
  const response = await apiClient.get('/payments/methods');
  return response.data;
};

// 创建支付意向
const createPaymentIntent = async (orderId, token) => {
  const response = await apiClient.post(
    '/payments/create-intent',
    { orderId },
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    }
  );
  return response.data;
};

// 确认支付
const confirmPayment = async (paymentId, paymentDetails, token) => {
  const response = await apiClient.post(
    `/payments/${paymentId}/confirm`,
    paymentDetails,
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    }
  );
  return response.data;
};

// 获取支付状态
const getPaymentStatus = async (paymentId, token) => {
  const response = await apiClient.get(`/payments/${paymentId}/status`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

// 退款（管理员）
const refundPayment = async (paymentId, refundReason, token) => {
  const response = await apiClient.post(
    `/payments/${paymentId}/refund`,
    { reason: refundReason },
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    }
  );
  return response.data;
};

const paymentService = {
  getPaymentMethods,
  createPaymentIntent,
  confirmPayment,
  getPaymentStatus,
  refundPayment,
};

export default paymentService;