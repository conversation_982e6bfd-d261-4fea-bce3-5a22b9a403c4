import apiClient from './apiClient';

// 获取产品列表
const getProducts = async (
  keyword = '',
  pageNumber = 1,
  category = '',
  sortBy = '',
  minPrice = '',
  maxPrice = ''
) => {
  const response = await apiClient.get('/products', {
    params: {
      keyword,
      page: pageNumber,
      category,
      sortBy,
      minPrice,
      maxPrice,
    },
  });
  return response.data;
};

// 获取产品详情
const getProductDetails = async (id) => {
  const response = await apiClient.get(`/products/${id}`);
  return response.data;
};

// 创建产品评论
const createProductReview = async (productId, review, token) => {
  const response = await apiClient.post(
    `/products/${productId}/reviews`,
    review,
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    }
  );
  return response.data;
};

// 获取产品评论
const getProductReviews = async (productId) => {
  const response = await apiClient.get(`/products/${productId}/reviews`);
  return response.data;
};

// 创建产品（管理员）
const createProduct = async (productData, token) => {
  const response = await apiClient.post('/products', productData, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

// 更新产品（管理员）
const updateProduct = async (id, productData, token) => {
  const response = await apiClient.put(`/products/${id}`, productData, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

// 删除产品（管理员）
const deleteProduct = async (id, token) => {
  const response = await apiClient.delete(`/products/${id}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

// 上传产品图片（管理员）
const uploadProductImage = async (id, formData, token) => {
  const response = await apiClient.post(`/products/${id}/upload`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

// 删除产品图片（管理员）
const deleteProductImage = async (id, imageId, token) => {
  const response = await apiClient.delete(`/products/${id}/images/${imageId}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

const productService = {
  getProducts,
  getProductDetails,
  createProductReview,
  getProductReviews,
  createProduct,
  updateProduct,
  deleteProduct,
  uploadProductImage,
  deleteProductImage,
};

export default productService;