const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { AppError } = require('./error');

// 确保上传目录存在
const uploadDir = path.join(__dirname, '../uploads');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// 配置存储
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    // 根据文件类型确定存储目录
    let dest = uploadDir;
    if (file.fieldname === 'product') {
      dest = path.join(uploadDir, 'products');
    } else if (file.fieldname === 'avatar') {
      dest = path.join(uploadDir, 'avatars');
    } else if (file.fieldname === 'category') {
      dest = path.join(uploadDir, 'categories');
    }

    // 确保目录存在
    if (!fs.existsSync(dest)) {
      fs.mkdirSync(dest, { recursive: true });
    }

    cb(null, dest);
  },
  filename: function (req, file, cb) {
    // 生成唯一文件名：时间戳-原始文件名
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
    const ext = path.extname(file.originalname);
    cb(null, uniqueSuffix + ext);
  },
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
  // 允许的文件类型
  const allowedFileTypes = /jpeg|jpg|png|gif|webp/;
  const extname = allowedFileTypes.test(
    path.extname(file.originalname).toLowerCase()
  );
  const mimetype = allowedFileTypes.test(file.mimetype);

  if (extname && mimetype) {
    return cb(null, true);
  } else {
    cb(
      new AppError(
        '只允许上传图片文件（jpeg, jpg, png, gif, webp）',
        400
      )
    );
  }
};

// 配置上传
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024, // 限制5MB
  },
});

module.exports = upload;