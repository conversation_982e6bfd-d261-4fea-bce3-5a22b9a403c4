import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Statistic, Typography, Table, Tag, Button } from 'antd';
import { 
  ShoppingCartOutlined, 
  UserOutlined, 
  DollarOutlined, 
  ShoppingOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined
} from '@ant-design/icons';
import { Link } from 'react-router-dom';
import { getOrderStatistics } from '../../services/orderService';
import { formatPrice, formatDate, formatOrderStatus } from '../../utils/formatUtils';

const { Title } = Typography;

const Dashboard = () => {
  const [stats, setStats] = useState({
    totalSales: 0,
    totalOrders: 0,
    totalUsers: 0,
    totalProducts: 0,
    recentOrders: [],
    salesByMonth: [],
    loading: true,
  });

  useEffect(() => {
    const fetchStatistics = async () => {
      try {
        const data = await getOrderStatistics();
        setStats({
          ...data,
          loading: false,
        });
      } catch (error) {
        console.error('获取统计数据失败:', error);
        setStats(prev => ({ ...prev, loading: false }));
      }
    };

    fetchStatistics();
  }, []);

  // 获取订单状态标签颜色
  const getStatusTagColor = (status) => {
    switch (status) {
      case 'pending':
        return 'orange';
      case 'processing':
        return 'blue';
      case 'shipped':
        return 'cyan';
      case 'delivered':
        return 'green';
      case 'cancelled':
        return 'red';
      default:
        return 'default';
    }
  };

  // 最近订单表格列
  const columns = [
    {
      title: '订单号',
      dataIndex: '_id',
      key: '_id',
      render: (id) => <Link to={`/admin/orders/${id}`}>{id.substring(0, 8)}...</Link>,
    },
    {
      title: '客户',
      dataIndex: 'user',
      key: 'user',
      render: (user) => user?.name || '未知用户',
    },
    {
      title: '金额',
      dataIndex: 'totalPrice',
      key: 'totalPrice',
      render: (price) => formatPrice(price),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusTagColor(status)}>{formatOrderStatus(status)}</Tag>
      ),
    },
    {
      title: '日期',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => formatDate(date),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Link to={`/admin/orders/${record._id}`}>
          <Button type="link" size="small">查看详情</Button>
        </Link>
      ),
    },
  ];

  return (
    <div className="admin-dashboard">
      <Title level={2}>控制面板</Title>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="总销售额"
              value={stats.totalSales}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#3f8600' }}
              prefix={<DollarOutlined />}
              suffix={<span style={{ fontSize: '14px', color: '#52c41a' }}>
                <ArrowUpOutlined /> 12%
              </span>}
              loading={stats.loading}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="订单总数"
              value={stats.totalOrders}
              valueStyle={{ color: '#1890ff' }}
              prefix={<ShoppingOutlined />}
              loading={stats.loading}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="用户总数"
              value={stats.totalUsers}
              valueStyle={{ color: '#722ed1' }}
              prefix={<UserOutlined />}
              loading={stats.loading}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="商品总数"
              value={stats.totalProducts}
              valueStyle={{ color: '#fa8c16' }}
              prefix={<ShoppingCartOutlined />}
              loading={stats.loading}
            />
          </Card>
        </Col>
      </Row>

      {/* 最近订单 */}
      <Card title="最近订单" style={{ marginBottom: '24px' }}>
        <Table
          columns={columns}
          dataSource={stats.recentOrders}
          rowKey="_id"
          pagination={false}
          loading={stats.loading}
        />
        <div style={{ marginTop: '16px', textAlign: 'right' }}>
          <Link to="/admin/orders">
            <Button type="primary">查看全部订单</Button>
          </Link>
        </div>
      </Card>
    </div>
  );
};

export default Dashboard;