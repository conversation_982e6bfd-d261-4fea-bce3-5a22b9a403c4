version: '3.8'

services:
  # MongoDB 数据库
  mongodb:
    image: mongo:6.0
    container_name: ecommerce-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: ecommerce
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./scripts/init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js:ro
    networks:
      - ecommerce-network

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: ecommerce-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ecommerce-network

  # 后端 API 服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: ecommerce-backend
    restart: unless-stopped
    environment:
      NODE_ENV: production
      MONGODB_URI: ********************************************************************
      JWT_SECRET: your_jwt_secret_key_here
      PORT: 3001
    ports:
      - "3001:3001"
    depends_on:
      - mongodb
      - redis
    volumes:
      - ./backend/uploads:/app/uploads
    networks:
      - ecommerce-network

  # 前端应用
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: ecommerce-frontend
    restart: unless-stopped
    environment:
      REACT_APP_API_URL: http://localhost:3001/api
    ports:
      - "3000:80"
    depends_on:
      - backend
    networks:
      - ecommerce-network

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: ecommerce-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - frontend
      - backend
    networks:
      - ecommerce-network

volumes:
  mongodb_data:
  redis_data:

networks:
  ecommerce-network:
    driver: bridge
