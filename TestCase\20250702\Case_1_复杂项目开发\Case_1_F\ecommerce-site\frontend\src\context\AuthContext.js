import React, { createContext, useState, useEffect } from 'react';
import axios from 'axios';

export const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(localStorage.getItem('token'));
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // 配置axios默认设置
  axios.defaults.baseURL = 'http://localhost:5000/api';
  if (token) {
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  }

  // 用户登录
  const login = async (email, password) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await axios.post('/auth/login', { email, password });
      
      if (response.data.success) {
        setUser(response.data.user);
        setToken(response.data.token);
        localStorage.setItem('token', response.data.token);
        axios.defaults.headers.common['Authorization'] = `Bearer ${response.data.token}`;
        return true;
      }
    } catch (err) {
      setError(err.response?.data?.message || '登录失败，请稍后再试');
      return false;
    } finally {
      setLoading(false);
    }
  };

  // 用户注册
  const register = async (userData) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await axios.post('/auth/register', userData);
      
      if (response.data.success) {
        setUser(response.data.user);
        setToken(response.data.token);
        localStorage.setItem('token', response.data.token);
        axios.defaults.headers.common['Authorization'] = `Bearer ${response.data.token}`;
        return true;
      }
    } catch (err) {
      setError(err.response?.data?.message || '注册失败，请稍后再试');
      return false;
    } finally {
      setLoading(false);
    }
  };

  // 用户退出登录
  const logout = async () => {
    try {
      setLoading(true);
      
      if (token) {
        await axios.get('/auth/logout');
      }
      
      setUser(null);
      setToken(null);
      localStorage.removeItem('token');
      delete axios.defaults.headers.common['Authorization'];
      return true;
    } catch (err) {
      console.error('退出登录失败:', err);
      return false;
    } finally {
      setLoading(false);
    }
  };

  // 获取当前用户信息
  const getUserProfile = async () => {
    try {
      if (!token) {
        setLoading(false);
        return;
      }
      
      const response = await axios.get('/auth/me');
      
      if (response.data.success) {
        setUser(response.data.data);
      } else {
        setUser(null);
        setToken(null);
        localStorage.removeItem('token');
        delete axios.defaults.headers.common['Authorization'];
      }
    } catch (err) {
      console.error('获取用户信息失败:', err);
      setUser(null);
      setToken(null);
      localStorage.removeItem('token');
      delete axios.defaults.headers.common['Authorization'];
    } finally {
      setLoading(false);
    }
  };

  // 更新用户信息
  const updateProfile = async (userData) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await axios.put('/auth/updatedetails', userData);
      
      if (response.data.success) {
        setUser(response.data.data);
        return true;
      }
    } catch (err) {
      setError(err.response?.data?.message || '更新用户信息失败，请稍后再试');
      return false;
    } finally {
      setLoading(false);
    }
  };

  // 更新密码
  const updatePassword = async (currentPassword, newPassword) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await axios.put('/auth/updatepassword', { currentPassword, newPassword });
      
      if (response.data.success) {
        return true;
      }
    } catch (err) {
      setError(err.response?.data?.message || '更新密码失败，请稍后再试');
      return false;
    } finally {
      setLoading(false);
    }
  };

  // 应用加载时获取用户信息
  useEffect(() => {
    getUserProfile();
  }, []);

  return (
    <AuthContext.Provider
      value={{
        user,
        token,
        loading,
        error,
        login,
        register,
        logout,
        updateProfile,
        updatePassword,
        setError,
        isAuthenticated: !!user
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}; 