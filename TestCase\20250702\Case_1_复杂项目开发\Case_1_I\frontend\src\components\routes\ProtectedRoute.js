import React from 'react';
import { Navigate, Outlet } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { Result, Spin } from 'antd';

// 需要登录才能访问的路由
const ProtectedRoute = () => {
  const { userInfo, loading } = useSelector((state) => state.auth);

  // 如果正在加载，显示加载中
  if (loading) {
    return (
      <div className="centered-container">
        <Spin size="large" />
      </div>
    );
  }

  // 如果没有登录，重定向到登录页面
  if (!userInfo) {
    return <Navigate to="/login" replace />;
  }

  // 如果已登录，渲染子路由
  return <Outlet />;
};

export default ProtectedRoute;