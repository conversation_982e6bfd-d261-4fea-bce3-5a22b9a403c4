import moment from 'moment';

// 格式化价格
export const formatPrice = (price) => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
  }).format(price);
};

// 格式化日期
export const formatDate = (date, format = 'YYYY-MM-DD HH:mm:ss') => {
  return moment(date).format(format);
};

// 格式化日期为相对时间（如：3小时前）
export const formatDateFromNow = (date) => {
  return moment(date).fromNow();
};

// 截断文本
export const truncateText = (text, maxLength = 100) => {
  if (!text) return '';
  if (text.length <= maxLength) return text;
  return `${text.substring(0, maxLength)}...`;
};

// 格式化订单状态
export const formatOrderStatus = (status) => {
  const statusMap = {
    pending: '待付款',
    paid: '已付款',
    processing: '处理中',
    shipped: '已发货',
    delivered: '已送达',
    cancelled: '已取消',
    refunded: '已退款',
  };
  return statusMap[status] || status;
};

// 格式化支付状态
export const formatPaymentStatus = (status) => {
  const statusMap = {
    pending: '待支付',
    completed: '已完成',
    failed: '失败',
    refunded: '已退款',
    partially_refunded: '部分退款',
  };
  return statusMap[status] || status;
};

// 格式化文件大小
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};