const express = require('express');
const Order = require('../models/Order');
const { authenticateToken } = require('../middleware/auth');
const PaymentService = require('../services/PaymentService');

const router = express.Router();

// 创建支付订单
router.post('/create', authenticateToken, async (req, res) => {
  try {
    const { orderNo, paymentMethod } = req.body;
    const userId = req.user._id;

    // 查找订单
    const order = await Order.findOne({ orderNo, user: userId });
    if (!order) {
      return res.status(404).json({ message: '订单不存在' });
    }

    if (order.status !== 'pending') {
      return res.status(400).json({ message: '订单状态不正确' });
    }

    if (order.paymentMethod !== paymentMethod) {
      return res.status(400).json({ message: '支付方式不匹配' });
    }

    let paymentResult;
    
    switch (paymentMethod) {
      case 'alipay':
        paymentResult = await PaymentService.createAlipayOrder(order);
        break;
      case 'wechat':
        paymentResult = await PaymentService.createWechatOrder(order);
        break;
      default:
        return res.status(400).json({ message: '不支持的支付方式' });
    }

    res.json({
      message: '支付订单创建成功',
      paymentData: paymentResult
    });
  } catch (error) {
    console.error('创建支付订单错误:', error);
    res.status(500).json({ message: '创建支付订单失败' });
  }
});

// 支付宝回调
router.post('/alipay/notify', async (req, res) => {
  try {
    const notifyData = req.body;
    
    // 验证支付宝回调签名
    const isValid = PaymentService.verifyAlipayNotify(notifyData);
    if (!isValid) {
      return res.status(400).send('FAIL');
    }

    const { out_trade_no, trade_status, trade_no } = notifyData;

    if (trade_status === 'TRADE_SUCCESS' || trade_status === 'TRADE_FINISHED') {
      // 更新订单状态
      const order = await Order.findOne({ orderNo: out_trade_no });
      if (order && order.paymentStatus === 'pending') {
        order.paymentStatus = 'paid';
        order.status = 'paid';
        order.paymentId = trade_no;
        order.paidAt = new Date();
        await order.save();

        console.log(`订单 ${out_trade_no} 支付成功`);
      }
    }

    res.send('SUCCESS');
  } catch (error) {
    console.error('支付宝回调处理错误:', error);
    res.status(500).send('FAIL');
  }
});

// 微信支付回调
router.post('/wechat/notify', async (req, res) => {
  try {
    const notifyData = req.body;
    
    // 验证微信支付回调签名
    const isValid = PaymentService.verifyWechatNotify(notifyData);
    if (!isValid) {
      return res.status(400).json({ code: 'FAIL', message: '签名验证失败' });
    }

    const { out_trade_no, trade_state, transaction_id } = notifyData;

    if (trade_state === 'SUCCESS') {
      // 更新订单状态
      const order = await Order.findOne({ orderNo: out_trade_no });
      if (order && order.paymentStatus === 'pending') {
        order.paymentStatus = 'paid';
        order.status = 'paid';
        order.paymentId = transaction_id;
        order.paidAt = new Date();
        await order.save();

        console.log(`订单 ${out_trade_no} 支付成功`);
      }
    }

    res.json({ code: 'SUCCESS', message: '成功' });
  } catch (error) {
    console.error('微信支付回调处理错误:', error);
    res.status(500).json({ code: 'FAIL', message: '处理失败' });
  }
});

// 查询支付状态
router.get('/status/:orderNo', authenticateToken, async (req, res) => {
  try {
    const { orderNo } = req.params;
    const userId = req.user._id;

    const order = await Order.findOne({ orderNo, user: userId });
    if (!order) {
      return res.status(404).json({ message: '订单不存在' });
    }

    // 如果订单状态是待付款，主动查询支付状态
    if (order.paymentStatus === 'pending') {
      let paymentStatus;
      
      switch (order.paymentMethod) {
        case 'alipay':
          paymentStatus = await PaymentService.queryAlipayOrder(orderNo);
          break;
        case 'wechat':
          paymentStatus = await PaymentService.queryWechatOrder(orderNo);
          break;
        default:
          paymentStatus = { status: 'pending' };
      }

      // 如果查询到已支付，更新订单状态
      if (paymentStatus.status === 'paid' && order.paymentStatus === 'pending') {
        order.paymentStatus = 'paid';
        order.status = 'paid';
        order.paymentId = paymentStatus.transactionId;
        order.paidAt = new Date();
        await order.save();
      }
    }

    res.json({
      orderNo: order.orderNo,
      paymentStatus: order.paymentStatus,
      paymentMethod: order.paymentMethod,
      finalAmount: order.finalAmount,
      paidAt: order.paidAt
    });
  } catch (error) {
    console.error('查询支付状态错误:', error);
    res.status(500).json({ message: '查询支付状态失败' });
  }
});

// 申请退款
router.post('/refund', authenticateToken, async (req, res) => {
  try {
    const { orderNo, reason } = req.body;
    const userId = req.user._id;

    const order = await Order.findOne({ orderNo, user: userId });
    if (!order) {
      return res.status(404).json({ message: '订单不存在' });
    }

    if (order.paymentStatus !== 'paid') {
      return res.status(400).json({ message: '订单未支付，无法退款' });
    }

    if (order.status === 'refunded') {
      return res.status(400).json({ message: '订单已退款' });
    }

    let refundResult;
    
    switch (order.paymentMethod) {
      case 'alipay':
        refundResult = await PaymentService.refundAlipayOrder(order, reason);
        break;
      case 'wechat':
        refundResult = await PaymentService.refundWechatOrder(order, reason);
        break;
      default:
        return res.status(400).json({ message: '不支持的支付方式' });
    }

    if (refundResult.success) {
      // 更新订单状态
      order.paymentStatus = 'refunded';
      order.status = 'refunded';
      order.refundedAt = new Date();
      await order.save();

      // 恢复库存
      for (const item of order.items) {
        await Product.updateOne(
          { _id: item.product, 'skus.sku': item.sku },
          { $inc: { 'skus.$.stock': item.quantity } }
        );
      }

      res.json({
        message: '退款申请成功',
        refundId: refundResult.refundId
      });
    } else {
      res.status(400).json({
        message: '退款申请失败',
        error: refundResult.error
      });
    }
  } catch (error) {
    console.error('申请退款错误:', error);
    res.status(500).json({ message: '申请退款失败' });
  }
});

module.exports = router;
