import React, { useEffect } from 'react';
import { message } from 'antd';
import { useSelector, useDispatch } from 'react-redux';
import { hideNotification } from '../../redux/slices/uiSlice';

const Message = () => {
  const dispatch = useDispatch();
  const { notification } = useSelector((state) => state.ui);

  useEffect(() => {
    if (notification.show) {
      // 根据通知类型显示不同的消息
      switch (notification.type) {
        case 'success':
          message.success(notification.message);
          break;
        case 'error':
          message.error(notification.message);
          break;
        case 'warning':
          message.warning(notification.message);
          break;
        case 'info':
          message.info(notification.message);
          break;
        default:
          message.info(notification.message);
      }

      // 隐藏通知
      dispatch(hideNotification());
    }
  }, [notification, dispatch]);

  return null; // 这个组件不渲染任何内容
};

export default Message;