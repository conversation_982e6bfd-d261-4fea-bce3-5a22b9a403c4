const express = require('express');
const Order = require('../models/Order');
const Product = require('../models/Product');
const { authenticateToken, requireAdmin } = require('../middleware/auth');
const { validateOrder } = require('../middleware/validation');

const router = express.Router();

// 创建订单
router.post('/', authenticateToken, validateOrder, async (req, res) => {
  try {
    const { items, shippingAddress, paymentMethod, remark } = req.body;
    const userId = req.user._id;

    // 验证商品和计算总价
    let totalAmount = 0;
    const orderItems = [];

    for (const item of items) {
      const product = await Product.findById(item.product);
      if (!product || product.status !== 'active') {
        return res.status(400).json({ message: `商品 ${item.product} 不存在或已下架` });
      }

      // 查找对应的SKU
      const sku = product.skus.find(s => s.sku === item.sku);
      if (!sku) {
        return res.status(400).json({ message: `商品 ${product.name} 的规格不存在` });
      }

      // 检查库存
      if (sku.stock < item.quantity) {
        return res.status(400).json({ 
          message: `商品 ${product.name} 库存不足，当前库存：${sku.stock}` 
        });
      }

      const subtotal = sku.price * item.quantity;
      totalAmount += subtotal;

      orderItems.push({
        product: product._id,
        sku: sku.sku,
        name: product.name,
        image: product.images[0],
        specs: sku.specs,
        price: sku.price,
        quantity: item.quantity,
        subtotal
      });
    }

    // 计算运费（简单逻辑：满99免运费，否则10元）
    const shippingFee = totalAmount >= 99 ? 0 : 10;

    // 创建订单
    const order = new Order({
      user: userId,
      items: orderItems,
      shippingAddress,
      totalAmount,
      shippingFee,
      finalAmount: totalAmount + shippingFee,
      paymentMethod,
      remark
    });

    await order.save();

    // 减少库存
    for (const item of items) {
      await Product.updateOne(
        { _id: item.product, 'skus.sku': item.sku },
        { $inc: { 'skus.$.stock': -item.quantity } }
      );
    }

    await order.populate('user', 'username email');

    res.status(201).json({
      message: '订单创建成功',
      order
    });
  } catch (error) {
    console.error('创建订单错误:', error);
    res.status(500).json({ message: '创建订单失败' });
  }
});

// 获取用户订单列表
router.get('/my', authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 10, status } = req.query;
    const skip = (page - 1) * limit;
    const userId = req.user._id;

    const filter = { user: userId };
    if (status) filter.status = status;

    const orders = await Order.find(filter)
      .populate('items.product', 'name images')
      .select('-__v')
      .skip(skip)
      .limit(parseInt(limit))
      .sort({ createdAt: -1 });

    const total = await Order.countDocuments(filter);

    res.json({
      orders,
      pagination: {
        current: parseInt(page),
        pageSize: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('获取订单列表错误:', error);
    res.status(500).json({ message: '获取订单列表失败' });
  }
});

// 获取订单详情
router.get('/:orderNo', authenticateToken, async (req, res) => {
  try {
    const { orderNo } = req.params;
    const userId = req.user._id;

    const filter = { orderNo };
    if (req.user.role !== 'admin') {
      filter.user = userId;
    }

    const order = await Order.findOne(filter)
      .populate('user', 'username email')
      .populate('items.product', 'name images');

    if (!order) {
      return res.status(404).json({ message: '订单不存在' });
    }

    res.json({ order });
  } catch (error) {
    console.error('获取订单详情错误:', error);
    res.status(500).json({ message: '获取订单详情失败' });
  }
});

// 取消订单
router.put('/:orderNo/cancel', authenticateToken, async (req, res) => {
  try {
    const { orderNo } = req.params;
    const userId = req.user._id;

    const order = await Order.findOne({ orderNo, user: userId });
    if (!order) {
      return res.status(404).json({ message: '订单不存在' });
    }

    if (order.status !== 'pending') {
      return res.status(400).json({ message: '只能取消待付款的订单' });
    }

    // 恢复库存
    for (const item of order.items) {
      await Product.updateOne(
        { _id: item.product, 'skus.sku': item.sku },
        { $inc: { 'skus.$.stock': item.quantity } }
      );
    }

    order.status = 'cancelled';
    order.cancelledAt = new Date();
    await order.save();

    res.json({
      message: '订单取消成功',
      order
    });
  } catch (error) {
    console.error('取消订单错误:', error);
    res.status(500).json({ message: '取消订单失败' });
  }
});

// 确认收货
router.put('/:orderNo/confirm', authenticateToken, async (req, res) => {
  try {
    const { orderNo } = req.params;
    const userId = req.user._id;

    const order = await Order.findOne({ orderNo, user: userId });
    if (!order) {
      return res.status(404).json({ message: '订单不存在' });
    }

    if (order.status !== 'shipped') {
      return res.status(400).json({ message: '只能确认已发货的订单' });
    }

    order.status = 'delivered';
    order.deliveredAt = new Date();
    await order.save();

    res.json({
      message: '确认收货成功',
      order
    });
  } catch (error) {
    console.error('确认收货错误:', error);
    res.status(500).json({ message: '确认收货失败' });
  }
});

// 管理员：获取所有订单
router.get('/', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      status, 
      orderNo, 
      startDate, 
      endDate 
    } = req.query;
    const skip = (page - 1) * limit;

    const filter = {};
    if (status) filter.status = status;
    if (orderNo) filter.orderNo = { $regex: orderNo, $options: 'i' };
    if (startDate || endDate) {
      filter.createdAt = {};
      if (startDate) filter.createdAt.$gte = new Date(startDate);
      if (endDate) filter.createdAt.$lte = new Date(endDate);
    }

    const orders = await Order.find(filter)
      .populate('user', 'username email')
      .populate('items.product', 'name images')
      .select('-__v')
      .skip(skip)
      .limit(parseInt(limit))
      .sort({ createdAt: -1 });

    const total = await Order.countDocuments(filter);

    res.json({
      orders,
      pagination: {
        current: parseInt(page),
        pageSize: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('获取订单列表错误:', error);
    res.status(500).json({ message: '获取订单列表失败' });
  }
});

// 管理员：更新订单状态
router.put('/:orderNo/status', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { orderNo } = req.params;
    const { status, trackingNumber, courier } = req.body;

    const order = await Order.findOne({ orderNo });
    if (!order) {
      return res.status(404).json({ message: '订单不存在' });
    }

    const updateData = { status };
    const now = new Date();

    switch (status) {
      case 'shipped':
        updateData.shippedAt = now;
        if (trackingNumber) updateData.trackingNumber = trackingNumber;
        if (courier) updateData.courier = courier;
        break;
      case 'delivered':
        updateData.deliveredAt = now;
        break;
      case 'completed':
        updateData.completedAt = now;
        break;
      case 'cancelled':
        updateData.cancelledAt = now;
        // 恢复库存
        for (const item of order.items) {
          await Product.updateOne(
            { _id: item.product, 'skus.sku': item.sku },
            { $inc: { 'skus.$.stock': item.quantity } }
          );
        }
        break;
    }

    const updatedOrder = await Order.findOneAndUpdate(
      { orderNo },
      updateData,
      { new: true }
    ).populate('user', 'username email');

    res.json({
      message: '订单状态更新成功',
      order: updatedOrder
    });
  } catch (error) {
    console.error('更新订单状态错误:', error);
    res.status(500).json({ message: '更新订单状态失败' });
  }
});

module.exports = router;
