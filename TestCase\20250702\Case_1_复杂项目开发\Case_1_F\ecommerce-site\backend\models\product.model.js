const mongoose = require('mongoose');

const ProductSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, '请提供商品名称'],
    trim: true,
    maxlength: [100, '商品名称不能超过100个字符']
  },
  description: {
    type: String,
    required: [true, '请提供商品描述'],
    maxlength: [2000, '商品描述不能超过2000个字符']
  },
  price: {
    type: Number,
    required: [true, '请提供商品价格'],
    min: [0, '价格必须大于0']
  },
  originalPrice: {
    type: Number,
    min: [0, '原价必须大于0']
  },
  images: [{
    type: String,
    default: 'default.jpg'
  }],
  category: {
    type: mongoose.Schema.ObjectId,
    ref: 'Category',
    required: [true, '请选择商品分类']
  },
  stock: {
    type: Number,
    required: [true, '请提供商品库存'],
    min: [0, '库存不能为负数'],
    default: 0
  },
  sold: {
    type: Number,
    default: 0
  },
  rating: {
    type: Number,
    default: 0,
    min: [0, '最小评分为0'],
    max: [5, '最大评分为5']
  },
  numReviews: {
    type: Number,
    default: 0
  },
  isFeatured: {
    type: Boolean,
    default: false
  },
  isOnSale: {
    type: Boolean,
    default: false
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// 虚拟字段 - 折扣百分比
ProductSchema.virtual('discountPercentage').get(function() {
  if (!this.originalPrice || this.originalPrice <= this.price) return 0;
  return Math.round((1 - this.price / this.originalPrice) * 100);
});

// 索引以提高查询性能
ProductSchema.index({ name: 'text', description: 'text' });

module.exports = mongoose.model('Product', ProductSchema); 