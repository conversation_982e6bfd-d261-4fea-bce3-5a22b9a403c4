import React, { useContext } from 'react';
import { Navigate } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';
import { Spin } from 'antd';

// 私有路由组件，用于保护需要登录的页面
const PrivateRoute = ({ children }) => {
  const { isAuthenticated, loading } = useContext(AuthContext);

  // 如果正在加载认证状态，显示加载中
  if (loading) {
    return (
      <div className="flex-center" style={{ height: '100vh' }}>
        <Spin size="large" tip="加载中..." />
      </div>
    );
  }

  // 如果未登录，重定向到登录页
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  // 已登录，显示受保护的组件
  return children;
};

export default PrivateRoute; 