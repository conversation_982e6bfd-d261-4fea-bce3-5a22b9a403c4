@import '~antd/dist/reset.css';

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, 'Helvetica Neue',
    <PERSON><PERSON>, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: rgba(0, 0, 0, 0.85);
  background-color: #f0f2f5;
  font-size: 14px;
  line-height: 1.5;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.page-title {
  font-size: 24px;
  font-weight: 500;
  margin-bottom: 24px;
}

.text-center {
  text-align: center;
}

.mb-1 {
  margin-bottom: 8px;
}

.mb-2 {
  margin-bottom: 16px;
}

.mb-3 {
  margin-bottom: 24px;
}

.mt-1 {
  margin-top: 8px;
}

.mt-2 {
  margin-top: 16px;
}

.mt-3 {
  margin-top: 24px;
}

.pointer {
  cursor: pointer;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

/* 覆盖Ant Design的一些默认样式 */
.ant-layout {
  background: #fff;
}

.ant-layout-content {
  min-height: calc(100vh - 134px);
  padding: 24px 0;
}

.ant-card-cover img {
  height: 200px;
  object-fit: cover;
} 