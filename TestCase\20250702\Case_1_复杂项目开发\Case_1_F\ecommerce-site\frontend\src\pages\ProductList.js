import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  Row,
  Col,
  Card,
  Input,
  Select,
  Slider,
  Checkbox,
  Pagination,
  Typography,
  Button,
  Spin,
  Empty,
  Breadcrumb,
  Divider,
  Space
} from 'antd';
import { FilterOutlined, SearchOutlined, ReloadOutlined } from '@ant-design/icons';
import axios from 'axios';
import ProductCard from '../components/products/ProductCard';

const { Title } = Typography;
const { Option } = Select;
const { Search } = Input;

const ProductList = () => {
  const [products, setProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [totalProducts, setTotalProducts] = useState(0);
  const [priceRange, setPriceRange] = useState([0, 10000]);
  const [showInStock, setShowInStock] = useState(false);
  const [showOnSale, setShowOnSale] = useState(false);
  
  // URL参数处理
  const location = useLocation();
  const navigate = useNavigate();
  const queryParams = new URLSearchParams(location.search);
  
  const [filters, setFilters] = useState({
    page: parseInt(queryParams.get('page'), 10) || 1,
    limit: parseInt(queryParams.get('limit'), 10) || 12,
    search: queryParams.get('search') || '',
    category: queryParams.get('category') || '',
    sortBy: queryParams.get('sortBy') || 'createdAt:desc',
    minPrice: queryParams.get('minPrice') || '',
    maxPrice: queryParams.get('maxPrice') || '',
  });
  
  // 获取所有分类
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await axios.get('/categories');
        if (response.data.success) {
          setCategories(response.data.data);
        }
      } catch (err) {
        console.error('获取分类失败:', err);
      }
    };
    
    fetchCategories();
  }, []);
  
  // 获取商品
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // 构建查询参数
        const params = { ...filters };
        
        // 如果设置了只显示有库存的商品
        if (showInStock) {
          params.inStock = true;
        }
        
        // 如果设置了只显示特价商品
        if (showOnSale) {
          params.onSale = true;
        }
        
        // 发送请求
        const response = await axios.get('/products', { params });
        
        if (response.data.success) {
          setProducts(response.data.data);
          setTotalProducts(response.data.total);
        }
      } catch (err) {
        console.error('获取商品失败:', err);
        setError('加载商品失败，请稍后再试');
      } finally {
        setLoading(false);
      }
    };
    
    fetchProducts();
    
    // 更新URL参数
    const newQueryParams = new URLSearchParams();
    
    Object.keys(filters).forEach(key => {
      if (filters[key]) {
        newQueryParams.set(key, filters[key]);
      }
    });
    
    if (showInStock) newQueryParams.set('inStock', 'true');
    if (showOnSale) newQueryParams.set('onSale', 'true');
    
    navigate({ search: newQueryParams.toString() }, { replace: true });
    
  }, [filters, showInStock, showOnSale, navigate]);
  
  // 处理筛选器变化
  const handleFilterChange = (name, value) => {
    // 如果是改变页码，则更新page
    if (name === 'page') {
      setFilters(prev => ({
        ...prev,
        page: value
      }));
    }
    // 如果是改变排序，则更新sortBy并重置页码
    else if (name === 'sortBy') {
      setFilters(prev => ({
        ...prev,
        sortBy: value,
        page: 1
      }));
    }
    // 如果是改变分类，则更新category并重置页码
    else if (name === 'category') {
      setFilters(prev => ({
        ...prev,
        category: value,
        page: 1
      }));
    }
    // 如果是搜索，则更新search并重置页码
    else if (name === 'search') {
      setFilters(prev => ({
        ...prev,
        search: value,
        page: 1
      }));
    }
    // 如果是价格范围，则更新minPrice和maxPrice并重置页码
    else if (name === 'priceRange') {
      setPriceRange(value);
      setFilters(prev => ({
        ...prev,
        minPrice: value[0],
        maxPrice: value[1],
        page: 1
      }));
    }
  };
  
  // 重置筛选器
  const handleResetFilters = () => {
    setPriceRange([0, 10000]);
    setShowInStock(false);
    setShowOnSale(false);
    setFilters({
      page: 1,
      limit: 12,
      search: '',
      category: '',
      sortBy: 'createdAt:desc',
      minPrice: '',
      maxPrice: ''
    });
  };
  
  // 获取所选分类的名称
  const getSelectedCategoryName = () => {
    if (!filters.category) return '';
    const category = categories.find(c => c._id === filters.category);
    return category ? category.name : '';
  };
  
  // 获取排序方式的文本描述
  const getSortByText = () => {
    const sortMap = {
      'price:asc': '价格从低到高',
      'price:desc': '价格从高到低',
      'createdAt:desc': '最新上架',
      'sold:desc': '销量最高'
    };
    
    return sortMap[filters.sortBy] || '默认排序';
  };
  
  return (
    <div>
      {/* 面包屑导航 */}
      <Breadcrumb className="mb-2">
        <Breadcrumb.Item href="/">首页</Breadcrumb.Item>
        <Breadcrumb.Item>商品列表</Breadcrumb.Item>
        {filters.category && (
          <Breadcrumb.Item>{getSelectedCategoryName()}</Breadcrumb.Item>
        )}
      </Breadcrumb>
      
      <Title level={2} className="mb-3">
        {filters.category ? getSelectedCategoryName() : '全部商品'}
        {filters.search && <span> - 搜索: "{filters.search}"</span>}
      </Title>
      
      <Row gutter={[24, 24]}>
        {/* 左侧筛选区 */}
        <Col xs={24} md={6}>
          <Card title="筛选条件">
            <div className="mb-2">
              <Search
                placeholder="搜索商品"
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                onSearch={(value) => handleFilterChange('search', value)}
                style={{ width: '100%' }}
              />
            </div>
            
            <Divider />
            
            <div className="mb-2">
              <Title level={5}>商品分类</Title>
              <Select
                style={{ width: '100%' }}
                placeholder="选择分类"
                value={filters.category || undefined}
                onChange={(value) => handleFilterChange('category', value)}
                allowClear
              >
                {categories.map(category => (
                  <Option key={category._id} value={category._id}>
                    {category.name}
                  </Option>
                ))}
              </Select>
            </div>
            
            <Divider />
            
            <div className="mb-2">
              <Title level={5}>价格区间</Title>
              <Slider
                range
                min={0}
                max={10000}
                step={100}
                value={priceRange}
                onChange={(value) => handleFilterChange('priceRange', value)}
              />
              <div className="flex-between">
                <span>¥{priceRange[0]}</span>
                <span>¥{priceRange[1]}</span>
              </div>
            </div>
            
            <Divider />
            
            <div className="mb-2">
              <Title level={5}>其他筛选</Title>
              <Space direction="vertical">
                <Checkbox
                  checked={showInStock}
                  onChange={(e) => setShowInStock(e.target.checked)}
                >
                  只显示有库存
                </Checkbox>
                <Checkbox
                  checked={showOnSale}
                  onChange={(e) => setShowOnSale(e.target.checked)}
                >
                  只显示特价商品
                </Checkbox>
              </Space>
            </div>
            
            <Divider />
            
            <Button
              type="primary"
              icon={<ReloadOutlined />}
              onClick={handleResetFilters}
              block
            >
              重置筛选条件
            </Button>
          </Card>
        </Col>
        
        {/* 右侧商品展示区 */}
        <Col xs={24} md={18}>
          {/* 排序工具栏 */}
          <Card className="mb-3">
            <div className="flex-between">
              <div>
                <strong>排序方式: </strong>
                <Select
                  value={filters.sortBy}
                  onChange={(value) => handleFilterChange('sortBy', value)}
                  style={{ width: 150 }}
                >
                  <Option value="createdAt:desc">最新上架</Option>
                  <Option value="price:asc">价格从低到高</Option>
                  <Option value="price:desc">价格从高到低</Option>
                  <Option value="sold:desc">销量最高</Option>
                </Select>
              </div>
              <div>
                <span style={{ marginRight: 8 }}>共 {totalProducts} 件商品</span>
                <Select
                  value={filters.limit}
                  onChange={(value) => setFilters(prev => ({ ...prev, limit: value, page: 1 }))}
                  style={{ width: 80 }}
                >
                  <Option value={12}>12</Option>
                  <Option value={24}>24</Option>
                  <Option value={48}>48</Option>
                </Select>
                <span style={{ marginLeft: 8 }}>条/页</span>
              </div>
            </div>
          </Card>
          
          {/* 商品列表 */}
          {loading ? (
            <div className="text-center" style={{ padding: '50px 0' }}>
              <Spin size="large" tip="加载中..." />
            </div>
          ) : error ? (
            <Empty
              description={error}
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          ) : products.length === 0 ? (
            <Empty description="没有找到符合条件的商品" />
          ) : (
            <>
              <Row gutter={[16, 24]}>
                {products.map(product => (
                  <Col key={product._id} xs={24} sm={12} md={8} lg={6}>
                    <ProductCard product={product} />
                  </Col>
                ))}
              </Row>
              
              {/* 分页器 */}
              <div className="text-center mt-3">
                <Pagination
                  current={filters.page}
                  pageSize={filters.limit}
                  total={totalProducts}
                  onChange={(page) => handleFilterChange('page', page)}
                  showSizeChanger={false}
                />
              </div>
            </>
          )}
        </Col>
      </Row>
    </div>
  );
};

export default ProductList; 