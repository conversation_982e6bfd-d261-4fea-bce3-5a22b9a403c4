import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Layout, Menu, Input, Badge, Avatar, Dropdown, Button, Space } from 'antd';
import {
  SearchOutlined,
  ShoppingCartOutlined,
  UserOutlined,
  LoginOutlined,
  LogoutOutlined,
  ProfileOutlined,
  ShoppingOutlined,
} from '@ant-design/icons';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store';
import { logout } from '../../store/slices/authSlice';

const { Header: AntHeader } = Layout;
const { Search } = Input;

const Header: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { isAuthenticated, user } = useSelector((state: RootState) => state.auth);
  const { totalItems } = useSelector((state: RootState) => state.cart);
  const [searchValue, setSearchValue] = useState('');

  const handleSearch = (value: string) => {
    if (value.trim()) {
      navigate(`/products?search=${encodeURIComponent(value.trim())}`);
    }
  };

  const handleLogout = () => {
    dispatch(logout());
    navigate('/');
  };

  const userMenuItems = [
    {
      key: 'profile',
      icon: <ProfileOutlined />,
      label: '个人中心',
      onClick: () => navigate('/profile'),
    },
    {
      key: 'orders',
      icon: <ShoppingOutlined />,
      label: '我的订单',
      onClick: () => navigate('/orders'),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout,
    },
  ];

  const mainMenuItems = [
    {
      key: 'home',
      label: <Link to="/">首页</Link>,
    },
    {
      key: 'products',
      label: <Link to="/products">商品</Link>,
    },
  ];

  return (
    <AntHeader className="bg-white shadow-sm border-b">
      <div className="container mx-auto flex items-center justify-between h-16">
        {/* Logo */}
        <div className="flex items-center">
          <Link to="/" className="text-xl font-bold text-blue-600 mr-8">
            电商网站
          </Link>
          
          {/* 主导航 */}
          <Menu
            mode="horizontal"
            items={mainMenuItems}
            className="border-none bg-transparent"
            style={{ minWidth: 200 }}
          />
        </div>

        {/* 搜索框 */}
        <div className="flex-1 max-w-md mx-8">
          <Search
            placeholder="搜索商品"
            allowClear
            enterButton={<SearchOutlined />}
            size="large"
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
            onSearch={handleSearch}
          />
        </div>

        {/* 右侧操作区 */}
        <div className="flex items-center space-x-4">
          {/* 购物车 */}
          <Badge count={totalItems} size="small">
            <Button
              type="text"
              icon={<ShoppingCartOutlined />}
              size="large"
              onClick={() => navigate('/cart')}
              className="flex items-center"
            >
              购物车
            </Button>
          </Badge>

          {/* 用户区域 */}
          {isAuthenticated ? (
            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomRight"
              trigger={['click']}
            >
              <Button type="text" className="flex items-center">
                <Avatar
                  size="small"
                  src={user?.avatar}
                  icon={<UserOutlined />}
                  className="mr-2"
                />
                <span>{user?.username}</span>
              </Button>
            </Dropdown>
          ) : (
            <Space>
              <Button
                type="text"
                icon={<LoginOutlined />}
                onClick={() => navigate('/login')}
              >
                登录
              </Button>
              <Button
                type="primary"
                onClick={() => navigate('/register')}
              >
                注册
              </Button>
            </Space>
          )}
        </div>
      </div>
    </AntHeader>
  );
};

export default Header;
