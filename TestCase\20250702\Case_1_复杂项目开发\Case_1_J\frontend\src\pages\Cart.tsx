import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Row, Col, Card, Button, InputNumber, Typography, Space, Empty, Image, Divider } from 'antd';
import { DeleteOutlined, ShoppingOutlined } from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../store';
import { updateQuantity, removeFromCart, clearCart } from '../store/slices/cartSlice';

const { Title, Text } = Typography;

const Cart: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { items, totalItems, totalAmount } = useSelector((state: RootState) => state.cart);
  const { isAuthenticated } = useSelector((state: RootState) => state.auth);

  const handleQuantityChange = (productId: string, sku: string, quantity: number) => {
    dispatch(updateQuantity({ productId, sku, quantity }));
  };

  const handleRemoveItem = (productId: string, sku: string) => {
    dispatch(removeFromCart({ productId, sku }));
  };

  const handleClearCart = () => {
    dispatch(clearCart());
  };

  const handleCheckout = () => {
    if (!isAuthenticated) {
      navigate('/login', { state: { from: { pathname: '/checkout' } } });
      return;
    }
    navigate('/checkout');
  };

  const handleContinueShopping = () => {
    navigate('/products');
  };

  if (items.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          <Title level={2} className="mb-6">购物车</Title>
          <Card>
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description="购物车是空的"
            >
              <Button type="primary" onClick={handleContinueShopping}>
                去购物
              </Button>
            </Empty>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-6">
          <Title level={2}>购物车 ({totalItems})</Title>
          <Button type="text" danger onClick={handleClearCart}>
            清空购物车
          </Button>
        </div>

        <Row gutter={[24, 24]}>
          {/* 购物车商品列表 */}
          <Col xs={24} lg={16}>
            <Card>
              <Space direction="vertical" size="middle" className="w-full">
                {items.map((item) => (
                  <div key={`${item.productId}-${item.sku}`}>
                    <Row gutter={[16, 16]} align="middle">
                      {/* 商品图片 */}
                      <Col xs={6} sm={4}>
                        <Image
                          src={item.image}
                          alt={item.name}
                          className="w-full h-20 object-cover rounded"
                          preview={false}
                        />
                      </Col>

                      {/* 商品信息 */}
                      <Col xs={18} sm={12}>
                        <Space direction="vertical" size="small">
                          <Text strong className="text-base">
                            {item.name}
                          </Text>
                          {item.specs.length > 0 && (
                            <div>
                              {item.specs.map((spec, index) => (
                                <Text key={index} className="text-sm text-gray-500 mr-2">
                                  {spec.name}: {spec.value}
                                </Text>
                              ))}
                            </div>
                          )}
                          <Text className="text-red-500 font-bold">
                            ¥{item.price}
                          </Text>
                        </Space>
                      </Col>

                      {/* 数量控制 */}
                      <Col xs={12} sm={4}>
                        <Space direction="vertical" size="small" className="w-full">
                          <InputNumber
                            min={1}
                            max={item.stock}
                            value={item.quantity}
                            onChange={(value) => 
                              handleQuantityChange(item.productId, item.sku, value || 1)
                            }
                            className="w-full"
                          />
                          <Text className="text-xs text-gray-500">
                            库存 {item.stock}
                          </Text>
                        </Space>
                      </Col>

                      {/* 小计和删除 */}
                      <Col xs={12} sm={4}>
                        <Space direction="vertical" size="small" className="w-full text-right">
                          <Text strong className="text-lg">
                            ¥{(item.price * item.quantity).toFixed(2)}
                          </Text>
                          <Button
                            type="text"
                            danger
                            icon={<DeleteOutlined />}
                            onClick={() => handleRemoveItem(item.productId, item.sku)}
                          >
                            删除
                          </Button>
                        </Space>
                      </Col>
                    </Row>
                    <Divider />
                  </div>
                ))}
              </Space>
            </Card>
          </Col>

          {/* 订单摘要 */}
          <Col xs={24} lg={8}>
            <Card title="订单摘要" className="sticky top-4">
              <Space direction="vertical" size="middle" className="w-full">
                <div className="flex justify-between">
                  <Text>商品总数:</Text>
                  <Text>{totalItems} 件</Text>
                </div>
                
                <div className="flex justify-between">
                  <Text>商品总价:</Text>
                  <Text>¥{totalAmount.toFixed(2)}</Text>
                </div>

                <div className="flex justify-between">
                  <Text>运费:</Text>
                  <Text>{totalAmount >= 99 ? '免费' : '¥10.00'}</Text>
                </div>

                <Divider />

                <div className="flex justify-between">
                  <Text strong className="text-lg">总计:</Text>
                  <Text strong className="text-lg text-red-500">
                    ¥{(totalAmount + (totalAmount >= 99 ? 0 : 10)).toFixed(2)}
                  </Text>
                </div>

                {totalAmount < 99 && (
                  <Text className="text-sm text-gray-500">
                    再购买 ¥{(99 - totalAmount).toFixed(2)} 即可免运费
                  </Text>
                )}

                <Button
                  type="primary"
                  size="large"
                  block
                  onClick={handleCheckout}
                >
                  去结算
                </Button>

                <Button
                  type="default"
                  size="large"
                  block
                  icon={<ShoppingOutlined />}
                  onClick={handleContinueShopping}
                >
                  继续购物
                </Button>
              </Space>
            </Card>
          </Col>
        </Row>
      </div>
    </div>
  );
};

export default Cart;
