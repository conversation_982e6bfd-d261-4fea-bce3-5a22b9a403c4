const express = require('express');
const Product = require('../models/Product');
const Category = require('../models/Category');
const { authenticateToken, requireAdmin, optionalAuth } = require('../middleware/auth');
const { validateProduct } = require('../middleware/validation');

const router = express.Router();

// 获取商品列表（支持搜索、筛选、分页）
router.get('/', optionalAuth, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 12,
      search,
      category,
      brand,
      minPrice,
      maxPrice,
      sort = 'createdAt',
      order = 'desc'
    } = req.query;

    const skip = (page - 1) * limit;
    const filter = { status: 'active' };

    // 搜索
    if (search) {
      filter.$text = { $search: search };
    }

    // 分类筛选
    if (category) {
      filter.category = category;
    }

    // 品牌筛选
    if (brand) {
      filter.brand = { $regex: brand, $options: 'i' };
    }

    // 价格筛选
    if (minPrice || maxPrice) {
      filter.basePrice = {};
      if (minPrice) filter.basePrice.$gte = parseFloat(minPrice);
      if (maxPrice) filter.basePrice.$lte = parseFloat(maxPrice);
    }

    // 排序
    const sortObj = {};
    sortObj[sort] = order === 'desc' ? -1 : 1;

    const products = await Product.find(filter)
      .populate('category', 'name slug')
      .populate('createdBy', 'username')
      .select('-__v')
      .skip(skip)
      .limit(parseInt(limit))
      .sort(sortObj);

    const total = await Product.countDocuments(filter);

    res.json({
      products,
      pagination: {
        current: parseInt(page),
        pageSize: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('获取商品列表错误:', error);
    res.status(500).json({ message: '获取商品列表失败' });
  }
});

// 获取商品详情
router.get('/:id', optionalAuth, async (req, res) => {
  try {
    const product = await Product.findById(req.params.id)
      .populate('category', 'name slug')
      .populate('createdBy', 'username');

    if (!product) {
      return res.status(404).json({ message: '商品不存在' });
    }

    if (product.status !== 'active' && (!req.user || req.user.role !== 'admin')) {
      return res.status(404).json({ message: '商品不存在' });
    }

    res.json({ product });
  } catch (error) {
    console.error('获取商品详情错误:', error);
    res.status(500).json({ message: '获取商品详情失败' });
  }
});

// 创建商品（管理员）
router.post('/', authenticateToken, requireAdmin, validateProduct, async (req, res) => {
  try {
    const {
      name,
      description,
      category,
      brand,
      images,
      basePrice,
      originalPrice,
      skus,
      tags,
      weight,
      dimensions,
      seoTitle,
      seoDescription,
      seoKeywords
    } = req.body;

    // 验证分类是否存在
    const categoryExists = await Category.findById(category);
    if (!categoryExists) {
      return res.status(400).json({ message: '分类不存在' });
    }

    const product = new Product({
      name,
      description,
      category,
      brand,
      images,
      basePrice,
      originalPrice,
      skus: skus || [],
      tags: tags || [],
      weight,
      dimensions,
      seoTitle,
      seoDescription,
      seoKeywords: seoKeywords || [],
      createdBy: req.user._id
    });

    await product.save();
    await product.populate('category', 'name slug');

    res.status(201).json({
      message: '商品创建成功',
      product
    });
  } catch (error) {
    console.error('创建商品错误:', error);
    if (error.name === 'ValidationError') {
      return res.status(400).json({ message: '商品数据验证失败' });
    }
    res.status(500).json({ message: '创建商品失败' });
  }
});

// 更新商品（管理员）
router.put('/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const productId = req.params.id;
    const updateData = req.body;

    // 如果更新分类，验证分类是否存在
    if (updateData.category) {
      const categoryExists = await Category.findById(updateData.category);
      if (!categoryExists) {
        return res.status(400).json({ message: '分类不存在' });
      }
    }

    const product = await Product.findByIdAndUpdate(
      productId,
      updateData,
      { new: true, runValidators: true }
    ).populate('category', 'name slug');

    if (!product) {
      return res.status(404).json({ message: '商品不存在' });
    }

    res.json({
      message: '商品更新成功',
      product
    });
  } catch (error) {
    console.error('更新商品错误:', error);
    if (error.name === 'ValidationError') {
      return res.status(400).json({ message: '商品数据验证失败' });
    }
    res.status(500).json({ message: '更新商品失败' });
  }
});

// 删除商品（管理员）
router.delete('/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const product = await Product.findByIdAndDelete(req.params.id);

    if (!product) {
      return res.status(404).json({ message: '商品不存在' });
    }

    res.json({ message: '商品删除成功' });
  } catch (error) {
    console.error('删除商品错误:', error);
    res.status(500).json({ message: '删除商品失败' });
  }
});

// 获取热门商品
router.get('/featured/hot', async (req, res) => {
  try {
    const { limit = 8 } = req.query;

    const products = await Product.find({ status: 'active' })
      .populate('category', 'name slug')
      .select('name images basePrice originalPrice sales rating')
      .sort({ sales: -1 })
      .limit(parseInt(limit));

    res.json({ products });
  } catch (error) {
    console.error('获取热门商品错误:', error);
    res.status(500).json({ message: '获取热门商品失败' });
  }
});

// 获取推荐商品
router.get('/featured/recommended', async (req, res) => {
  try {
    const { limit = 8 } = req.query;

    const products = await Product.find({ status: 'active' })
      .populate('category', 'name slug')
      .select('name images basePrice originalPrice rating reviewCount')
      .sort({ rating: -1, reviewCount: -1 })
      .limit(parseInt(limit));

    res.json({ products });
  } catch (error) {
    console.error('获取推荐商品错误:', error);
    res.status(500).json({ message: '获取推荐商品失败' });
  }
});

module.exports = router;
