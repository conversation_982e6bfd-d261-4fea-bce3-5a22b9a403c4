import React, { useEffect } from 'react';
import { Layout, Menu } from 'antd';
import { Link, useLocation } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import {
  UserOutlined,
  ShoppingOutlined,
  HomeOutlined,
  HeartOutlined,
  SettingOutlined,
  DashboardOutlined,
  AppstoreOutlined,
  TagsOutlined,
  TeamOutlined,
  BarChartOutlined,
} from '@ant-design/icons';
import { toggleSidebar } from '../../redux/slices/uiSlice';

const { Sider } = Layout;

const Sidebar = () => {
  const dispatch = useDispatch();
  const location = useLocation();
  const { userInfo } = useSelector((state) => state.auth);
  const { sidebarCollapsed } = useSelector((state) => state.ui);

  // 根据路径确定当前选中的菜单项
  const getSelectedKey = () => {
    const path = location.pathname;
    if (path.startsWith('/profile')) return 'profile';
    if (path.startsWith('/orders')) return 'orders';
    if (path.startsWith('/addresses')) return 'addresses';
    if (path.startsWith('/wishlist')) return 'wishlist';
    if (path.startsWith('/settings')) return 'settings';
    if (path.startsWith('/admin/dashboard')) return 'admin-dashboard';
    if (path.startsWith('/admin/products')) return 'admin-products';
    if (path.startsWith('/admin/categories')) return 'admin-categories';
    if (path.startsWith('/admin/orders')) return 'admin-orders';
    if (path.startsWith('/admin/users')) return 'admin-users';
    return '';
  };

  // 用户菜单项
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: <Link to="/profile">个人中心</Link>,
    },
    {
      key: 'orders',
      icon: <ShoppingOutlined />,
      label: <Link to="/orders">我的订单</Link>,
    },
    {
      key: 'addresses',
      icon: <HomeOutlined />,
      label: <Link to="/addresses">地址管理</Link>,
    },
    {
      key: 'wishlist',
      icon: <HeartOutlined />,
      label: <Link to="/wishlist">收藏夹</Link>,
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: <Link to="/settings">账户设置</Link>,
    },
  ];

  // 管理员菜单项
  const adminMenuItems = [
    {
      key: 'admin-dashboard',
      icon: <DashboardOutlined />,
      label: <Link to="/admin/dashboard">控制面板</Link>,
    },
    {
      key: 'admin-products',
      icon: <AppstoreOutlined />,
      label: <Link to="/admin/products">商品管理</Link>,
    },
    {
      key: 'admin-categories',
      icon: <TagsOutlined />,
      label: <Link to="/admin/categories">分类管理</Link>,
    },
    {
      key: 'admin-orders',
      icon: <ShoppingOutlined />,
      label: <Link to="/admin/orders">订单管理</Link>,
    },
    {
      key: 'admin-users',
      icon: <TeamOutlined />,
      label: <Link to="/admin/users">用户管理</Link>,
    },
    {
      key: 'admin-stats',
      icon: <BarChartOutlined />,
      label: <Link to="/admin/stats">数据统计</Link>,
    },
  ];

  // 根据用户角色显示不同的菜单
  const menuItems = userInfo?.role === 'admin' ? adminMenuItems : userMenuItems;

  return (
    <Sider
      collapsible
      collapsed={sidebarCollapsed}
      onCollapse={() => dispatch(toggleSidebar())}
      className="app-sidebar"
      breakpoint="lg"
      width={200}
    >
      <Menu
        mode="inline"
        selectedKeys={[getSelectedKey()]}
        style={{ height: '100%', borderRight: 0 }}
        items={menuItems}
      />
    </Sider>
  );
};

export default Sidebar;