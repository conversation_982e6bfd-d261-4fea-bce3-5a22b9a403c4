const express = require('express');
const router = express.Router();
const { check } = require('express-validator');
const {
  register,
  login,
  getMe,
  updateDetails,
  updatePassword,
  logout
} = require('../controllers/auth.controller');
const { protect } = require('../middleware/auth.middleware');

// 用户注册
router.post(
  '/register',
  [
    check('username', '用户名是必须的').not().isEmpty(),
    check('email', '请提供有效的邮箱').isEmail(),
    check('password', '请提供至少6个字符的密码').isLength({ min: 6 })
  ],
  register
);

// 用户登录
router.post('/login', login);

// 获取当前用户信息 - 需要登录
router.get('/me', protect, getMe);

// 更新用户信息 - 需要登录
router.put('/updatedetails', protect, updateDetails);

// 更新密码 - 需要登录
router.put('/updatepassword', protect, updatePassword);

// 退出登录 - 需要登录
router.get('/logout', protect, logout);

module.exports = router; 