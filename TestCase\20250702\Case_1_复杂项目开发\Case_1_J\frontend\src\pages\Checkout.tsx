import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Row, Col, Card, Button, Radio, Form, Input, Typography, Space, Divider, message } from 'antd';
import { PlusOutlined, EditOutlined } from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../store';
import { fetchAddresses } from '../store/slices/userSlice';
import { createOrder, clearCurrentOrder } from '../store/slices/orderSlice';
import { clearCart } from '../store/slices/cartSlice';
import { Address } from '../store/slices/authSlice';

const { Title, Text } = Typography;
const { TextArea } = Input;

const Checkout: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { items, totalAmount } = useSelector((state: RootState) => state.cart);
  const { addresses } = useSelector((state: RootState) => state.user);
  const { loading } = useSelector((state: RootState) => state.order);
  
  const [selectedAddress, setSelectedAddress] = useState<Address | null>(null);
  const [paymentMethod, setPaymentMethod] = useState<'alipay' | 'wechat' | 'bank_card'>('alipay');
  const [remark, setRemark] = useState('');

  const shippingFee = totalAmount >= 99 ? 0 : 10;
  const finalAmount = totalAmount + shippingFee;

  useEffect(() => {
    dispatch(fetchAddresses() as any);
    dispatch(clearCurrentOrder());
  }, [dispatch]);

  useEffect(() => {
    if (addresses.length > 0) {
      // 选择默认地址或第一个地址
      const defaultAddress = addresses.find(addr => addr.isDefault) || addresses[0];
      setSelectedAddress(defaultAddress);
    }
  }, [addresses]);

  useEffect(() => {
    if (items.length === 0) {
      navigate('/cart');
    }
  }, [items, navigate]);

  const handleSubmitOrder = async () => {
    if (!selectedAddress) {
      message.error('请选择收货地址');
      return;
    }

    if (items.length === 0) {
      message.error('购物车为空');
      return;
    }

    try {
      const orderData = {
        items: items.map(item => ({
          product: item.productId,
          sku: item.sku,
          quantity: item.quantity,
        })),
        shippingAddress: {
          name: selectedAddress.name,
          phone: selectedAddress.phone,
          province: selectedAddress.province,
          city: selectedAddress.city,
          district: selectedAddress.district,
          detail: selectedAddress.detail,
        },
        paymentMethod,
        remark,
      };

      const result = await dispatch(createOrder(orderData) as any).unwrap();
      
      // 清空购物车
      dispatch(clearCart());
      
      // 跳转到支付页面
      navigate(`/payment/${result.orderNo}`);
    } catch (error) {
      // 错误已在slice中处理
    }
  };

  const handleAddAddress = () => {
    // 这里应该打开添加地址的模态框
    message.info('添加地址功能待实现');
  };

  const handleEditAddress = (address: Address) => {
    // 这里应该打开编辑地址的模态框
    message.info('编辑地址功能待实现');
  };

  if (items.length === 0) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <Title level={2} className="mb-6">确认订单</Title>

        <Row gutter={[24, 24]}>
          {/* 主要内容 */}
          <Col xs={24} lg={16}>
            {/* 收货地址 */}
            <Card title="收货地址" className="mb-6">
              {addresses.length > 0 ? (
                <Space direction="vertical" size="middle" className="w-full">
                  <Radio.Group
                    value={selectedAddress?._id}
                    onChange={(e) => {
                      const address = addresses.find(addr => addr._id === e.target.value);
                      setSelectedAddress(address || null);
                    }}
                    className="w-full"
                  >
                    {addresses.map((address) => (
                      <Radio key={address._id} value={address._id} className="w-full">
                        <div className="flex items-center justify-between w-full">
                          <div>
                            <div className="flex items-center space-x-2">
                              <Text strong>{address.name}</Text>
                              <Text>{address.phone}</Text>
                              {address.isDefault && (
                                <span className="bg-blue-100 text-blue-600 px-2 py-1 rounded text-xs">
                                  默认
                                </span>
                              )}
                            </div>
                            <div className="text-gray-600 mt-1">
                              {address.province} {address.city} {address.district} {address.detail}
                            </div>
                          </div>
                          <Button
                            type="text"
                            icon={<EditOutlined />}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleEditAddress(address);
                            }}
                          >
                            编辑
                          </Button>
                        </div>
                      </Radio>
                    ))}
                  </Radio.Group>
                  
                  <Button
                    type="dashed"
                    icon={<PlusOutlined />}
                    onClick={handleAddAddress}
                    block
                  >
                    添加新地址
                  </Button>
                </Space>
              ) : (
                <div className="text-center py-8">
                  <Text className="text-gray-500 mb-4 block">
                    您还没有收货地址
                  </Text>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={handleAddAddress}
                  >
                    添加收货地址
                  </Button>
                </div>
              )}
            </Card>

            {/* 商品清单 */}
            <Card title="商品清单" className="mb-6">
              <Space direction="vertical" size="middle" className="w-full">
                {items.map((item) => (
                  <div key={`${item.productId}-${item.sku}`}>
                    <Row gutter={[16, 16]} align="middle">
                      <Col xs={6} sm={3}>
                        <img
                          src={item.image}
                          alt={item.name}
                          className="w-full h-16 object-cover rounded"
                        />
                      </Col>
                      <Col xs={18} sm={15}>
                        <Space direction="vertical" size="small">
                          <Text strong>{item.name}</Text>
                          {item.specs.length > 0 && (
                            <div>
                              {item.specs.map((spec, index) => (
                                <Text key={index} className="text-sm text-gray-500 mr-2">
                                  {spec.name}: {spec.value}
                                </Text>
                              ))}
                            </div>
                          )}
                        </Space>
                      </Col>
                      <Col xs={12} sm={3}>
                        <Text>¥{item.price}</Text>
                      </Col>
                      <Col xs={12} sm={3}>
                        <Text>x{item.quantity}</Text>
                      </Col>
                    </Row>
                    <Divider />
                  </div>
                ))}
              </Space>
            </Card>

            {/* 支付方式 */}
            <Card title="支付方式" className="mb-6">
              <Radio.Group
                value={paymentMethod}
                onChange={(e) => setPaymentMethod(e.target.value)}
              >
                <Space direction="vertical">
                  <Radio value="alipay">
                    <div className="flex items-center">
                      <span className="text-blue-600 text-xl mr-2">💰</span>
                      支付宝
                    </div>
                  </Radio>
                  <Radio value="wechat">
                    <div className="flex items-center">
                      <span className="text-green-600 text-xl mr-2">💚</span>
                      微信支付
                    </div>
                  </Radio>
                  <Radio value="bank_card">
                    <div className="flex items-center">
                      <span className="text-gray-600 text-xl mr-2">💳</span>
                      银行卡
                    </div>
                  </Radio>
                </Space>
              </Radio.Group>
            </Card>

            {/* 订单备注 */}
            <Card title="订单备注">
              <TextArea
                placeholder="请输入订单备注（可选）"
                rows={3}
                value={remark}
                onChange={(e) => setRemark(e.target.value)}
                maxLength={200}
                showCount
              />
            </Card>
          </Col>

          {/* 订单摘要 */}
          <Col xs={24} lg={8}>
            <Card title="订单摘要" className="sticky top-4">
              <Space direction="vertical" size="middle" className="w-full">
                <div className="flex justify-between">
                  <Text>商品总价:</Text>
                  <Text>¥{totalAmount.toFixed(2)}</Text>
                </div>
                
                <div className="flex justify-between">
                  <Text>运费:</Text>
                  <Text>{shippingFee === 0 ? '免费' : `¥${shippingFee.toFixed(2)}`}</Text>
                </div>

                <Divider />

                <div className="flex justify-between">
                  <Text strong className="text-lg">应付总额:</Text>
                  <Text strong className="text-lg text-red-500">
                    ¥{finalAmount.toFixed(2)}
                  </Text>
                </div>

                <Button
                  type="primary"
                  size="large"
                  block
                  loading={loading}
                  onClick={handleSubmitOrder}
                  disabled={!selectedAddress}
                >
                  提交订单
                </Button>

                <div className="text-xs text-gray-500 text-center">
                  点击"提交订单"表示您同意相关服务条款
                </div>
              </Space>
            </Card>
          </Col>
        </Row>
      </div>
    </div>
  );
};

export default Checkout;
