import React, { useState, useEffect, useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Steps,
  Form,
  Input,
  Button,
  Select,
  Radio,
  Row,
  Col,
  Card,
  Typography,
  Table,
  Divider,
  message,
  Result,
  Spin
} from 'antd';
import {
  UserOutlined,
  CreditCardOutlined,
  CheckCircleOutlined,
  LoadingOutlined
} from '@ant-design/icons';
import axios from 'axios';
import { CartContext } from '../context/CartContext';
import { AuthContext } from '../context/AuthContext';

const { Title, Text } = Typography;
const { Step } = Steps;
const { Option } = Select;
const { TextArea } = Input;

const Checkout = () => {
  const { cart, clearCart } = useContext(CartContext);
  const { user } = useContext(AuthContext);
  const navigate = useNavigate();
  const [form] = Form.useForm();
  
  const [currentStep, setCurrentStep] = useState(0);
  const [shippingAddress, setShippingAddress] = useState(null);
  const [paymentMethod, setPaymentMethod] = useState(null);
  const [orderId, setOrderId] = useState(null);
  const [loading, setLoading] = useState(false);
  const [paymentMethods, setPaymentMethods] = useState([]);
  
  // 检查购物车是否为空
  useEffect(() => {
    if (cart.items.length === 0) {
      message.error('您的购物车是空的，无法结算');
      navigate('/cart');
    }
  }, [cart, navigate]);
  
  // 获取支付方式
  useEffect(() => {
    const fetchPaymentMethods = async () => {
      try {
        const response = await axios.get('/payment/methods');
        if (response.data.success) {
          setPaymentMethods(response.data.data);
        }
      } catch (err) {
        console.error('获取支付方式失败:', err);
      }
    };
    
    fetchPaymentMethods();
    
    // 如果用户已经登录，预填充地址信息
    if (user) {
      form.setFieldsValue({
        fullName: user.firstName && user.lastName ? `${user.firstName} ${user.lastName}` : user.username,
        phone: user.phoneNumber || '',
        address: user.address?.street || '',
        city: user.address?.city || '',
        postalCode: user.address?.zipCode || '',
        country: user.address?.country || '中国',
      });
    }
  }, [user, form]);
  
  // 处理订单提交
  const handleCreateOrder = async () => {
    try {
      setLoading(true);
      
      const response = await axios.post('/orders', {
        shippingAddress,
        paymentMethod
      });
      
      if (response.data.success) {
        setOrderId(response.data.data._id);
        // 清空购物车
        await clearCart();
        setCurrentStep(2);
      }
    } catch (err) {
      console.error('创建订单失败:', err);
      message.error(err.response?.data?.message || '创建订单失败，请稍后再试');
    } finally {
      setLoading(false);
    }
  };
  
  // 处理支付（模拟）
  const handlePayment = async () => {
    try {
      setLoading(true);
      
      // 模拟支付过程
      const paymentResponse = await axios.post('/payment/simulate-payment', {
        orderId,
        paymentStatus: 'success'
      });
      
      if (paymentResponse.data.success) {
        setCurrentStep(3);
      }
    } catch (err) {
      console.error('支付失败:', err);
      message.error(err.response?.data?.message || '支付失败，请稍后再试');
    } finally {
      setLoading(false);
    }
  };
  
  // 处理地址表单提交
  const handleAddressSubmit = (values) => {
    setShippingAddress(values);
    setCurrentStep(1);
  };
  
  // 处理支付方式选择
  const handlePaymentMethodSelect = (value) => {
    setPaymentMethod(value);
  };
  
  // 处理返回购物车
  const handleBackToCart = () => {
    navigate('/cart');
  };
  
  // 表格列定义
  const columns = [
    {
      title: '商品',
      dataIndex: 'product',
      key: 'product',
      render: (product) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <img
            src={product.images && product.images.length > 0 ? product.images[0] : 'https://via.placeholder.com/50'}
            alt={product.name}
            style={{ width: 50, height: 50, objectFit: 'cover', marginRight: 10 }}
          />
          <Text>{product.name}</Text>
        </div>
      ),
    },
    {
      title: '单价',
      dataIndex: 'price',
      key: 'price',
      render: (price) => <Text>¥{price.toFixed(2)}</Text>,
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
    },
    {
      title: '小计',
      key: 'subtotal',
      render: (_, record) => (
        <Text>¥{(record.price * record.quantity).toFixed(2)}</Text>
      ),
    },
  ];
  
  // 订单摘要
  const OrderSummary = () => (
    <Card title="订单摘要" className="mb-3">
      <Table
        columns={columns}
        dataSource={cart.items.map((item, index) => ({ ...item, key: index }))}
        pagination={false}
        size="small"
      />
      <Divider />
      <Row>
        <Col span={12} offset={12}>
          <div className="flex-between mb-2">
            <Text>商品总价:</Text>
            <Text>¥{cart.totalPrice.toFixed(2)}</Text>
          </div>
          <div className="flex-between mb-2">
            <Text>配送费:</Text>
            <Text>¥{cart.totalPrice >= 99 ? 0 : 10}.00</Text>
          </div>
          <div className="flex-between mb-2">
            <Text>税费:</Text>
            <Text>¥{(cart.totalPrice * 0.05).toFixed(2)}</Text>
          </div>
          <Divider />
          <div className="flex-between">
            <Text strong>总计:</Text>
            <Text style={{ color: '#ff4d4f', fontSize: '20px', fontWeight: 'bold' }}>
              ¥{(cart.totalPrice + (cart.totalPrice >= 99 ? 0 : 10) + cart.totalPrice * 0.05).toFixed(2)}
            </Text>
          </div>
        </Col>
      </Row>
    </Card>
  );
  
  return (
    <div>
      <Title level={2}>
        结算
      </Title>
      
      <Steps current={currentStep} className="mb-3">
        <Step title="收货地址" icon={<UserOutlined />} />
        <Step title="支付方式" icon={<CreditCardOutlined />} />
        <Step title="完成" icon={<CheckCircleOutlined />} />
      </Steps>
      
      <Row gutter={24}>
        <Col xs={24} lg={16}>
          {/* 步骤1：填写收货地址 */}
          {currentStep === 0 && (
            <Card title="收货地址" className="mb-3">
              <Form
                form={form}
                layout="vertical"
                onFinish={handleAddressSubmit}
              >
                <Row gutter={16}>
                  <Col xs={24} sm={12}>
                    <Form.Item
                      name="fullName"
                      label="收货人姓名"
                      rules={[{ required: true, message: '请输入收货人姓名' }]}
                    >
                      <Input placeholder="请输入收货人姓名" />
                    </Form.Item>
                  </Col>
                  <Col xs={24} sm={12}>
                    <Form.Item
                      name="phoneNumber"
                      label="联系电话"
                      rules={[{ required: true, message: '请输入联系电话' }]}
                    >
                      <Input placeholder="请输入联系电话" />
                    </Form.Item>
                  </Col>
                </Row>
                
                <Form.Item
                  name="address"
                  label="详细地址"
                  rules={[{ required: true, message: '请输入详细地址' }]}
                >
                  <TextArea rows={2} placeholder="请输入详细地址" />
                </Form.Item>
                
                <Row gutter={16}>
                  <Col xs={24} sm={12}>
                    <Form.Item
                      name="city"
                      label="城市"
                      rules={[{ required: true, message: '请输入城市' }]}
                    >
                      <Input placeholder="请输入城市" />
                    </Form.Item>
                  </Col>
                  <Col xs={24} sm={12}>
                    <Form.Item
                      name="postalCode"
                      label="邮政编码"
                      rules={[{ required: true, message: '请输入邮政编码' }]}
                    >
                      <Input placeholder="请输入邮政编码" />
                    </Form.Item>
                  </Col>
                </Row>
                
                <Form.Item
                  name="country"
                  label="国家/地区"
                  initialValue="中国"
                >
                  <Select placeholder="请选择国家/地区">
                    <Option value="中国">中国</Option>
                    <Option value="美国">美国</Option>
                    <Option value="加拿大">加拿大</Option>
                    <Option value="日本">日本</Option>
                    <Option value="韩国">韩国</Option>
                  </Select>
                </Form.Item>
                
                <Form.Item>
                  <div className="flex-between">
                    <Button type="default" onClick={handleBackToCart}>
                      返回购物车
                    </Button>
                    <Button type="primary" htmlType="submit">
                      下一步
                    </Button>
                  </div>
                </Form.Item>
              </Form>
            </Card>
          )}
          
          {/* 步骤2：选择支付方式 */}
          {currentStep === 1 && (
            <Card title="支付方式" className="mb-3">
              <Radio.Group
                onChange={(e) => handlePaymentMethodSelect(e.target.value)}
                value={paymentMethod}
                style={{ width: '100%' }}
              >
                {paymentMethods.map((method) => (
                  <Radio.Button
                    key={method.id}
                    value={method.id}
                    style={{
                      display: 'block',
                      height: '60px',
                      lineHeight: '60px',
                      marginBottom: '10px',
                      borderRadius: '4px',
                      width: '100%',
                      paddingLeft: '20px'
                    }}
                  >
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <div style={{ marginLeft: '10px' }}>
                        <div>{method.name}</div>
                        <div style={{ fontSize: '12px', color: '#999' }}>{method.description}</div>
                      </div>
                    </div>
                  </Radio.Button>
                ))}
              </Radio.Group>
              
              <Divider />
              
              <div className="flex-between">
                <Button type="default" onClick={() => setCurrentStep(0)}>
                  返回
                </Button>
                <Button
                  type="primary"
                  onClick={handleCreateOrder}
                  disabled={!paymentMethod || loading}
                >
                  {loading ? <LoadingOutlined /> : null}
                  提交订单
                </Button>
              </div>
            </Card>
          )}
          
          {/* 步骤3：订单确认 */}
          {currentStep === 2 && (
            <Card title="订单确认" className="mb-3">
              <Result
                status="success"
                title="订单已创建成功！"
                subTitle={`订单号: ${orderId}`}
                extra={[
                  <Button
                    type="primary"
                    key="pay"
                    onClick={handlePayment}
                    loading={loading}
                  >
                    立即支付
                  </Button>
                ]}
              />
            </Card>
          )}
          
          {/* 步骤4：支付完成 */}
          {currentStep === 3 && (
            <Card className="mb-3">
              <Result
                status="success"
                title="支付成功！"
                subTitle={`订单号: ${orderId}`}
                extra={[
                  <Button
                    type="primary"
                    key="orders"
                    onClick={() => navigate('/orders')}
                  >
                    查看订单
                  </Button>,
                  <Button
                    key="home"
                    onClick={() => navigate('/')}
                  >
                    返回首页
                  </Button>
                ]}
              />
            </Card>
          )}
        </Col>
        
        {/* 订单摘要，在步骤1-2显示 */}
        <Col xs={24} lg={8}>
          {(currentStep === 0 || currentStep === 1) && (
            <OrderSummary />
          )}
        </Col>
      </Row>
    </div>
  );
};

export default Checkout; 