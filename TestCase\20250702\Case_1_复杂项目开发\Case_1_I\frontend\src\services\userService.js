import apiClient from './apiClient';

// 获取所有用户（管理员）
const getUsers = async (pageNumber = 1, keyword = '', token) => {
  const response = await apiClient.get('/users', {
    params: {
      page: pageNumber,
      keyword,
    },
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

// 获取用户详情（管理员）
const getUserDetails = async (userId, token) => {
  const response = await apiClient.get(`/users/${userId}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

// 更新用户信息（管理员）
const updateUser = async (userId, userData, token) => {
  const response = await apiClient.put(`/users/${userId}`, userData, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

// 删除用户（管理员）
const deleteUser = async (userId, token) => {
  const response = await apiClient.delete(`/users/${userId}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

// 上传用户头像
const uploadAvatar = async (formData, token) => {
  const response = await apiClient.post('/users/avatar', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

// 获取用户地址列表
const getUserAddresses = async (token) => {
  const response = await apiClient.get('/users/addresses', {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

// 添加用户地址
const addUserAddress = async (addressData, token) => {
  const response = await apiClient.post('/users/addresses', addressData, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

// 更新用户地址
const updateUserAddress = async (addressId, addressData, token) => {
  const response = await apiClient.put(
    `/users/addresses/${addressId}`,
    addressData,
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    }
  );
  return response.data;
};

// 删除用户地址
const deleteUserAddress = async (addressId, token) => {
  const response = await apiClient.delete(`/users/addresses/${addressId}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

// 设置默认地址
const setDefaultAddress = async (addressId, token) => {
  const response = await apiClient.put(
    `/users/addresses/${addressId}/default`,
    {},
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    }
  );
  return response.data;
};

const userService = {
  getUsers,
  getUserDetails,
  updateUser,
  deleteUser,
  uploadAvatar,
  getUserAddresses,
  addUserAddress,
  updateUserAddress,
  deleteUserAddress,
  setDefaultAddress,
};

export default userService;