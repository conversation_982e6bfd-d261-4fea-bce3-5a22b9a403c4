import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { message } from 'antd';

export interface CartItem {
  productId: string;
  sku: string;
  name: string;
  image: string;
  price: number;
  quantity: number;
  specs: Array<{ name: string; value: string }>;
  stock: number;
}

interface CartState {
  items: CartItem[];
  totalItems: number;
  totalAmount: number;
  loading: boolean;
}

const initialState: CartState = {
  items: [],
  totalItems: 0,
  totalAmount: 0,
  loading: false,
};

// 计算购物车总计
const calculateTotals = (items: CartItem[]) => {
  const totalItems = items.reduce((sum, item) => sum + item.quantity, 0);
  const totalAmount = items.reduce((sum, item) => sum + item.price * item.quantity, 0);
  return { totalItems, totalAmount };
};

// 异步actions
export const loadCart = createAsyncThunk(
  'cart/loadCart',
  async () => {
    // 从localStorage加载购物车数据
    const cartData = localStorage.getItem('cart');
    return cartData ? JSON.parse(cartData) : [];
  }
);

export const saveCart = createAsyncThunk(
  'cart/saveCart',
  async (items: CartItem[]) => {
    // 保存到localStorage
    localStorage.setItem('cart', JSON.stringify(items));
    return items;
  }
);

const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    addToCart: (state, action: PayloadAction<CartItem>) => {
      const newItem = action.payload;
      const existingItem = state.items.find(
        item => item.productId === newItem.productId && item.sku === newItem.sku
      );

      if (existingItem) {
        // 如果商品已存在，增加数量
        const newQuantity = existingItem.quantity + newItem.quantity;
        if (newQuantity <= newItem.stock) {
          existingItem.quantity = newQuantity;
          message.success('商品已添加到购物车');
        } else {
          message.warning('库存不足');
          return;
        }
      } else {
        // 如果是新商品，直接添加
        if (newItem.quantity <= newItem.stock) {
          state.items.push(newItem);
          message.success('商品已添加到购物车');
        } else {
          message.warning('库存不足');
          return;
        }
      }

      // 重新计算总计
      const totals = calculateTotals(state.items);
      state.totalItems = totals.totalItems;
      state.totalAmount = totals.totalAmount;

      // 保存到localStorage
      localStorage.setItem('cart', JSON.stringify(state.items));
    },

    updateQuantity: (state, action: PayloadAction<{ productId: string; sku: string; quantity: number }>) => {
      const { productId, sku, quantity } = action.payload;
      const item = state.items.find(
        item => item.productId === productId && item.sku === sku
      );

      if (item) {
        if (quantity <= 0) {
          // 如果数量为0或负数，移除商品
          state.items = state.items.filter(
            item => !(item.productId === productId && item.sku === sku)
          );
          message.success('商品已从购物车移除');
        } else if (quantity <= item.stock) {
          item.quantity = quantity;
        } else {
          message.warning('库存不足');
          return;
        }

        // 重新计算总计
        const totals = calculateTotals(state.items);
        state.totalItems = totals.totalItems;
        state.totalAmount = totals.totalAmount;

        // 保存到localStorage
        localStorage.setItem('cart', JSON.stringify(state.items));
      }
    },

    removeFromCart: (state, action: PayloadAction<{ productId: string; sku: string }>) => {
      const { productId, sku } = action.payload;
      state.items = state.items.filter(
        item => !(item.productId === productId && item.sku === sku)
      );

      // 重新计算总计
      const totals = calculateTotals(state.items);
      state.totalItems = totals.totalItems;
      state.totalAmount = totals.totalAmount;

      // 保存到localStorage
      localStorage.setItem('cart', JSON.stringify(state.items));
      message.success('商品已从购物车移除');
    },

    clearCart: (state) => {
      state.items = [];
      state.totalItems = 0;
      state.totalAmount = 0;
      localStorage.removeItem('cart');
      message.success('购物车已清空');
    },

    selectItems: (state, action: PayloadAction<Array<{ productId: string; sku: string; selected: boolean }>>) => {
      // 这里可以添加选择商品的逻辑，用于结算时选择部分商品
      // 暂时不实现，简化处理
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(loadCart.pending, (state) => {
        state.loading = true;
      })
      .addCase(loadCart.fulfilled, (state, action) => {
        state.loading = false;
        state.items = action.payload;
        const totals = calculateTotals(state.items);
        state.totalItems = totals.totalItems;
        state.totalAmount = totals.totalAmount;
      })
      .addCase(loadCart.rejected, (state) => {
        state.loading = false;
      })
      .addCase(saveCart.fulfilled, (state, action) => {
        state.items = action.payload;
        const totals = calculateTotals(state.items);
        state.totalItems = totals.totalItems;
        state.totalAmount = totals.totalAmount;
      });
  },
});

export const {
  addToCart,
  updateQuantity,
  removeFromCart,
  clearCart,
  selectItems,
} = cartSlice.actions;

export default cartSlice.reducer;
