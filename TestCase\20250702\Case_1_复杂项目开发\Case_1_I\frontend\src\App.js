import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Layout, ConfigProvider } from 'antd';
import { useSelector } from 'react-redux';

// 布局组件
import Header from './components/layout/Header';
import Footer from './components/layout/Footer';
import Sidebar from './components/layout/Sidebar';

// 页面组件
import HomePage from './pages/HomePage';
import LoginPage from './pages/auth/LoginPage';
import RegisterPage from './pages/auth/RegisterPage';
import ProductListPage from './pages/product/ProductListPage';
import ProductDetailPage from './pages/product/ProductDetailPage';
import CartPage from './pages/cart/CartPage';
import CheckoutPage from './pages/checkout/CheckoutPage';
import OrderListPage from './pages/order/OrderListPage';
import OrderDetailPage from './pages/order/OrderDetailPage';
import ProfilePage from './pages/user/ProfilePage';
import AddressPage from './pages/user/AddressPage';
import NotFoundPage from './pages/NotFoundPage';

// 管理员页面
import AdminDashboard from './pages/admin/Dashboard';
import AdminProductList from './pages/admin/product/ProductList';
import AdminProductEdit from './pages/admin/product/ProductEdit';
import AdminCategoryList from './pages/admin/category/CategoryList';
import AdminCategoryEdit from './pages/admin/category/CategoryEdit';
import AdminOrderList from './pages/admin/order/OrderList';
import AdminOrderDetail from './pages/admin/order/OrderDetail';
import AdminUserList from './pages/admin/user/UserList';

// 路由保护组件
import ProtectedRoute from './components/routes/ProtectedRoute';
import AdminRoute from './components/routes/AdminRoute';

const { Content } = Layout;

function App() {
  const { userInfo } = useSelector((state) => state.auth);
  
  return (
    <ConfigProvider
      theme={{
        token: {
          colorPrimary: '#1890ff',
        },
      }}
    >
      <Router>
        <Layout className="app-container">
          <Header />
          <Layout>
            {userInfo && <Sidebar />}
            <Content className="main-content">
              <Routes>
                {/* 公共路由 */}
                <Route path="/" element={<HomePage />} />
                <Route path="/login" element={<LoginPage />} />
                <Route path="/register" element={<RegisterPage />} />
                <Route path="/products" element={<ProductListPage />} />
                <Route path="/product/:id" element={<ProductDetailPage />} />
                
                {/* 需要登录的路由 */}
                <Route element={<ProtectedRoute />}>
                  <Route path="/cart" element={<CartPage />} />
                  <Route path="/checkout" element={<CheckoutPage />} />
                  <Route path="/orders" element={<OrderListPage />} />
                  <Route path="/order/:id" element={<OrderDetailPage />} />
                  <Route path="/profile" element={<ProfilePage />} />
                  <Route path="/addresses" element={<AddressPage />} />
                </Route>
                
                {/* 管理员路由 */}
                <Route element={<AdminRoute />}>
                  <Route path="/admin/dashboard" element={<AdminDashboard />} />
                  <Route path="/admin/products" element={<AdminProductList />} />
                  <Route path="/admin/product/:id?" element={<AdminProductEdit />} />
                  <Route path="/admin/categories" element={<AdminCategoryList />} />
                  <Route path="/admin/category/:id?" element={<AdminCategoryEdit />} />
                  <Route path="/admin/orders" element={<AdminOrderList />} />
                  <Route path="/admin/order/:id" element={<AdminOrderDetail />} />
                  <Route path="/admin/users" element={<AdminUserList />} />
                </Route>
                
                {/* 404页面 */}
                <Route path="*" element={<NotFoundPage />} />
              </Routes>
            </Content>
          </Layout>
          <Footer />
        </Layout>
      </Router>
    </ConfigProvider>
  );
}

export default App;