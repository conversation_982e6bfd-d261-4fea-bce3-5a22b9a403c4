import React, { useState } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { Form, Input, Button, Card, Typography, Divider, Space } from 'antd';
import { UserOutlined, LockOutlined, MailOutlined } from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../store';
import { login } from '../store/slices/authSlice';

const { Title, Text } = Typography;

interface LoginForm {
  email: string;
  password: string;
}

const Login: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const { loading } = useSelector((state: RootState) => state.auth);
  const [form] = Form.useForm();

  const from = (location.state as any)?.from?.pathname || '/';

  const onFinish = async (values: LoginForm) => {
    try {
      await dispatch(login(values) as any).unwrap();
      navigate(from, { replace: true });
    } catch (error) {
      // 错误已在slice中处理
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full">
        <Card className="shadow-lg">
          <div className="text-center mb-8">
            <Title level={2}>登录账户</Title>
            <Text className="text-gray-600">
              欢迎回来，请登录您的账户
            </Text>
          </div>

          <Form
            form={form}
            name="login"
            onFinish={onFinish}
            layout="vertical"
            size="large"
          >
            <Form.Item
              name="email"
              label="邮箱"
              rules={[
                { required: true, message: '请输入邮箱' },
                { type: 'email', message: '请输入有效的邮箱地址' },
              ]}
            >
              <Input
                prefix={<MailOutlined />}
                placeholder="请输入邮箱"
                autoComplete="email"
              />
            </Form.Item>

            <Form.Item
              name="password"
              label="密码"
              rules={[
                { required: true, message: '请输入密码' },
                { min: 6, message: '密码长度至少6位' },
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="请输入密码"
                autoComplete="current-password"
              />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                className="w-full"
                loading={loading}
              >
                登录
              </Button>
            </Form.Item>
          </Form>

          <Divider>或</Divider>

          <div className="text-center">
            <Space direction="vertical" size="middle">
              <Text className="text-gray-600">
                还没有账户？
                <Link to="/register" className="text-blue-600 hover:text-blue-500 ml-1">
                  立即注册
                </Link>
              </Text>
              
              <Link to="/" className="text-blue-600 hover:text-blue-500">
                返回首页
              </Link>
            </Space>
          </div>
        </Card>

        {/* 演示账户信息 */}
        <Card className="mt-4 bg-blue-50 border-blue-200">
          <div className="text-center">
            <Title level={5} className="text-blue-800 mb-2">
              演示账户
            </Title>
            <Space direction="vertical" size="small">
              <Text className="text-blue-700">
                邮箱: <EMAIL>
              </Text>
              <Text className="text-blue-700">
                密码: 123456
              </Text>
              <Button
                type="link"
                size="small"
                onClick={() => {
                  form.setFieldsValue({
                    email: '<EMAIL>',
                    password: '123456',
                  });
                }}
              >
                一键填入
              </Button>
            </Space>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default Login;
