const User = require('../models/User');
const { generateToken } = require('../utils/jwt');
const { successResponse, errorResponse } = require('../utils/apiResponse');
const { AppError } = require('../middleware/error');

/**
 * 用户注册
 * @route POST /api/auth/register
 * @access Public
 */
exports.register = async (req, res, next) => {
  try {
    const { name, email, password, phone } = req.body;

    // 检查邮箱是否已存在
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return errorResponse(res, '该邮箱已被注册', 400);
    }

    // 创建新用户
    const user = await User.create({
      name,
      email,
      password,
      phone,
    });

    // 生成JWT令牌
    const token = generateToken(user._id);

    // 返回用户信息（不包含密码）
    const userData = {
      _id: user._id,
      name: user.name,
      email: user.email,
      phone: user.phone,
      role: user.role,
    };

    return successResponse(
      res,
      { user: userData, token },
      '注册成功',
      201
    );
  } catch (error) {
    next(error);
  }
};

/**
 * 用户登录
 * @route POST /api/auth/login
 * @access Public
 */
exports.login = async (req, res, next) => {
  try {
    const { email, password } = req.body;

    // 检查是否提供了邮箱和密码
    if (!email || !password) {
      return errorResponse(res, '请提供邮箱和密码', 400);
    }

    // 查找用户并获取密码字段
    const user = await User.findOne({ email }).select('+password');

    // 检查用户是否存在及密码是否正确
    if (!user || !(await user.comparePassword(password))) {
      return errorResponse(res, '邮箱或密码不正确', 401);
    }

    // 检查用户是否被禁用
    if (!user.isActive) {
      return errorResponse(res, '您的账户已被禁用，请联系管理员', 403);
    }

    // 生成JWT令牌
    const token = generateToken(user._id);

    // 返回用户信息（不包含密码）
    const userData = {
      _id: user._id,
      name: user.name,
      email: user.email,
      phone: user.phone,
      role: user.role,
    };

    return successResponse(res, { user: userData, token }, '登录成功');
  } catch (error) {
    next(error);
  }
};

/**
 * 获取当前用户信息
 * @route GET /api/auth/me
 * @access Private
 */
exports.getCurrentUser = async (req, res, next) => {
  try {
    const user = await User.findById(req.user._id);

    if (!user) {
      return errorResponse(res, '用户不存在', 404);
    }

    return successResponse(res, { user }, '获取用户信息成功');
  } catch (error) {
    next(error);
  }
};

/**
 * 修改密码
 * @route PUT /api/auth/change-password
 * @access Private
 */
exports.changePassword = async (req, res, next) => {
  try {
    const { currentPassword, newPassword } = req.body;

    // 获取当前用户（包含密码字段）
    const user = await User.findById(req.user._id).select('+password');

    // 验证当前密码
    if (!(await user.comparePassword(currentPassword))) {
      return errorResponse(res, '当前密码不正确', 401);
    }

    // 更新密码
    user.password = newPassword;
    await user.save();

    // 生成新的JWT令牌
    const token = generateToken(user._id);

    return successResponse(res, { token }, '密码修改成功');
  } catch (error) {
    next(error);
  }
};

/**
 * 忘记密码（发送重置链接）
 * @route POST /api/auth/forgot-password
 * @access Public
 */
exports.forgotPassword = async (req, res, next) => {
  try {
    const { email } = req.body;

    // 查找用户
    const user = await User.findOne({ email });
    if (!user) {
      return errorResponse(res, '该邮箱未注册', 404);
    }

    // 在实际应用中，这里应该生成重置令牌并发送邮件
    // 为了简化，我们只返回成功消息
    return successResponse(
      res,
      {},
      '密码重置链接已发送到您的邮箱，请查收'
    );
  } catch (error) {
    next(error);
  }
};

/**
 * 重置密码
 * @route POST /api/auth/reset-password/:token
 * @access Public
 */
exports.resetPassword = async (req, res, next) => {
  try {
    // 在实际应用中，这里应该验证重置令牌并允许用户设置新密码
    // 为了简化，我们只返回成功消息
    return successResponse(res, {}, '密码重置成功，请使用新密码登录');
  } catch (error) {
    next(error);
  }
};

/**
 * 退出登录
 * @route POST /api/auth/logout
 * @access Private
 */
exports.logout = (req, res) => {
  // JWT是无状态的，客户端只需删除令牌即可
  // 这里我们只返回成功消息
  return successResponse(res, {}, '退出登录成功');
};