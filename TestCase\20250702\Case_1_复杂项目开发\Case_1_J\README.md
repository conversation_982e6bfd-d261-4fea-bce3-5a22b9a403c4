# 电商网站项目

一个完整的电商网站，包含前后端分离架构，具备用户管理、商品管理、订单系统和支付功能。

## 技术栈

### 后端
- Node.js + Express
- MongoDB + Mongoose
- JWT 认证
- 支付宝/微信支付集成

### 前端
- React + TypeScript
- Ant Design UI组件库
- Axios HTTP客户端
- React Router

## 项目结构

```
├── backend/          # 后端服务
│   ├── src/
│   │   ├── controllers/  # 控制器
│   │   ├── models/       # 数据模型
│   │   ├── routes/       # 路由
│   │   ├── middleware/   # 中间件
│   │   ├── services/     # 业务逻辑
│   │   └── utils/        # 工具函数
│   ├── package.json
│   └── server.js
├── frontend/         # 前端应用
│   ├── src/
│   │   ├── components/   # 组件
│   │   ├── pages/        # 页面
│   │   ├── services/     # API服务
│   │   ├── store/        # 状态管理
│   │   └── utils/        # 工具函数
│   ├── package.json
│   └── public/
└── docs/            # 文档
```

## 核心功能

1. **用户管理**
   - 用户注册/登录
   - 个人信息管理
   - 地址管理

2. **商品管理**
   - 商品展示
   - 分类浏览
   - 搜索功能

3. **订单系统**
   - 购物车管理
   - 订单创建
   - 订单状态跟踪

4. **支付功能**
   - 支付宝支付
   - 微信支付
   - 支付状态回调

## 快速开始

### 方式一：使用启动脚本（推荐）
```bash
# 给脚本执行权限
chmod +x scripts/start.sh

# 运行启动脚本
./scripts/start.sh
```

### 方式二：手动启动

#### 1. 启动数据库
确保 MongoDB 正在运行，或使用 Docker：
```bash
docker run -d -p 27017:27017 --name mongodb mongo:6.0
```

#### 2. 后端启动
```bash
cd backend
npm install
cp .env.example .env  # 复制并配置环境变量
npm run dev           # 开发模式
# 或
npm start            # 生产模式
```

#### 3. 前端启动
```bash
cd frontend
npm install
cp .env.example .env  # 复制并配置环境变量
npm start
```

#### 4. 初始化数据（可选）
```bash
# 插入示例商品数据
cd scripts
node seed-data.js
```

### 方式三：使用 Docker Compose
```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 停止服务
docker-compose down
```

## 访问地址

- **前端应用**: http://localhost:3000
- **后端API**: http://localhost:3001
- **API健康检查**: http://localhost:3001/health
- **MongoDB**: localhost:27017

## 默认账户

### 管理员账户
- 邮箱: <EMAIL>
- 密码: admin123

### 演示账户
- 邮箱: <EMAIL>
- 密码: 123456

## API文档

### 认证接口
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `GET /api/auth/me` - 获取当前用户信息
- `POST /api/auth/logout` - 用户登出

### 用户接口
- `GET /api/users/profile` - 获取用户资料
- `PUT /api/users/profile` - 更新用户资料
- `GET /api/users/addresses` - 获取用户地址
- `POST /api/users/addresses` - 添加用户地址
- `PUT /api/users/addresses/:id` - 更新用户地址
- `DELETE /api/users/addresses/:id` - 删除用户地址

### 商品接口
- `GET /api/products` - 获取商品列表
- `GET /api/products/:id` - 获取商品详情
- `GET /api/products/featured/hot` - 获取热门商品
- `GET /api/products/featured/recommended` - 获取推荐商品

### 分类接口
- `GET /api/categories` - 获取分类列表（树形）
- `GET /api/categories/flat` - 获取分类列表（扁平）
- `GET /api/categories/:id` - 获取分类详情

### 订单接口
- `POST /api/orders` - 创建订单
- `GET /api/orders/my` - 获取用户订单列表
- `GET /api/orders/:orderNo` - 获取订单详情
- `PUT /api/orders/:orderNo/cancel` - 取消订单
- `PUT /api/orders/:orderNo/confirm` - 确认收货

### 支付接口
- `POST /api/payments/create` - 创建支付订单
- `GET /api/payments/status/:orderNo` - 查询支付状态
- `POST /api/payments/refund` - 申请退款
- `POST /api/payments/alipay/notify` - 支付宝回调
- `POST /api/payments/wechat/notify` - 微信支付回调

## 项目特性

### 后端特性
- ✅ RESTful API 设计
- ✅ JWT 身份认证
- ✅ 数据验证和错误处理
- ✅ 文件上传支持
- ✅ 支付集成（支付宝/微信）
- ✅ 订单状态管理
- ✅ 库存管理
- ✅ 分页和搜索
- ✅ 安全中间件
- ✅ 请求限制
- ✅ 日志记录

### 前端特性
- ✅ React + TypeScript
- ✅ Redux Toolkit 状态管理
- ✅ React Router 路由
- ✅ Ant Design UI组件
- ✅ 响应式设计
- ✅ 购物车功能
- ✅ 用户认证
- ✅ 订单管理
- ✅ 支付流程
- ✅ 商品搜索和筛选
- ✅ 图片预览
- ✅ 表单验证

### 核心功能
- ✅ 用户注册/登录
- ✅ 商品浏览和搜索
- ✅ 购物车管理
- ✅ 订单创建和管理
- ✅ 支付流程
- ✅ 用户个人中心
- ✅ 地址管理
- ✅ 订单状态跟踪

## 技术栈详情

### 后端技术栈
- **运行时**: Node.js 18+
- **框架**: Express.js
- **数据库**: MongoDB + Mongoose
- **认证**: JWT
- **验证**: express-validator
- **安全**: helmet, cors, express-rate-limit
- **文件上传**: multer
- **日志**: morgan
- **压缩**: compression

### 前端技术栈
- **框架**: React 18
- **语言**: TypeScript
- **状态管理**: Redux Toolkit + Redux Persist
- **路由**: React Router v6
- **UI组件**: Ant Design
- **样式**: CSS + Tailwind CSS
- **HTTP客户端**: Axios
- **日期处理**: dayjs
- **工具库**: lodash

### 开发工具
- **包管理**: npm
- **代码规范**: ESLint + Prettier
- **构建工具**: Create React App
- **容器化**: Docker + Docker Compose
- **反向代理**: Nginx

## 部署说明

### 开发环境
1. 确保安装 Node.js 18+ 和 MongoDB
2. 运行启动脚本或手动启动服务
3. 访问 http://localhost:3000

### 生产环境
1. 使用 Docker Compose 部署：
```bash
docker-compose -f docker-compose.yml up -d
```

2. 或手动部署：
```bash
# 构建前端
cd frontend
npm run build

# 启动后端
cd ../backend
npm start

# 配置 Nginx 反向代理
```

### 环境变量配置

#### 后端环境变量 (.env)
```env
NODE_ENV=production
MONGODB_URI=mongodb://localhost:27017/ecommerce
JWT_SECRET=your_jwt_secret_key
PORT=3001
ALIPAY_APP_ID=your_alipay_app_id
WECHAT_APP_ID=your_wechat_app_id
```

#### 前端环境变量 (.env)
```env
REACT_APP_API_URL=http://localhost:3001/api
REACT_APP_ENV=production
```

## 开发指南

### 添加新功能
1. 后端：在 `backend/src/routes/` 添加路由
2. 前端：在 `frontend/src/pages/` 添加页面
3. 状态管理：在 `frontend/src/store/slices/` 添加slice
4. API服务：在 `frontend/src/services/` 添加服务

### 数据库模型
- User: 用户模型
- Product: 商品模型
- Category: 分类模型
- Order: 订单模型

### 代码规范
- 使用 TypeScript 进行类型检查
- 遵循 ESLint 规则
- 组件使用函数式组件 + Hooks
- API 使用 RESTful 设计

## 常见问题

### Q: 如何重置数据库？
A: 删除 MongoDB 中的 ecommerce 数据库，重新运行初始化脚本

### Q: 支付功能如何测试？
A: 当前为模拟支付，实际项目需要配置真实的支付宝/微信支付参数

### Q: 如何添加新的商品分类？
A: 使用管理员账户登录，通过 API 或直接在数据库中添加

### Q: 前端如何代理后端API？
A: 在 package.json 中配置了 proxy，开发时会自动代理到后端

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！
