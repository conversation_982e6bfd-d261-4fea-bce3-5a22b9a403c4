const Product = require('../models/Product');
const { successResponse, errorResponse, paginatedResponse } = require('../utils/apiResponse');

/**
 * 创建商品
 * @route POST /api/products
 * @access Private/Admin
 */
exports.createProduct = async (req, res, next) => {
  try {
    const {
      name,
      description,
      price,
      originalPrice,
      category,
      stock,
      images,
      specifications,
      isFeatured,
    } = req.body;

    // 创建新商品
    const product = await Product.create({
      name,
      description,
      price,
      originalPrice,
      category,
      stock,
      images: images || [],
      specifications: specifications || {},
      isFeatured: isFeatured || false,
    });

    return successResponse(res, { product }, '商品创建成功', 201);
  } catch (error) {
    next(error);
  }
};

/**
 * 获取所有商品
 * @route GET /api/products
 * @access Public
 */
exports.getAllProducts = async (req, res, next) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // 查询条件
    const query = { isActive: true };

    // 名称搜索
    if (req.query.name) {
      query.name = { $regex: req.query.name, $options: 'i' };
    }

    // 分类筛选
    if (req.query.category) {
      query.category = req.query.category;
    }

    // 价格范围筛选
    if (req.query.minPrice || req.query.maxPrice) {
      query.price = {};
      if (req.query.minPrice) {
        query.price.$gte = parseFloat(req.query.minPrice);
      }
      if (req.query.maxPrice) {
        query.price.$lte = parseFloat(req.query.maxPrice);
      }
    }

    // 特色商品筛选
    if (req.query.isFeatured === 'true') {
      query.isFeatured = true;
    }

    // 排序
    let sort = { createdAt: -1 }; // 默认按创建时间降序
    if (req.query.sort) {
      switch (req.query.sort) {
        case 'price_asc':
          sort = { price: 1 };
          break;
        case 'price_desc':
          sort = { price: -1 };
          break;
        case 'name_asc':
          sort = { name: 1 };
          break;
        case 'name_desc':
          sort = { name: -1 };
          break;
        case 'rating_desc':
          sort = { rating: -1 };
          break;
      }
    }

    // 执行查询
    const products = await Product.find(query)
      .populate('category', 'name')
      .select('-reviews')
      .sort(sort)
      .skip(skip)
      .limit(limit);

    // 获取总数
    const total = await Product.countDocuments(query);

    return paginatedResponse(
      res,
      products,
      page,
      limit,
      total,
      '获取商品列表成功'
    );
  } catch (error) {
    next(error);
  }
};

/**
 * 获取单个商品
 * @route GET /api/products/:id
 * @access Public
 */
exports.getProductById = async (req, res, next) => {
  try {
    const product = await Product.findById(req.params.id)
      .populate('category', 'name')
      .populate('reviews.user', 'name avatar');

    if (!product) {
      return errorResponse(res, '商品不存在', 404);
    }

    return successResponse(res, { product }, '获取商品信息成功');
  } catch (error) {
    next(error);
  }
};

/**
 * 更新商品
 * @route PUT /api/products/:id
 * @access Private/Admin
 */
exports.updateProduct = async (req, res, next) => {
  try {
    const {
      name,
      description,
      price,
      originalPrice,
      category,
      stock,
      images,
      specifications,
      isFeatured,
      isActive,
    } = req.body;

    const product = await Product.findById(req.params.id);

    if (!product) {
      return errorResponse(res, '商品不存在', 404);
    }

    // 更新字段
    if (name !== undefined) product.name = name;
    if (description !== undefined) product.description = description;
    if (price !== undefined) product.price = price;
    if (originalPrice !== undefined) product.originalPrice = originalPrice;
    if (category !== undefined) product.category = category;
    if (stock !== undefined) product.stock = stock;
    if (images !== undefined) product.images = images;
    if (specifications !== undefined) product.specifications = specifications;
    if (isFeatured !== undefined) product.isFeatured = isFeatured;
    if (isActive !== undefined) product.isActive = isActive;

    const updatedProduct = await product.save();

    return successResponse(
      res,
      { product: updatedProduct },
      '商品更新成功'
    );
  } catch (error) {
    next(error);
  }
};

/**
 * 删除商品
 * @route DELETE /api/products/:id
 * @access Private/Admin
 */
exports.deleteProduct = async (req, res, next) => {
  try {
    const product = await Product.findById(req.params.id);

    if (!product) {
      return errorResponse(res, '商品不存在', 404);
    }

    await product.remove();

    return successResponse(res, {}, '商品删除成功');
  } catch (error) {
    next(error);
  }
};

/**
 * 上传商品图片
 * @route POST /api/products/:id/images
 * @access Private/Admin
 */
exports.uploadProductImages = async (req, res, next) => {
  try {
    if (!req.files || req.files.length === 0) {
      return errorResponse(res, '请选择要上传的图片', 400);
    }

    const product = await Product.findById(req.params.id);

    if (!product) {
      return errorResponse(res, '商品不存在', 404);
    }

    // 获取上传的图片路径
    const imagePaths = req.files.map(
      (file) => `/uploads/products/${file.filename}`
    );

    // 添加到商品图片数组
    product.images = [...product.images, ...imagePaths];
    await product.save();

    return successResponse(
      res,
      { images: product.images },
      '商品图片上传成功'
    );
  } catch (error) {
    next(error);
  }
};

/**
 * 删除商品图片
 * @route DELETE /api/products/:id/images/:imageIndex
 * @access Private/Admin
 */
exports.deleteProductImage = async (req, res, next) => {
  try {
    const { id, imageIndex } = req.params;
    const index = parseInt(imageIndex);

    const product = await Product.findById(id);

    if (!product) {
      return errorResponse(res, '商品不存在', 404);
    }

    if (index < 0 || index >= product.images.length) {
      return errorResponse(res, '图片索引无效', 400);
    }

    // 删除指定索引的图片
    product.images.splice(index, 1);
    await product.save();

    return successResponse(
      res,
      { images: product.images },
      '商品图片删除成功'
    );
  } catch (error) {
    next(error);
  }
};

/**
 * 添加商品评价
 * @route POST /api/products/:id/reviews
 * @access Private
 */
exports.addProductReview = async (req, res, next) => {
  try {
    const { rating, comment } = req.body;
    const productId = req.params.id;

    const product = await Product.findById(productId);

    if (!product) {
      return errorResponse(res, '商品不存在', 404);
    }

    // 检查用户是否已经评价过该商品
    const alreadyReviewed = product.reviews.find(
      (review) => review.user.toString() === req.user._id.toString()
    );

    if (alreadyReviewed) {
      return errorResponse(res, '您已经评价过该商品', 400);
    }

    // 创建新评价
    const review = {
      user: req.user._id,
      rating: Number(rating),
      comment,
    };

    // 添加到评价数组
    product.reviews.push(review);

    // 更新评分
    product.calculateAverageRating();

    await product.save();

    return successResponse(res, { product }, '评价添加成功');
  } catch (error) {
    next(error);
  }
};

/**
 * 获取商品评价
 * @route GET /api/products/:id/reviews
 * @access Public
 */
exports.getProductReviews = async (req, res, next) => {
  try {
    const product = await Product.findById(req.params.id)
      .select('reviews rating numReviews')
      .populate('reviews.user', 'name avatar');

    if (!product) {
      return errorResponse(res, '商品不存在', 404);
    }

    return successResponse(
      res,
      {
        reviews: product.reviews,
        rating: product.rating,
        numReviews: product.numReviews,
      },
      '获取评价成功'
    );
  } catch (error) {
    next(error);
  }
};