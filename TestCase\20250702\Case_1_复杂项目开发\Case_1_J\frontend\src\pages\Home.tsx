import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Row, Col, Card, Button, Typography, Carousel, Space } from 'antd';
import { ShoppingCartOutlined, EyeOutlined } from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../store';
import { fetchHotProducts, fetchRecommendedProducts } from '../store/slices/productSlice';
import { addToCart } from '../store/slices/cartSlice';
import { Product } from '../store/slices/productSlice';

const { Title, Text } = Typography;
const { Meta } = Card;

const Home: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { hotProducts, recommendedProducts, loading } = useSelector((state: RootState) => state.product);

  useEffect(() => {
    dispatch(fetchHotProducts(8) as any);
    dispatch(fetchRecommendedProducts(8) as any);
  }, [dispatch]);

  const handleAddToCart = (product: Product) => {
    if (product.skus && product.skus.length > 0) {
      const firstSku = product.skus[0];
      dispatch(addToCart({
        productId: product._id,
        sku: firstSku.sku,
        name: product.name,
        image: product.images[0],
        price: firstSku.price,
        quantity: 1,
        specs: firstSku.specs,
        stock: firstSku.stock,
      }));
    }
  };

  const handleViewProduct = (productId: string) => {
    navigate(`/products/${productId}`);
  };

  const renderProductCard = (product: Product) => (
    <Card
      key={product._id}
      hoverable
      className="product-card"
      cover={
        <img
          alt={product.name}
          src={product.images[0] || '/placeholder.jpg'}
          className="product-image"
          onClick={() => handleViewProduct(product._id)}
        />
      }
      actions={[
        <Button
          type="text"
          icon={<EyeOutlined />}
          onClick={() => handleViewProduct(product._id)}
        >
          查看
        </Button>,
        <Button
          type="text"
          icon={<ShoppingCartOutlined />}
          onClick={() => handleAddToCart(product)}
          disabled={product.totalStock === 0}
        >
          {product.totalStock === 0 ? '缺货' : '加购物车'}
        </Button>,
      ]}
    >
      <Meta
        title={
          <div className="truncate" title={product.name}>
            {product.name}
          </div>
        }
        description={
          <Space direction="vertical" size="small" className="w-full">
            <div className="flex items-center justify-between">
              <span className="price">¥{product.basePrice}</span>
              {product.originalPrice && product.originalPrice > product.basePrice && (
                <span className="original-price">¥{product.originalPrice}</span>
              )}
            </div>
            <div className="flex items-center justify-between text-sm text-gray-500">
              <span>销量: {product.sales}</span>
              <span>评分: {product.rating.toFixed(1)}</span>
            </div>
          </Space>
        }
      />
    </Card>
  );

  // 轮播图数据
  const bannerData = [
    {
      id: 1,
      image: 'https://via.placeholder.com/1200x400/1890ff/ffffff?text=Banner+1',
      title: '春季新品上市',
      subtitle: '全场8折优惠',
    },
    {
      id: 2,
      image: 'https://via.placeholder.com/1200x400/52c41a/ffffff?text=Banner+2',
      title: '品质生活',
      subtitle: '精选好物推荐',
    },
    {
      id: 3,
      image: 'https://via.placeholder.com/1200x400/fa8c16/ffffff?text=Banner+3',
      title: '限时特惠',
      subtitle: '错过再等一年',
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 轮播图 */}
      <div className="mb-8">
        <Carousel autoplay>
          {bannerData.map((banner) => (
            <div key={banner.id}>
              <div
                className="h-96 bg-cover bg-center flex items-center justify-center"
                style={{ backgroundImage: `url(${banner.image})` }}
              >
                <div className="text-center text-white">
                  <Title level={1} className="text-white mb-4">
                    {banner.title}
                  </Title>
                  <Text className="text-xl text-white">
                    {banner.subtitle}
                  </Text>
                </div>
              </div>
            </div>
          ))}
        </Carousel>
      </div>

      <div className="container mx-auto px-4">
        {/* 热门商品 */}
        <section className="mb-12">
          <div className="flex items-center justify-between mb-6">
            <Title level={2}>热门商品</Title>
            <Button type="link" onClick={() => navigate('/products')}>
              查看更多 →
            </Button>
          </div>
          <Row gutter={[16, 16]}>
            {hotProducts.map((product) => (
              <Col key={product._id} xs={12} sm={8} md={6} lg={6}>
                {renderProductCard(product)}
              </Col>
            ))}
          </Row>
        </section>

        {/* 推荐商品 */}
        <section className="mb-12">
          <div className="flex items-center justify-between mb-6">
            <Title level={2}>为您推荐</Title>
            <Button type="link" onClick={() => navigate('/products')}>
              查看更多 →
            </Button>
          </div>
          <Row gutter={[16, 16]}>
            {recommendedProducts.map((product) => (
              <Col key={product._id} xs={12} sm={8} md={6} lg={6}>
                {renderProductCard(product)}
              </Col>
            ))}
          </Row>
        </section>

        {/* 服务特色 */}
        <section className="mb-12">
          <Row gutter={[32, 32]} className="text-center">
            <Col xs={24} sm={12} md={6}>
              <div className="p-6">
                <div className="text-4xl text-blue-500 mb-4">🚚</div>
                <Title level={4}>免费配送</Title>
                <Text className="text-gray-600">满99元免费配送</Text>
              </div>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <div className="p-6">
                <div className="text-4xl text-green-500 mb-4">🔒</div>
                <Title level={4}>安全支付</Title>
                <Text className="text-gray-600">多种支付方式，安全可靠</Text>
              </div>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <div className="p-6">
                <div className="text-4xl text-orange-500 mb-4">↩️</div>
                <Title level={4}>7天退换</Title>
                <Text className="text-gray-600">7天无理由退换货</Text>
              </div>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <div className="p-6">
                <div className="text-4xl text-purple-500 mb-4">🎧</div>
                <Title level={4}>24小时客服</Title>
                <Text className="text-gray-600">专业客服在线服务</Text>
              </div>
            </Col>
          </Row>
        </section>
      </div>
    </div>
  );
};

export default Home;
