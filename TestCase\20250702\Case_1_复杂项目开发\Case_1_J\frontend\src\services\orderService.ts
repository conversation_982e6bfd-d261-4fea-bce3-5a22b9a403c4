import { request } from './api';
import { Order, OrderItem, ShippingAddress } from '../store/slices/orderSlice';

interface CreateOrderData {
  items: Array<{
    product: string;
    sku: string;
    quantity: number;
  }>;
  shippingAddress: ShippingAddress;
  paymentMethod: 'alipay' | 'wechat' | 'bank_card';
  remark?: string;
}

interface OrderListResponse {
  orders: Order[];
  pagination: {
    current: number;
    pageSize: number;
    total: number;
    pages: number;
  };
}

interface OrderDetailResponse {
  order: Order;
}

interface OrderListParams {
  page?: number;
  limit?: number;
  status?: string;
}

class OrderService {
  // 创建订单
  async createOrder(orderData: CreateOrderData): Promise<{ message: string; order: Order }> {
    return request.post('/orders', orderData);
  }

  // 获取用户订单列表
  async getOrders(params: OrderListParams = {}): Promise<OrderListResponse> {
    const queryParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const queryString = queryParams.toString();
    const url = queryString ? `/orders/my?${queryString}` : '/orders/my';
    
    return request.get(url);
  }

  // 获取订单详情
  async getOrderByNo(orderNo: string): Promise<OrderDetailResponse> {
    return request.get(`/orders/${orderNo}`);
  }

  // 取消订单
  async cancelOrder(orderNo: string): Promise<{ message: string; order: Order }> {
    return request.put(`/orders/${orderNo}/cancel`);
  }

  // 确认收货
  async confirmOrder(orderNo: string): Promise<{ message: string; order: Order }> {
    return request.put(`/orders/${orderNo}/confirm`);
  }

  // 根据状态获取订单
  async getOrdersByStatus(status: string, params: Omit<OrderListParams, 'status'> = {}): Promise<OrderListResponse> {
    return this.getOrders({ ...params, status });
  }

  // 获取待付款订单
  async getPendingOrders(params: Omit<OrderListParams, 'status'> = {}): Promise<OrderListResponse> {
    return this.getOrdersByStatus('pending', params);
  }

  // 获取已付款订单
  async getPaidOrders(params: Omit<OrderListParams, 'status'> = {}): Promise<OrderListResponse> {
    return this.getOrdersByStatus('paid', params);
  }

  // 获取已发货订单
  async getShippedOrders(params: Omit<OrderListParams, 'status'> = {}): Promise<OrderListResponse> {
    return this.getOrdersByStatus('shipped', params);
  }

  // 获取已完成订单
  async getCompletedOrders(params: Omit<OrderListParams, 'status'> = {}): Promise<OrderListResponse> {
    return this.getOrdersByStatus('completed', params);
  }

  // 获取已取消订单
  async getCancelledOrders(params: Omit<OrderListParams, 'status'> = {}): Promise<OrderListResponse> {
    return this.getOrdersByStatus('cancelled', params);
  }
}

export default new OrderService();
