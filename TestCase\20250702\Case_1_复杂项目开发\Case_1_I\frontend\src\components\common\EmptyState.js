import React from 'react';
import { Empty, Button } from 'antd';
import { Link } from 'react-router-dom';

const EmptyState = ({
  image = Empty.PRESENTED_IMAGE_SIMPLE,
  description = '暂无数据',
  buttonText,
  buttonLink,
  onButtonClick,
}) => {
  const renderButton = () => {
    if (!buttonText) return null;

    if (buttonLink) {
      return (
        <Button type="primary">
          <Link to={buttonLink}>{buttonText}</Link>
        </Button>
      );
    }

    if (onButtonClick) {
      return (
        <Button type="primary" onClick={onButtonClick}>
          {buttonText}
        </Button>
      );
    }

    return null;
  };

  return (
    <div className="empty-state-container">
      <Empty image={image} description={description}>
        {renderButton()}
      </Empty>
    </div>
  );
};

export default EmptyState;