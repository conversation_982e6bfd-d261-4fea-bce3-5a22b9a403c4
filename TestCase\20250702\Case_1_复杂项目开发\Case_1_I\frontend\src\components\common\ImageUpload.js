import React, { useState } from 'react';
import { Upload, message, Button } from 'antd';
import { UploadOutlined, LoadingOutlined, PlusOutlined } from '@ant-design/icons';

const ImageUpload = ({
  onUpload,
  maxCount = 1,
  listType = 'picture-card',
  buttonText = '上传图片',
  accept = 'image/*',
  showUploadList = true,
  initialFileList = [],
}) => {
  const [loading, setLoading] = useState(false);
  const [fileList, setFileList] = useState(initialFileList);

  // 上传前检查文件
  const beforeUpload = (file) => {
    // 检查文件类型
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error('只能上传图片文件!');
      return false;
    }

    // 检查文件大小 (小于 5MB)
    const isLt5M = file.size / 1024 / 1024 < 5;
    if (!isLt5M) {
      message.error('图片必须小于 5MB!');
      return false;
    }

    return true;
  };

  // 自定义上传
  const customUpload = async ({ file, onSuccess, onError }) => {
    setLoading(true);
    try {
      // 调用传入的上传函数
      const result = await onUpload(file);
      onSuccess(result, file);
      message.success('上传成功');
    } catch (error) {
      onError(error);
      message.error('上传失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理文件列表变化
  const handleChange = ({ fileList: newFileList }) => {
    setFileList(newFileList);
  };

  // 上传按钮
  const uploadButton = (
    <div>
      {loading ? <LoadingOutlined /> : <PlusOutlined />}
      <div style={{ marginTop: 8 }}>{buttonText}</div>
    </div>
  );

  return (
    <Upload
      listType={listType}
      fileList={fileList}
      beforeUpload={beforeUpload}
      customRequest={customUpload}
      onChange={handleChange}
      maxCount={maxCount}
      accept={accept}
      showUploadList={showUploadList}
    >
      {listType === 'picture-card' ? (
        fileList.length >= maxCount ? null : uploadButton
      ) : (
        <Button icon={<UploadOutlined />} loading={loading}>
          {buttonText}
        </Button>
      )}
    </Upload>
  );
};

export default ImageUpload;