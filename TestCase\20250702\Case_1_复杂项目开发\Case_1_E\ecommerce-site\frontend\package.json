{"name": "ecommerce-frontend", "version": "0.1.0", "private": true, "dependencies": {"@reduxjs/toolkit": "^1.9.5", "@stripe/react-stripe-js": "^2.1.0", "@stripe/stripe-js": "^1.52.1", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3", "axios": "^1.3.6", "bootstrap": "^5.2.3", "react": "^18.2.0", "react-bootstrap": "^2.7.4", "react-dom": "^18.2.0", "react-helmet-async": "^1.3.0", "react-icons": "^4.8.0", "react-redux": "^8.0.5", "react-router-bootstrap": "^0.26.2", "react-router-dom": "^6.10.0", "react-scripts": "5.0.1", "react-toastify": "^9.1.2", "web-vitals": "^3.3.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000"}