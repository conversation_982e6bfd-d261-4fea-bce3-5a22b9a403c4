import React, { useState } from 'react';
import { Row, Col, Typography, Button, InputNumber, Tabs, Divider, Image, Space } from 'antd';
import { ShoppingCartOutlined, HeartOutlined, HeartFilled } from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import Price from '../common/Price';
import Rating from '../common/Rating';
import { addToCart } from '../../redux/slices/cartSlice';
import { formatDate } from '../../utils/formatUtils';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

const ProductDetail = ({ product, onAddToWishlist, inWishlist = false }) => {
  const dispatch = useDispatch();
  const { userInfo } = useSelector((state) => state.auth);
  const [quantity, setQuantity] = useState(1);
  const [selectedImage, setSelectedImage] = useState(product.images && product.images.length > 0 ? product.images[0] : '/placeholder.png');

  // 处理添加到购物车
  const handleAddToCart = () => {
    dispatch(addToCart({ productId: product._id, quantity }));
  };

  // 处理添加到收藏夹
  const handleAddToWishlist = () => {
    if (userInfo) {
      onAddToWishlist && onAddToWishlist(product._id);
    } else {
      // 如果未登录，跳转到登录页面
      window.location.href = '/login?redirect=/wishlist';
    }
  };

  // 计算折扣
  const discount = product.originalPrice > product.price
    ? `${Math.round((1 - product.price / product.originalPrice) * 100)}% OFF`
    : null;

  return (
    <div className="product-detail">
      <Row gutter={[32, 32]}>
        {/* 产品图片 */}
        <Col xs={24} md={12}>
          <div className="product-images">
            <div className="main-image">
              <Image
                src={selectedImage}
                alt={product.name}
                style={{ width: '100%', height: 'auto', maxHeight: '400px', objectFit: 'contain' }}
              />
            </div>
            {product.images && product.images.length > 1 && (
              <div className="thumbnail-images" style={{ marginTop: '16px' }}>
                <Row gutter={[8, 8]}>
                  {product.images.map((image, index) => (
                    <Col span={6} key={index}>
                      <div
                        className={`thumbnail ${selectedImage === image ? 'active' : ''}`}
                        onClick={() => setSelectedImage(image)}
                        style={{ cursor: 'pointer', border: selectedImage === image ? '2px solid #1890ff' : '1px solid #d9d9d9' }}
                      >
                        <Image
                          src={image}
                          alt={`${product.name}-${index}`}
                          style={{ width: '100%', height: '80px', objectFit: 'cover' }}
                          preview={false}
                        />
                      </div>
                    </Col>
                  ))}
                </Row>
              </div>
            )}
          </div>
        </Col>

        {/* 产品信息 */}
        <Col xs={24} md={12}>
          <div className="product-info">
            <Title level={2}>{product.name}</Title>
            
            <div className="product-rating" style={{ marginBottom: '16px' }}>
              <Rating value={product.rating} disabled />
              <Text type="secondary" style={{ marginLeft: '8px' }}>
                ({product.numReviews} 条评价)
              </Text>
            </div>

            <div className="product-price" style={{ marginBottom: '16px' }}>
              <Price
                price={product.price}
                originalPrice={product.originalPrice}
                discount={discount}
                size="xlarge"
                color="danger"
              />
            </div>

            {product.countInStock > 0 ? (
              <Text type="success" style={{ marginBottom: '16px', display: 'block' }}>
                有货 ({product.countInStock} 件)
              </Text>
            ) : (
              <Text type="danger" style={{ marginBottom: '16px', display: 'block' }}>
                缺货
              </Text>
            )}

            <Divider />

            {/* 购买数量 */}
            <div className="product-quantity" style={{ marginBottom: '16px' }}>
              <Text style={{ marginRight: '16px' }}>数量:</Text>
              <InputNumber
                min={1}
                max={product.countInStock}
                defaultValue={1}
                onChange={setQuantity}
                disabled={product.countInStock === 0}
              />
            </div>

            {/* 操作按钮 */}
            <div className="product-actions" style={{ marginBottom: '24px' }}>
              <Space>
                <Button
                  type="primary"
                  icon={<ShoppingCartOutlined />}
                  size="large"
                  onClick={handleAddToCart}
                  disabled={product.countInStock === 0}
                >
                  加入购物车
                </Button>
                <Button
                  type={inWishlist ? 'default' : 'default'}
                  icon={inWishlist ? <HeartFilled style={{ color: '#ff4d4f' }} /> : <HeartOutlined />}
                  size="large"
                  onClick={handleAddToWishlist}
                >
                  {inWishlist ? '已收藏' : '收藏'}
                </Button>
              </Space>
            </div>

            {/* 产品简介 */}
            <div className="product-short-description">
              <Paragraph ellipsis={{ rows: 3, expandable: true, symbol: '展开' }}>
                {product.description}
              </Paragraph>
            </div>
          </div>
        </Col>
      </Row>

      {/* 产品详情标签页 */}
      <div className="product-tabs" style={{ marginTop: '32px' }}>
        <Tabs defaultActiveKey="description">
          <TabPane tab="商品详情" key="description">
            <div dangerouslySetInnerHTML={{ __html: product.description }} />
          </TabPane>
          <TabPane tab="规格参数" key="specifications">
            {product.specifications ? (
              <div dangerouslySetInnerHTML={{ __html: product.specifications }} />
            ) : (
              <Text type="secondary">暂无规格参数</Text>
            )}
          </TabPane>
          <TabPane tab="包装与售后" key="packaging">
            {product.packaging ? (
              <div dangerouslySetInnerHTML={{ __html: product.packaging }} />
            ) : (
              <div>
                <Paragraph>标准包装，按照商品分类标准进行配送。</Paragraph>
                <Paragraph>本商品支持7天无理由退换货，请在收到商品后及时检查。</Paragraph>
              </div>
            )}
          </TabPane>
        </Tabs>
      </div>
    </div>
  );
};

export default ProductDetail;