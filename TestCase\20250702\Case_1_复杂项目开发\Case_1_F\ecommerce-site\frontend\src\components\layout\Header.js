import React, { useContext } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Layout, Menu, Button, Badge, Dropdown, Space } from 'antd';
import {
  HomeOutlined,
  ShoppingCartOutlined,
  UserOutlined,
  LogoutOutlined,
  ShoppingOutlined,
  LoginOutlined,
  UserAddOutlined,
  HistoryOutlined,
  MenuOutlined,
} from '@ant-design/icons';
import { AuthContext } from '../../context/AuthContext';
import { CartContext } from '../../context/CartContext';

const { Header } = Layout;

const HeaderComponent = () => {
  const { isAuthenticated, user, logout } = useContext(AuthContext);
  const { cart } = useContext(CartContext);
  const navigate = useNavigate();

  // 获取购物车中的商品总数
  const cartItemCount = cart.items.reduce((total, item) => total + item.quantity, 0);

  // 处理登出
  const handleLogout = async () => {
    await logout();
    navigate('/');
  };

  // 用户下拉菜单
  const userMenu = (
    <Menu>
      <Menu.Item key="profile" icon={<UserOutlined />}>
        <Link to="/profile">个人中心</Link>
      </Menu.Item>
      <Menu.Item key="orders" icon={<HistoryOutlined />}>
        <Link to="/orders">我的订单</Link>
      </Menu.Item>
      <Menu.Item key="logout" icon={<LogoutOutlined />} onClick={handleLogout}>
        退出登录
      </Menu.Item>
    </Menu>
  );

  return (
    <Header
      style={{
        background: '#fff',
        boxShadow: '0 2px 8px rgba(0,0,0,0.06)',
        padding: '0',
      }}
    >
      <div className="container flex-between">
        <div className="logo" style={{ fontSize: '24px', fontWeight: 'bold' }}>
          <Link to="/" style={{ color: '#1890ff' }}>
            电商网站
          </Link>
        </div>

        <Menu mode="horizontal" style={{ flex: 1, justifyContent: 'center' }}>
          <Menu.Item key="home" icon={<HomeOutlined />}>
            <Link to="/">首页</Link>
          </Menu.Item>
          <Menu.Item key="products" icon={<ShoppingOutlined />}>
            <Link to="/products">全部商品</Link>
          </Menu.Item>
        </Menu>

        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Link to="/cart">
            <Badge count={cartItemCount} size="small">
              <Button
                type="text"
                icon={<ShoppingCartOutlined style={{ fontSize: '20px' }} />}
              />
            </Badge>
          </Link>

          {isAuthenticated ? (
            <Dropdown overlay={userMenu} placement="bottomRight">
              <Button type="text">
                <Space>
                  <UserOutlined />
                  {user?.username || '用户'}
                  <MenuOutlined />
                </Space>
              </Button>
            </Dropdown>
          ) : (
            <>
              <Button
                type="text"
                icon={<LoginOutlined />}
                onClick={() => navigate('/login')}
              >
                登录
              </Button>
              <Button
                type="primary"
                icon={<UserAddOutlined />}
                onClick={() => navigate('/register')}
              >
                注册
              </Button>
            </>
          )}
        </div>
      </div>
    </Header>
  );
};

export default HeaderComponent; 