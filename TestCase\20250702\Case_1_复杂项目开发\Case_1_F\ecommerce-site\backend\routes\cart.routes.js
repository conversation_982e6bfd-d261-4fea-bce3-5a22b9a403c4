const express = require('express');
const router = express.Router();
const { 
  getCart, 
  addToCart, 
  updateCartItem, 
  removeCartItem, 
  clearCart 
} = require('../controllers/cart.controller');
const { protect } = require('../middleware/auth.middleware');

// 所有购物车路由都需要登录
router.use(protect);

router.route('/')
  .get(getCart)          // 获取购物车
  .post(addToCart)       // 添加商品到购物车
  .delete(clearCart);    // 清空购物车

router.route('/:itemId')
  .put(updateCartItem)   // 更新购物车项
  .delete(removeCartItem); // 移除购物车项

module.exports = router; 