import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Row, Col, Carousel, Typography, Card, Divider, Button, Spin, Empty } from 'antd';
import { RightOutlined } from '@ant-design/icons';
import axios from 'axios';
import ProductCard from '../components/products/ProductCard';

const { Title } = Typography;

const Home = () => {
  const [featuredProducts, setFeaturedProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // 轮播图数据
  const carouselItems = [
    {
      image: 'https://via.placeholder.com/1200x400/3498db/ffffff?text=热卖商品',
      title: '夏季大促',
      description: '精选商品，低至5折',
      link: '/products'
    },
    {
      image: 'https://via.placeholder.com/1200x400/2ecc71/ffffff?text=新品上市',
      title: '新品上市',
      description: '第一时间体验全新产品',
      link: '/products'
    },
    {
      image: 'https://via.placeholder.com/1200x400/e74c3c/ffffff?text=限时特惠',
      title: '限时特惠',
      description: '错过再等一年',
      link: '/products'
    }
  ];

  // 获取热门商品和分类
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // 获取精选商品
        const productsResponse = await axios.get('/products/featured');
        
        // 获取所有分类
        const categoriesResponse = await axios.get('/categories', {
          params: { parentOnly: true }
        });

        if (productsResponse.data.success && categoriesResponse.data.success) {
          setFeaturedProducts(productsResponse.data.data);
          setCategories(categoriesResponse.data.data);
        }
      } catch (err) {
        console.error('加载首页数据失败:', err);
        setError('加载数据失败，请刷新页面重试');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <div className="text-center" style={{ padding: '100px 0' }}>
        <Spin size="large" tip="加载中..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center" style={{ padding: '100px 0' }}>
        <Empty
          description={error}
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
        <Button type="primary" onClick={() => window.location.reload()}>
          刷新页面
        </Button>
      </div>
    );
  }

  return (
    <div>
      {/* 轮播图 */}
      <Carousel autoplay effect="fade">
        {carouselItems.map((item, index) => (
          <div key={index}>
            <Link to={item.link}>
              <div
                style={{
                  height: '400px',
                  background: `url(${item.image}) center center / cover no-repeat`,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  flexDirection: 'column',
                  color: '#fff',
                  textShadow: '0 0 10px rgba(0,0,0,0.5)'
                }}
              >
                <h2 style={{ fontSize: '36px', marginBottom: '10px' }}>{item.title}</h2>
                <p style={{ fontSize: '18px' }}>{item.description}</p>
              </div>
            </Link>
          </div>
        ))}
      </Carousel>

      {/* 商品分类 */}
      <div className="mt-3 mb-3">
        <div className="flex-between mb-2">
          <Title level={3}>商品分类</Title>
          <Link to="/products">
            <Button type="link">
              全部分类 <RightOutlined />
            </Button>
          </Link>
        </div>
        <Row gutter={[16, 16]}>
          {categories.map(category => (
            <Col key={category._id} xs={12} sm={8} md={6} lg={4}>
              <Link to={`/products?category=${category._id}`}>
                <Card
                  hoverable
                  cover={
                    <img
                      alt={category.name}
                      src={category.image || 'https://via.placeholder.com/200x200?text=' + category.name}
                      style={{ height: '150px', objectFit: 'cover' }}
                    />
                  }
                  bodyStyle={{ padding: '12px', textAlign: 'center' }}
                >
                  {category.name}
                </Card>
              </Link>
            </Col>
          ))}
        </Row>
      </div>

      {/* 热门商品 */}
      <Divider />
      <div className="mb-3">
        <div className="flex-between mb-2">
          <Title level={3}>热门推荐</Title>
          <Link to="/products">
            <Button type="link">
              更多商品 <RightOutlined />
            </Button>
          </Link>
        </div>
        <Row gutter={[16, 16]}>
          {featuredProducts.map(product => (
            <Col key={product._id} xs={24} sm={12} md={8} lg={6}>
              <ProductCard product={product} />
            </Col>
          ))}
        </Row>
      </div>

      {/* 促销活动 */}
      <Divider />
      <div className="mb-3">
        <Title level={3} className="mb-2">促销活动</Title>
        <Row gutter={[16, 16]}>
          <Col xs={24} md={12}>
            <Link to="/products">
              <Card
                hoverable
                cover={
                  <img
                    alt="限时抢购"
                    src="https://via.placeholder.com/600x200/f39c12/ffffff?text=限时抢购"
                  />
                }
              >
                <Card.Meta
                  title="限时抢购"
                  description="每日精选商品限时特价，低至5折起"
                />
              </Card>
            </Link>
          </Col>
          <Col xs={24} md={12}>
            <Link to="/products">
              <Card
                hoverable
                cover={
                  <img
                    alt="新人福利"
                    src="https://via.placeholder.com/600x200/9b59b6/ffffff?text=新人福利"
                  />
                }
              >
                <Card.Meta
                  title="新人福利"
                  description="新用户注册即送100元优惠券，购物更划算"
                />
              </Card>
            </Link>
          </Col>
        </Row>
      </div>
    </div>
  );
};

export default Home; 