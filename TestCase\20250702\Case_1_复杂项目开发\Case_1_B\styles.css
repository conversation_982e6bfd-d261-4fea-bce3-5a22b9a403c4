body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    line-height: 1.6;
}
header {
    background: #4CAF50;
    color: white;
    padding: 1rem 0;
    text-align: center;
}
nav ul {
    list-style: none;
    padding: 0;
}
nav ul li {
    display: inline;
    margin: 0 10px;
}
nav a {
    color: white;
    text-decoration: none;
}
main {
    padding: 20px;
}
section {
    margin-bottom: 20px;
}
.product-list, .cart-items, .order-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
}
button {
    background: #4CAF50;
    color: white;
    border: none;
    padding: 10px 15px;
    cursor: pointer;
}
button:hover {
    background: #45a049;
}