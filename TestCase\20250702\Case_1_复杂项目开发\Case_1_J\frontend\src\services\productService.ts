import { request } from './api';
import { Product, Category } from '../store/slices/productSlice';

interface ProductListResponse {
  products: Product[];
  pagination: {
    current: number;
    pageSize: number;
    total: number;
    pages: number;
  };
}

interface ProductDetailResponse {
  product: Product;
}

interface CategoryListResponse {
  categories: Category[];
}

interface ProductListParams {
  page?: number;
  limit?: number;
  search?: string;
  category?: string;
  brand?: string;
  minPrice?: number;
  maxPrice?: number;
  sort?: string;
  order?: 'asc' | 'desc';
}

class ProductService {
  // 获取商品列表
  async getProducts(params: ProductListParams = {}): Promise<ProductListResponse> {
    const queryParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const queryString = queryParams.toString();
    const url = queryString ? `/products?${queryString}` : '/products';
    
    return request.get(url);
  }

  // 获取商品详情
  async getProductById(id: string): Promise<ProductDetailResponse> {
    return request.get(`/products/${id}`);
  }

  // 获取分类列表
  async getCategories(): Promise<CategoryListResponse> {
    return request.get('/categories');
  }

  // 获取扁平分类列表
  async getFlatCategories(params: { level?: number; parent?: string } = {}): Promise<CategoryListResponse> {
    const queryParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const queryString = queryParams.toString();
    const url = queryString ? `/categories/flat?${queryString}` : '/categories/flat';
    
    return request.get(url);
  }

  // 获取热门商品
  async getHotProducts(limit: number = 8): Promise<{ products: Product[] }> {
    return request.get(`/products/featured/hot?limit=${limit}`);
  }

  // 获取推荐商品
  async getRecommendedProducts(limit: number = 8): Promise<{ products: Product[] }> {
    return request.get(`/products/featured/recommended?limit=${limit}`);
  }

  // 搜索商品
  async searchProducts(query: string, params: Omit<ProductListParams, 'search'> = {}): Promise<ProductListResponse> {
    return this.getProducts({ ...params, search: query });
  }

  // 根据分类获取商品
  async getProductsByCategory(categoryId: string, params: Omit<ProductListParams, 'category'> = {}): Promise<ProductListResponse> {
    return this.getProducts({ ...params, category: categoryId });
  }

  // 根据品牌获取商品
  async getProductsByBrand(brand: string, params: Omit<ProductListParams, 'brand'> = {}): Promise<ProductListResponse> {
    return this.getProducts({ ...params, brand });
  }

  // 获取价格范围内的商品
  async getProductsByPriceRange(minPrice: number, maxPrice: number, params: Omit<ProductListParams, 'minPrice' | 'maxPrice'> = {}): Promise<ProductListResponse> {
    return this.getProducts({ ...params, minPrice, maxPrice });
  }
}

export default new ProductService();
