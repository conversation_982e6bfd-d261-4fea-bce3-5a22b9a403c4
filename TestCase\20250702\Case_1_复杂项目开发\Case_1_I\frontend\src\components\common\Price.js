import React from 'react';
import { Typography } from 'antd';
import { formatPrice } from '../../utils/formatUtils';

const { Text } = Typography;

const Price = ({
  price,
  originalPrice,
  discount,
  size = 'medium',
  color = 'default',
}) => {
  // 根据尺寸设置样式
  const sizeStyles = {
    small: { fontSize: '12px' },
    medium: { fontSize: '16px' },
    large: { fontSize: '20px' },
    xlarge: { fontSize: '24px' },
  };

  // 根据颜色设置样式
  const colorStyles = {
    default: { color: 'inherit' },
    primary: { color: '#1890ff' },
    success: { color: '#52c41a' },
    warning: { color: '#faad14' },
    danger: { color: '#f5222d' },
  };

  const style = {
    ...sizeStyles[size],
    ...colorStyles[color],
  };

  // 如果有原价，显示折扣价和原价
  if (originalPrice && originalPrice > price) {
    return (
      <div className="price-container">
        <Text strong style={style}>
          {formatPrice(price)}
        </Text>
        <Text delete type="secondary" style={{ marginLeft: '8px' }}>
          {formatPrice(originalPrice)}
        </Text>
        {discount && (
          <Text type="danger" style={{ marginLeft: '8px' }}>
            {discount}
          </Text>
        )}
      </div>
    );
  }

  // 如果只有价格，只显示价格
  return (
    <Text strong style={style}>
      {formatPrice(price)}
    </Text>
  );
};

export default Price;