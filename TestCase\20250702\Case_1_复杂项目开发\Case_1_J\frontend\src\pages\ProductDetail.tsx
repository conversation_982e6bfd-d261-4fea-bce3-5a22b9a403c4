import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Row, Col, Card, Button, InputNumber, Space, Typography, Tag, Breadcrumb, Image, Spin, message } from 'antd';
import { ShoppingCartOutlined, HeartOutlined, ShareAltOutlined, HomeOutlined } from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../store';
import { fetchProductById, clearCurrentProduct } from '../store/slices/productSlice';
import { addToCart } from '../store/slices/cartSlice';

const { Title, Text, Paragraph } = Typography;

const ProductDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { currentProduct: product, loading } = useSelector((state: RootState) => state.product);
  
  const [selectedSku, setSelectedSku] = useState<any>(null);
  const [quantity, setQuantity] = useState(1);
  const [selectedSpecs, setSelectedSpecs] = useState<Record<string, string>>({});

  useEffect(() => {
    if (id) {
      dispatch(fetchProductById(id) as any);
    }
    return () => {
      dispatch(clearCurrentProduct());
    };
  }, [dispatch, id]);

  useEffect(() => {
    if (product && product.skus && product.skus.length > 0) {
      // 默认选择第一个SKU
      const firstSku = product.skus[0];
      setSelectedSku(firstSku);
      
      // 设置默认规格选择
      const defaultSpecs: Record<string, string> = {};
      firstSku.specs.forEach(spec => {
        defaultSpecs[spec.name] = spec.value;
      });
      setSelectedSpecs(defaultSpecs);
    }
  }, [product]);

  const handleSpecChange = (specName: string, specValue: string) => {
    const newSelectedSpecs = { ...selectedSpecs, [specName]: specValue };
    setSelectedSpecs(newSelectedSpecs);

    // 查找匹配的SKU
    const matchingSku = product?.skus.find(sku => {
      return sku.specs.every(spec => newSelectedSpecs[spec.name] === spec.value);
    });

    if (matchingSku) {
      setSelectedSku(matchingSku);
      setQuantity(1); // 重置数量
    }
  };

  const handleAddToCart = () => {
    if (!selectedSku) {
      message.warning('请选择商品规格');
      return;
    }

    if (quantity > selectedSku.stock) {
      message.warning('库存不足');
      return;
    }

    dispatch(addToCart({
      productId: product!._id,
      sku: selectedSku.sku,
      name: product!.name,
      image: selectedSku.image || product!.images[0],
      price: selectedSku.price,
      quantity,
      specs: selectedSku.specs,
      stock: selectedSku.stock,
    }));
  };

  const handleBuyNow = () => {
    handleAddToCart();
    navigate('/cart');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Spin size="large" />
      </div>
    );
  }

  if (!product) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Title level={3}>商品不存在</Title>
          <Button type="primary" onClick={() => navigate('/products')}>
            返回商品列表
          </Button>
        </div>
      </div>
    );
  }

  // 获取所有规格名称
  const specNames = Array.from(new Set(
    product.skus.flatMap(sku => sku.specs.map(spec => spec.name))
  ));

  // 获取每个规格的所有可选值
  const getSpecValues = (specName: string) => {
    return Array.from(new Set(
      product.skus
        .filter(sku => {
          // 过滤出与当前已选规格兼容的SKU
          return Object.entries(selectedSpecs).every(([name, value]) => {
            if (name === specName) return true; // 当前正在选择的规格不需要匹配
            return sku.specs.some(spec => spec.name === name && spec.value === value);
          });
        })
        .flatMap(sku => sku.specs.filter(spec => spec.name === specName).map(spec => spec.value))
    ));
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* 面包屑导航 */}
        <Breadcrumb className="mb-6">
          <Breadcrumb.Item>
            <HomeOutlined />
            <span className="ml-1">首页</span>
          </Breadcrumb.Item>
          <Breadcrumb.Item>
            <span>商品</span>
          </Breadcrumb.Item>
          <Breadcrumb.Item>
            {product.category.name}
          </Breadcrumb.Item>
          <Breadcrumb.Item>
            {product.name}
          </Breadcrumb.Item>
        </Breadcrumb>

        <Row gutter={[32, 32]}>
          {/* 商品图片 */}
          <Col xs={24} md={12}>
            <Card className="sticky top-4">
              <Image.PreviewGroup>
                <div className="mb-4">
                  <Image
                    src={selectedSku?.image || product.images[0]}
                    alt={product.name}
                    className="w-full rounded-lg"
                  />
                </div>
                <div className="grid grid-cols-4 gap-2">
                  {product.images.map((image, index) => (
                    <Image
                      key={index}
                      src={image}
                      alt={`${product.name} ${index + 1}`}
                      className="w-full h-20 object-cover rounded cursor-pointer"
                    />
                  ))}
                </div>
              </Image.PreviewGroup>
            </Card>
          </Col>

          {/* 商品信息 */}
          <Col xs={24} md={12}>
            <Space direction="vertical" size="large" className="w-full">
              {/* 基本信息 */}
              <div>
                <Title level={2}>{product.name}</Title>
                <Paragraph className="text-gray-600">
                  {product.description}
                </Paragraph>
                
                <Space wrap>
                  {product.tags.map(tag => (
                    <Tag key={tag} color="blue">{tag}</Tag>
                  ))}
                </Space>
              </div>

              {/* 价格信息 */}
              <Card>
                <Space direction="vertical" size="small">
                  <div className="flex items-center space-x-4">
                    <span className="text-3xl font-bold text-red-500">
                      ¥{selectedSku?.price || product.basePrice}
                    </span>
                    {product.originalPrice && product.originalPrice > (selectedSku?.price || product.basePrice) && (
                      <span className="text-lg text-gray-500 line-through">
                        ¥{product.originalPrice}
                      </span>
                    )}
                  </div>
                  <div className="text-sm text-gray-500">
                    <Space>
                      <span>销量: {product.sales}</span>
                      <span>评分: {product.rating.toFixed(1)}</span>
                      <span>库存: {selectedSku?.stock || product.totalStock}</span>
                    </Space>
                  </div>
                </Space>
              </Card>

              {/* 规格选择 */}
              {specNames.length > 0 && (
                <Card title="选择规格">
                  <Space direction="vertical" size="middle" className="w-full">
                    {specNames.map(specName => (
                      <div key={specName}>
                        <Text strong className="mb-2 block">{specName}:</Text>
                        <Space wrap>
                          {getSpecValues(specName).map(specValue => (
                            <Button
                              key={specValue}
                              type={selectedSpecs[specName] === specValue ? 'primary' : 'default'}
                              onClick={() => handleSpecChange(specName, specValue)}
                            >
                              {specValue}
                            </Button>
                          ))}
                        </Space>
                      </div>
                    ))}
                  </Space>
                </Card>
              )}

              {/* 数量选择和购买 */}
              <Card>
                <Space direction="vertical" size="middle" className="w-full">
                  <div className="flex items-center space-x-4">
                    <Text strong>数量:</Text>
                    <InputNumber
                      min={1}
                      max={selectedSku?.stock || product.totalStock}
                      value={quantity}
                      onChange={(value) => setQuantity(value || 1)}
                    />
                    <Text className="text-gray-500">
                      库存 {selectedSku?.stock || product.totalStock} 件
                    </Text>
                  </div>

                  <Space size="middle" className="w-full">
                    <Button
                      type="primary"
                      size="large"
                      icon={<ShoppingCartOutlined />}
                      onClick={handleAddToCart}
                      disabled={!selectedSku || selectedSku.stock === 0}
                      className="flex-1"
                    >
                      加入购物车
                    </Button>
                    <Button
                      type="primary"
                      size="large"
                      danger
                      onClick={handleBuyNow}
                      disabled={!selectedSku || selectedSku.stock === 0}
                      className="flex-1"
                    >
                      立即购买
                    </Button>
                  </Space>

                  <Space className="w-full justify-center">
                    <Button type="text" icon={<HeartOutlined />}>
                      收藏
                    </Button>
                    <Button type="text" icon={<ShareAltOutlined />}>
                      分享
                    </Button>
                  </Space>
                </Space>
              </Card>

              {/* 商品详情 */}
              <Card title="商品详情">
                <Space direction="vertical" size="small" className="w-full">
                  <div className="flex">
                    <Text strong className="w-20">品牌:</Text>
                    <Text>{product.brand}</Text>
                  </div>
                  <div className="flex">
                    <Text strong className="w-20">分类:</Text>
                    <Text>{product.category.name}</Text>
                  </div>
                  {product.weight && (
                    <div className="flex">
                      <Text strong className="w-20">重量:</Text>
                      <Text>{product.weight}g</Text>
                    </div>
                  )}
                </Space>
              </Card>
            </Space>
          </Col>
        </Row>
      </div>
    </div>
  );
};

export default ProductDetail;
