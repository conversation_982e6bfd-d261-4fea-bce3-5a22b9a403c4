# ShopHub E-Commerce Platform

A full-featured e-commerce platform with user authentication, product management, shopping cart, checkout process, and payment integration.

## Features

### User Features
- User registration and authentication
- Product browsing and searching
- Product reviews and ratings
- Shopping cart functionality
- Checkout process
- Order history
- User profile management
- Payment processing with Stripe

### Admin Features
- Product management (CRUD operations)
- User management
- Order management
- Order status updates

## Tech Stack

### Backend
- Node.js
- Express.js
- MongoDB (with Mongoose)
- JWT Authentication
- Stripe API for payments

### Frontend
- React
- Redux (with Redux Toolkit)
- React Router
- React Bootstrap
- Stripe Elements for payment UI

## Project Structure

```
ecommerce-site/
├── backend/
│   ├── controllers/     # Route controllers
│   ├── middleware/      # Custom middleware
│   ├── models/          # Mongoose models
│   ├── routes/          # API routes
│   └── server.js        # Express app
└── frontend/
    ├── public/          # Static files
    └── src/
        ├── assets/      # Images and other assets
        ├── components/  # Reusable components
        ├── screens/     # Page components
        ├── slices/      # Redux slices
        └── utils/       # Utility functions
```

## Installation

### Prerequisites
- Node.js
- MongoDB

### Setup Instructions

1. Clone the repository
```
git clone <repository-url>
cd ecommerce-site
```

2. Install backend dependencies
```
cd backend
npm install
```

3. Install frontend dependencies
```
cd ../frontend
npm install
```

4. Set up environment variables
   - Create a `.env` file in the backend directory with the following variables:
   ```
   PORT=5000
   MONGO_URI=your_mongodb_connection_string
   JWT_SECRET=your_jwt_secret
   STRIPE_SECRET_KEY=your_stripe_secret_key
   ```

5. Run the application
   - For development (backend and frontend concurrently):
   ```
   # From the root directory
   npm run dev
   ```
   - For backend only:
   ```
   # From the backend directory
   npm run server
   ```
   - For frontend only:
   ```
   # From the frontend directory
   npm start
   ```

## API Endpoints

### Products
- `GET /api/products` - Get all products
- `GET /api/products/:id` - Get single product
- `POST /api/products` - Create a product (Admin)
- `PUT /api/products/:id` - Update a product (Admin)
- `DELETE /api/products/:id` - Delete a product (Admin)
- `POST /api/products/:id/reviews` - Create product review
- `GET /api/products/top` - Get top products

### Users
- `POST /api/users` - Register a new user
- `POST /api/users/login` - Authenticate user & get token
- `GET /api/users/profile` - Get user profile
- `PUT /api/users/profile` - Update user profile
- `GET /api/users` - Get all users (Admin)
- `DELETE /api/users/:id` - Delete user (Admin)
- `GET /api/users/:id` - Get user by ID (Admin)
- `PUT /api/users/:id` - Update user (Admin)

### Orders
- `POST /api/orders` - Create new order
- `GET /api/orders/:id` - Get order by ID
- `PUT /api/orders/:id/pay` - Update order to paid
- `PUT /api/orders/:id/deliver` - Update order to delivered
- `PUT /api/orders/:id/status` - Update order status
- `GET /api/orders/myorders` - Get logged in user orders
- `GET /api/orders` - Get all orders (Admin)

### Payments
- `POST /api/payments/stripe` - Process Stripe payment
- `GET /api/payments/stripe/config` - Get Stripe config