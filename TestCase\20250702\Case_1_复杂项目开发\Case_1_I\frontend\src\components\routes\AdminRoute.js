import React from 'react';
import { Navigate, Outlet } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { Result, Spin } from 'antd';

// 管理员专用路由
const AdminRoute = () => {
  const { userInfo, loading } = useSelector((state) => state.auth);

  // 如果正在加载，显示加载中
  if (loading) {
    return (
      <div className="centered-container">
        <Spin size="large" />
      </div>
    );
  }

  // 如果没有登录，重定向到登录页面
  if (!userInfo) {
    return <Navigate to="/login" replace />;
  }

  // 如果不是管理员，显示无权限页面
  if (userInfo.role !== 'admin') {
    return (
      <Result
        status="403"
        title="403"
        subTitle="抱歉，您没有权限访问此页面。"
      />
    );
  }

  // 如果是管理员，渲染子路由
  return <Outlet />;
};

export default AdminRoute;