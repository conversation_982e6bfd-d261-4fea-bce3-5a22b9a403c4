{"name": "ecommerce-backend", "version": "1.0.0", "description": "电商网站后端API服务", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "keywords": ["ecommerce", "api", "nodejs", "express"], "author": "Developer", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "multer": "^1.4.5", "express-validator": "^7.0.1", "helmet": "^7.0.0", "express-rate-limit": "^6.10.0", "compression": "^1.7.4", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}