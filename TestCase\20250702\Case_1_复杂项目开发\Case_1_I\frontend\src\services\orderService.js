import apiClient from './apiClient';

// 创建订单
const createOrder = async (orderData, token) => {
  const response = await apiClient.post('/orders', orderData, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

// 获取订单详情
const getOrderDetails = async (orderId, token) => {
  const response = await apiClient.get(`/orders/${orderId}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

// 获取用户订单列表
const getUserOrders = async (pageNumber = 1, status = '', token) => {
  const response = await apiClient.get('/orders', {
    params: {
      page: pageNumber,
      status,
    },
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

// 取消订单
const cancelOrder = async (orderId, token) => {
  const response = await apiClient.put(
    `/orders/${orderId}/cancel`,
    {},
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    }
  );
  return response.data;
};

// 获取所有订单（管理员）
const getAllOrders = async (pageNumber = 1, status = '', token) => {
  const response = await apiClient.get('/orders/admin', {
    params: {
      page: pageNumber,
      status,
    },
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

// 更新订单状态（管理员）
const updateOrderStatus = async (orderId, status, token) => {
  const response = await apiClient.put(
    `/orders/${orderId}/status`,
    { status },
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    }
  );
  return response.data;
};

// 获取订单统计信息（管理员）
const getOrderStats = async (token) => {
  const response = await apiClient.get('/orders/stats', {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

const orderService = {
  createOrder,
  getOrderDetails,
  getUserOrders,
  cancelOrder,
  getAllOrders,
  updateOrderStatus,
  getOrderStats,
};

export default orderService;