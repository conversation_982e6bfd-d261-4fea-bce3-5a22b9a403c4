import { createSlice } from '@reduxjs/toolkit';

// 初始状态
const initialState = {
  notification: {
    show: false,
    type: '', // 'success', 'error', 'info', 'warning'
    message: '',
  },
  sidebarCollapsed: false,
  mobileMenuOpen: false,
  searchVisible: false,
  filterVisible: false,
};

// 创建slice
const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    // 显示通知
    showNotification: (state, action) => {
      state.notification = {
        show: true,
        type: action.payload.type,
        message: action.payload.message,
      };
    },
    // 隐藏通知
    hideNotification: (state) => {
      state.notification.show = false;
    },
    // 切换侧边栏折叠状态
    toggleSidebar: (state) => {
      state.sidebarCollapsed = !state.sidebarCollapsed;
    },
    // 设置侧边栏折叠状态
    setSidebarCollapsed: (state, action) => {
      state.sidebarCollapsed = action.payload;
    },
    // 切换移动端菜单状态
    toggleMobileMenu: (state) => {
      state.mobileMenuOpen = !state.mobileMenuOpen;
    },
    // 设置移动端菜单状态
    setMobileMenuOpen: (state, action) => {
      state.mobileMenuOpen = action.payload;
    },
    // 切换搜索框可见性
    toggleSearchVisible: (state) => {
      state.searchVisible = !state.searchVisible;
    },
    // 设置搜索框可见性
    setSearchVisible: (state, action) => {
      state.searchVisible = action.payload;
    },
    // 切换筛选器可见性
    toggleFilterVisible: (state) => {
      state.filterVisible = !state.filterVisible;
    },
    // 设置筛选器可见性
    setFilterVisible: (state, action) => {
      state.filterVisible = action.payload;
    },
  },
});

export const {
  showNotification,
  hideNotification,
  toggleSidebar,
  setSidebarCollapsed,
  toggleMobileMenu,
  setMobileMenuOpen,
  toggleSearchVisible,
  setSearchVisible,
  toggleFilterVisible,
  setFilterVisible,
} = uiSlice.actions;
export default uiSlice.reducer;