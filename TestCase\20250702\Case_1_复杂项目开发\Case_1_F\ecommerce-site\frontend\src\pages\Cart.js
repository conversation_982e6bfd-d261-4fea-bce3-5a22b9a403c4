import React, { useContext, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import {
  Row,
  Col,
  Typography,
  Table,
  Button,
  InputNumber,
  Image,
  Card,
  Empty,
  message,
  Popconfirm,
  Space,
  Tag,
  Divider
} from 'antd';
import {
  ShoppingCartOutlined,
  DeleteOutlined,
  ShoppingOutlined,
  RightOutlined
} from '@ant-design/icons';
import { CartContext } from '../context/CartContext';
import { AuthContext } from '../context/AuthContext';

const { Title, Text } = Typography;

const Cart = () => {
  const { cart, loading, error, updateCartItem, removeCartItem, clearCart } = useContext(CartContext);
  const { isAuthenticated } = useContext(AuthContext);
  const navigate = useNavigate();

  useEffect(() => {
    if (error) {
      message.error(error);
    }
  }, [error]);

  // 处理数量变化
  const handleQuantityChange = async (record, quantity) => {
    if (quantity < 1) {
      quantity = 1;
    } else if (quantity > record.product.stock) {
      quantity = record.product.stock;
      message.warning(`该商品最多只能购买 ${record.product.stock} 件`);
    }

    await updateCartItem(record.product._id, quantity);
  };

  // 处理移除商品
  const handleRemoveItem = async (productId) => {
    const success = await removeCartItem(productId);
    if (success) {
      message.success('商品已从购物车移除');
    }
  };

  // 处理清空购物车
  const handleClearCart = async () => {
    const success = await clearCart();
    if (success) {
      message.success('购物车已清空');
    }
  };

  // 处理结算
  const handleCheckout = () => {
    if (!isAuthenticated) {
      message.info('请先登录再结算');
      navigate('/login?redirect=checkout');
    } else {
      navigate('/checkout');
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '商品',
      dataIndex: 'product',
      key: 'product',
      render: (product) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Image
            src={product.images && product.images.length > 0 ? product.images[0] : 'https://via.placeholder.com/100'}
            alt={product.name}
            width={80}
            height={80}
            style={{ objectFit: 'cover' }}
            preview={false}
          />
          <div style={{ marginLeft: '16px' }}>
            <Link to={`/products/${product._id}`}>
              <Text strong>{product.name}</Text>
            </Link>
            {product.stock <= 0 && (
              <Tag color="red" style={{ marginLeft: '8px' }}>已售罄</Tag>
            )}
          </div>
        </div>
      ),
    },
    {
      title: '单价',
      dataIndex: 'price',
      key: 'price',
      render: (price) => (
        <Text style={{ color: '#ff4d4f' }}>¥{price.toFixed(2)}</Text>
      ),
    },
    {
      title: '数量',
      key: 'quantity',
      render: (_, record) => (
        <InputNumber
          min={1}
          max={record.product.stock}
          value={record.quantity}
          onChange={(value) => handleQuantityChange(record, value)}
          disabled={record.product.stock <= 0}
        />
      ),
    },
    {
      title: '小计',
      key: 'subtotal',
      render: (_, record) => (
        <Text style={{ color: '#ff4d4f', fontWeight: 'bold' }}>
          ¥{(record.price * record.quantity).toFixed(2)}
        </Text>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Popconfirm
          title="确定要从购物车中移除该商品吗？"
          onConfirm={() => handleRemoveItem(record.product._id)}
          okText="确定"
          cancelText="取消"
        >
          <Button
            type="text"
            icon={<DeleteOutlined />}
            danger
          >
            删除
          </Button>
        </Popconfirm>
      ),
    },
  ];

  return (
    <div>
      <div className="flex-between mb-3">
        <Title level={2}>
          <ShoppingCartOutlined /> 购物车
        </Title>
        {cart.items.length > 0 && (
          <Popconfirm
            title="确定要清空购物车吗？"
            onConfirm={handleClearCart}
            okText="确定"
            cancelText="取消"
          >
            <Button danger>清空购物车</Button>
          </Popconfirm>
        )}
      </div>

      {cart.items.length === 0 ? (
        <Card>
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="您的购物车是空的"
          >
            <Button type="primary" icon={<ShoppingOutlined />} onClick={() => navigate('/products')}>
              去选购商品
            </Button>
          </Empty>
        </Card>
      ) : (
        <>
          <Card className="mb-3">
            <Table
              columns={columns}
              dataSource={cart.items.map((item, index) => ({ ...item, key: index }))}
              pagination={false}
              loading={loading}
              rowKey="key"
            />
          </Card>

          <Row>
            <Col xs={24} md={{ span: 8, offset: 16 }}>
              <Card>
                <div className="mb-2">
                  <Space size="large">
                    <Text>商品总数:</Text>
                    <Text strong>{cart.items.reduce((total, item) => total + item.quantity, 0)} 件</Text>
                  </Space>
                </div>
                <div className="mb-2">
                  <Space size="large">
                    <Text>总计:</Text>
                    <Text style={{ color: '#ff4d4f', fontSize: '24px', fontWeight: 'bold' }}>
                      ¥{cart.totalPrice.toFixed(2)}
                    </Text>
                  </Space>
                </div>

                <Divider />

                <div className="flex-between">
                  <Button icon={<ShoppingOutlined />} onClick={() => navigate('/products')}>
                    继续购物
                  </Button>
                  <Button
                    type="primary"
                    size="large"
                    icon={<RightOutlined />}
                    onClick={handleCheckout}
                  >
                    去结算
                  </Button>
                </div>
              </Card>
            </Col>
          </Row>
        </>
      )}
    </div>
  );
};

export default Cart; 