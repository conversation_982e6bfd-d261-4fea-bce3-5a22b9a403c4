const User = require('../models/User');
const { successResponse, errorResponse, paginatedResponse } = require('../utils/apiResponse');
const upload = require('../middleware/upload');

/**
 * 获取所有用户（管理员）
 * @route GET /api/users
 * @access Private/Admin
 */
exports.getAllUsers = async (req, res, next) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // 查询条件
    const query = {};
    if (req.query.name) {
      query.name = { $regex: req.query.name, $options: 'i' };
    }
    if (req.query.email) {
      query.email = { $regex: req.query.email, $options: 'i' };
    }
    if (req.query.role) {
      query.role = req.query.role;
    }

    // 执行查询
    const users = await User.find(query)
      .select('-__v')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    // 获取总数
    const total = await User.countDocuments(query);

    return paginatedResponse(res, users, page, limit, total, '获取用户列表成功');
  } catch (error) {
    next(error);
  }
};

/**
 * 获取单个用户
 * @route GET /api/users/:id
 * @access Private/Admin
 */
exports.getUserById = async (req, res, next) => {
  try {
    const user = await User.findById(req.params.id).select('-__v');

    if (!user) {
      return errorResponse(res, '用户不存在', 404);
    }

    return successResponse(res, { user }, '获取用户信息成功');
  } catch (error) {
    next(error);
  }
};

/**
 * 更新用户信息
 * @route PUT /api/users/:id
 * @access Private/Admin
 */
exports.updateUser = async (req, res, next) => {
  try {
    const { name, email, phone, role, isActive } = req.body;

    const user = await User.findById(req.params.id);

    if (!user) {
      return errorResponse(res, '用户不存在', 404);
    }

    // 更新字段
    if (name) user.name = name;
    if (email) user.email = email;
    if (phone) user.phone = phone;
    if (role) user.role = role;
    if (isActive !== undefined) user.isActive = isActive;

    const updatedUser = await user.save();

    return successResponse(res, { user: updatedUser }, '用户信息更新成功');
  } catch (error) {
    next(error);
  }
};

/**
 * 删除用户
 * @route DELETE /api/users/:id
 * @access Private/Admin
 */
exports.deleteUser = async (req, res, next) => {
  try {
    const user = await User.findById(req.params.id);

    if (!user) {
      return errorResponse(res, '用户不存在', 404);
    }

    await user.remove();

    return successResponse(res, {}, '用户删除成功');
  } catch (error) {
    next(error);
  }
};

/**
 * 更新个人资料
 * @route PUT /api/users/profile
 * @access Private
 */
exports.updateProfile = async (req, res, next) => {
  try {
    const { name, phone } = req.body;

    const user = await User.findById(req.user._id);

    // 更新字段
    if (name) user.name = name;
    if (phone) user.phone = phone;

    const updatedUser = await user.save();

    return successResponse(res, { user: updatedUser }, '个人资料更新成功');
  } catch (error) {
    next(error);
  }
};

/**
 * 上传头像
 * @route POST /api/users/avatar
 * @access Private
 */
exports.uploadAvatar = async (req, res, next) => {
  try {
    // 上传处理由multer中间件完成
    if (!req.file) {
      return errorResponse(res, '请选择要上传的图片', 400);
    }

    const user = await User.findById(req.user._id);

    // 更新头像路径
    user.avatar = `/uploads/avatars/${req.file.filename}`;
    await user.save();

    return successResponse(res, { avatar: user.avatar }, '头像上传成功');
  } catch (error) {
    next(error);
  }
};

/**
 * 添加地址
 * @route POST /api/users/addresses
 * @access Private
 */
exports.addAddress = async (req, res, next) => {
  try {
    const { street, city, state, zipCode, country, isDefault } = req.body;

    const user = await User.findById(req.user._id);

    // 创建新地址
    const newAddress = {
      street,
      city,
      state,
      zipCode,
      country,
      isDefault: isDefault || false,
    };

    // 如果设为默认地址，将其他地址设为非默认
    if (newAddress.isDefault) {
      user.addresses.forEach((address) => {
        address.isDefault = false;
      });
    }

    // 添加新地址
    user.addresses.push(newAddress);
    await user.save();

    return successResponse(res, { addresses: user.addresses }, '地址添加成功');
  } catch (error) {
    next(error);
  }
};

/**
 * 更新地址
 * @route PUT /api/users/addresses/:addressId
 * @access Private
 */
exports.updateAddress = async (req, res, next) => {
  try {
    const { street, city, state, zipCode, country, isDefault } = req.body;
    const addressId = req.params.addressId;

    const user = await User.findById(req.user._id);

    // 查找要更新的地址
    const address = user.addresses.id(addressId);
    if (!address) {
      return errorResponse(res, '地址不存在', 404);
    }

    // 更新地址字段
    if (street) address.street = street;
    if (city) address.city = city;
    if (state) address.state = state;
    if (zipCode) address.zipCode = zipCode;
    if (country) address.country = country;

    // 处理默认地址
    if (isDefault !== undefined) {
      if (isDefault) {
        // 将其他地址设为非默认
        user.addresses.forEach((addr) => {
          if (!addr._id.equals(addressId)) {
            addr.isDefault = false;
          }
        });
      }
      address.isDefault = isDefault;
    }

    await user.save();

    return successResponse(res, { addresses: user.addresses }, '地址更新成功');
  } catch (error) {
    next(error);
  }
};

/**
 * 删除地址
 * @route DELETE /api/users/addresses/:addressId
 * @access Private
 */
exports.deleteAddress = async (req, res, next) => {
  try {
    const addressId = req.params.addressId;
    const user = await User.findById(req.user._id);

    // 查找要删除的地址
    const address = user.addresses.id(addressId);
    if (!address) {
      return errorResponse(res, '地址不存在', 404);
    }

    // 删除地址
    address.remove();
    await user.save();

    return successResponse(res, { addresses: user.addresses }, '地址删除成功');
  } catch (error) {
    next(error);
  }
};

/**
 * 获取所有地址
 * @route GET /api/users/addresses
 * @access Private
 */
exports.getAddresses = async (req, res, next) => {
  try {
    const user = await User.findById(req.user._id);
    return successResponse(res, { addresses: user.addresses }, '获取地址列表成功');
  } catch (error) {
    next(error);
  }
};