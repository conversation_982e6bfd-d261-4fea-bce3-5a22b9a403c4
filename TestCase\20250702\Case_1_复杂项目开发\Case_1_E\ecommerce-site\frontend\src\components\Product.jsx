import { Card } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import Rating from './Rating';

const Product = ({ product }) => {
  return (
    <Card className="my-3 p-3 rounded product-card">
      <Link to={`/product/${product._id}`}>
        <Card.Img src={product.image} variant="top" />
      </Link>

      <Card.Body>
        <Link to={`/product/${product._id}`}>
          <Card.Title as="div" className="product-title">
            <strong>{product.name}</strong>
          </Card.Title>
        </Link>

        <Card.Text as="div">
          <Rating
            value={product.rating}
            text={`${product.numReviews} reviews`}
          />
        </Card.Text>

        <Card.Text as="h3">
          ${product.price}
          {product.discount > 0 && (
            <span className="text-danger ms-2">
              <small>
                <del>${(product.price / (1 - product.discount / 100)).toFixed(2)}</del>
              </small>
              <span className="badge bg-danger ms-2">-{product.discount}%</span>
            </span>
          )}
        </Card.Text>
      </Card.Body>
    </Card>
  );
};

export default Product;