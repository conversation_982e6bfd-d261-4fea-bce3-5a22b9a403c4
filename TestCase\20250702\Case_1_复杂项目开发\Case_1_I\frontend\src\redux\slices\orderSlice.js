import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import orderService from '../../services/orderService';

// 初始状态
const initialState = {
  orders: [],
  order: null,
  loading: false,
  error: null,
  success: false,
  page: 1,
  pages: 1,
  totalOrders: 0,
};

// 异步action - 创建订单
export const createOrder = createAsyncThunk(
  'order/createOrder',
  async (_, { getState, rejectWithValue }) => {
    try {
      const { auth, cart } = getState();
      return await orderService.createOrder(
        {
          shippingAddress: cart.shippingAddress,
          paymentMethod: cart.paymentMethod,
        },
        auth.userInfo.token
      );
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || error.message);
    }
  }
);

// 异步action - 获取订单详情
export const getOrderDetails = createAsyncThunk(
  'order/getOrderDetails',
  async (orderId, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      return await orderService.getOrderDetails(orderId, auth.userInfo.token);
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || error.message);
    }
  }
);

// 异步action - 获取用户订单列表
export const getUserOrders = createAsyncThunk(
  'order/getUserOrders',
  async ({ pageNumber = 1, status = '' }, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      return await orderService.getUserOrders(
        pageNumber,
        status,
        auth.userInfo.token
      );
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || error.message);
    }
  }
);

// 异步action - 取消订单
export const cancelOrder = createAsyncThunk(
  'order/cancelOrder',
  async (orderId, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      return await orderService.cancelOrder(orderId, auth.userInfo.token);
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || error.message);
    }
  }
);

// 异步action - 获取所有订单（管理员）
export const getAllOrders = createAsyncThunk(
  'order/getAllOrders',
  async ({ pageNumber = 1, status = '' }, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      return await orderService.getAllOrders(
        pageNumber,
        status,
        auth.userInfo.token
      );
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || error.message);
    }
  }
);

// 异步action - 更新订单状态（管理员）
export const updateOrderStatus = createAsyncThunk(
  'order/updateOrderStatus',
  async ({ orderId, status }, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      return await orderService.updateOrderStatus(
        orderId,
        status,
        auth.userInfo.token
      );
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || error.message);
    }
  }
);

// 创建slice
const orderSlice = createSlice({
  name: 'order',
  initialState,
  reducers: {
    clearOrderDetails: (state) => {
      state.order = null;
    },
    clearOrderError: (state) => {
      state.error = null;
    },
    resetOrderSuccess: (state) => {
      state.success = false;
    },
  },
  extraReducers: (builder) => {
    builder
      // 创建订单
      .addCase(createOrder.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = false;
      })
      .addCase(createOrder.fulfilled, (state, action) => {
        state.loading = false;
        state.order = action.payload;
        state.success = true;
      })
      .addCase(createOrder.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        state.success = false;
      })
      // 获取订单详情
      .addCase(getOrderDetails.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getOrderDetails.fulfilled, (state, action) => {
        state.loading = false;
        state.order = action.payload;
      })
      .addCase(getOrderDetails.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // 获取用户订单列表
      .addCase(getUserOrders.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getUserOrders.fulfilled, (state, action) => {
        state.loading = false;
        state.orders = action.payload.orders;
        state.page = action.payload.page;
        state.pages = action.payload.pages;
        state.totalOrders = action.payload.totalOrders;
      })
      .addCase(getUserOrders.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // 取消订单
      .addCase(cancelOrder.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = false;
      })
      .addCase(cancelOrder.fulfilled, (state, action) => {
        state.loading = false;
        state.order = action.payload;
        state.success = true;
      })
      .addCase(cancelOrder.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        state.success = false;
      })
      // 获取所有订单（管理员）
      .addCase(getAllOrders.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getAllOrders.fulfilled, (state, action) => {
        state.loading = false;
        state.orders = action.payload.orders;
        state.page = action.payload.page;
        state.pages = action.payload.pages;
        state.totalOrders = action.payload.totalOrders;
      })
      .addCase(getAllOrders.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // 更新订单状态（管理员）
      .addCase(updateOrderStatus.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = false;
      })
      .addCase(updateOrderStatus.fulfilled, (state, action) => {
        state.loading = false;
        state.order = action.payload;
        state.success = true;
      })
      .addCase(updateOrderStatus.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        state.success = false;
      });
  },
});

export const { clearOrderDetails, clearOrderError, resetOrderSuccess } = orderSlice.actions;
export default orderSlice.reducer;