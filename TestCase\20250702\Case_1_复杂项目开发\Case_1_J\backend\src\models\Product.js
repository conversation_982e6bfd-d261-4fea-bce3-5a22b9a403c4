const mongoose = require('mongoose');

const specSchema = new mongoose.Schema({
  name: { type: String, required: true }, // 规格名称，如"颜色"、"尺寸"
  value: { type: String, required: true } // 规格值，如"红色"、"XL"
});

const skuSchema = new mongoose.Schema({
  specs: [specSchema], // 规格组合
  price: { type: Number, required: true },
  stock: { type: Number, required: true, default: 0 },
  sku: { type: String, required: true, unique: true }, // SKU编码
  image: { type: String } // SKU专属图片
});

const productSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  description: {
    type: String,
    required: true,
    maxlength: 2000
  },
  category: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category',
    required: true
  },
  brand: {
    type: String,
    required: true,
    trim: true
  },
  images: [{
    type: String,
    required: true
  }],
  basePrice: {
    type: Number,
    required: true,
    min: 0
  },
  originalPrice: {
    type: Number,
    min: 0
  },
  skus: [skuSchema], // SKU列表
  totalStock: {
    type: Number,
    default: 0
  },
  sales: {
    type: Number,
    default: 0
  },
  rating: {
    type: Number,
    default: 0,
    min: 0,
    max: 5
  },
  reviewCount: {
    type: Number,
    default: 0
  },
  tags: [String],
  status: {
    type: String,
    enum: ['active', 'inactive', 'out_of_stock'],
    default: 'active'
  },
  weight: {
    type: Number, // 重量(克)
    min: 0
  },
  dimensions: {
    length: Number,
    width: Number,
    height: Number
  },
  seoTitle: String,
  seoDescription: String,
  seoKeywords: [String],
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// 更新总库存
productSchema.pre('save', function(next) {
  if (this.skus && this.skus.length > 0) {
    this.totalStock = this.skus.reduce((total, sku) => total + sku.stock, 0);
  }
  this.updatedAt = Date.now();
  next();
});

// 索引
productSchema.index({ name: 'text', description: 'text', tags: 'text' });
productSchema.index({ category: 1 });
productSchema.index({ brand: 1 });
productSchema.index({ status: 1 });
productSchema.index({ createdAt: -1 });

module.exports = mongoose.model('Product', productSchema);
