const express = require('express');
const router = express.Router();
const orderController = require('../controllers/orderController');
const { protect, restrictTo } = require('../middleware/auth');

// 用户路由
router.post('/', protect, orderController.createOrder);
router.get('/', protect, orderController.getUserOrders);
router.get('/:id', protect, orderController.getOrderById);
router.put('/:id/cancel', protect, orderController.cancelOrder);

// 管理员路由
router.get('/admin', protect, restrictTo('admin'), orderController.getAllOrders);
router.get('/stats', protect, restrictTo('admin'), orderController.getOrderStats);
router.put(
  '/:id/status',
  protect,
  restrictTo('admin'),
  orderController.updateOrderStatus
);

module.exports = router;