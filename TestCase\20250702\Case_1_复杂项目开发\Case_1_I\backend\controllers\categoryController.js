const Category = require('../models/Category');
const { successResponse, errorResponse, paginatedResponse } = require('../utils/apiResponse');

/**
 * 创建分类
 * @route POST /api/categories
 * @access Private/Admin
 */
exports.createCategory = async (req, res, next) => {
  try {
    const { name, description, parent, image } = req.body;

    // 检查分类名称是否已存在
    const existingCategory = await Category.findOne({ name });
    if (existingCategory) {
      return errorResponse(res, '该分类名称已存在', 400);
    }

    // 创建新分类
    const category = await Category.create({
      name,
      description,
      parent: parent || null,
      image,
    });

    return successResponse(res, { category }, '分类创建成功', 201);
  } catch (error) {
    next(error);
  }
};

/**
 * 获取所有分类
 * @route GET /api/categories
 * @access Public
 */
exports.getAllCategories = async (req, res, next) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // 查询条件
    const query = {};
    if (req.query.name) {
      query.name = { $regex: req.query.name, $options: 'i' };
    }
    if (req.query.parent) {
      query.parent = req.query.parent;
    } else if (req.query.isRoot === 'true') {
      query.parent = null;
    }

    // 执行查询
    const categories = await Category.find(query)
      .populate('parent', 'name')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    // 获取总数
    const total = await Category.countDocuments(query);

    return paginatedResponse(
      res,
      categories,
      page,
      limit,
      total,
      '获取分类列表成功'
    );
  } catch (error) {
    next(error);
  }
};

/**
 * 获取分类树
 * @route GET /api/categories/tree
 * @access Public
 */
exports.getCategoryTree = async (req, res, next) => {
  try {
    // 获取所有分类
    const allCategories = await Category.find({ isActive: true }).sort({
      name: 1,
    });

    // 构建分类树
    const rootCategories = [];
    const categoriesMap = {};

    // 首先将所有分类放入映射表
    allCategories.forEach((category) => {
      categoriesMap[category._id] = {
        ...category.toObject(),
        children: [],
      };
    });

    // 然后构建树结构
    allCategories.forEach((category) => {
      if (category.parent) {
        // 如果有父分类，将其添加到父分类的children数组中
        if (categoriesMap[category.parent]) {
          categoriesMap[category.parent].children.push(
            categoriesMap[category._id]
          );
        }
      } else {
        // 如果没有父分类，则为根分类
        rootCategories.push(categoriesMap[category._id]);
      }
    });

    return successResponse(
      res,
      { categories: rootCategories },
      '获取分类树成功'
    );
  } catch (error) {
    next(error);
  }
};

/**
 * 获取单个分类
 * @route GET /api/categories/:id
 * @access Public
 */
exports.getCategoryById = async (req, res, next) => {
  try {
    const category = await Category.findById(req.params.id).populate(
      'parent',
      'name'
    );

    if (!category) {
      return errorResponse(res, '分类不存在', 404);
    }

    return successResponse(res, { category }, '获取分类信息成功');
  } catch (error) {
    next(error);
  }
};

/**
 * 更新分类
 * @route PUT /api/categories/:id
 * @access Private/Admin
 */
exports.updateCategory = async (req, res, next) => {
  try {
    const { name, description, parent, image, isActive } = req.body;

    const category = await Category.findById(req.params.id);

    if (!category) {
      return errorResponse(res, '分类不存在', 404);
    }

    // 如果更改了名称，检查是否与其他分类重复
    if (name && name !== category.name) {
      const existingCategory = await Category.findOne({ name });
      if (existingCategory) {
        return errorResponse(res, '该分类名称已存在', 400);
      }
      category.name = name;
    }

    // 更新其他字段
    if (description !== undefined) category.description = description;
    if (parent !== undefined) category.parent = parent || null;
    if (image !== undefined) category.image = image;
    if (isActive !== undefined) category.isActive = isActive;

    const updatedCategory = await category.save();

    return successResponse(
      res,
      { category: updatedCategory },
      '分类更新成功'
    );
  } catch (error) {
    next(error);
  }
};

/**
 * 删除分类
 * @route DELETE /api/categories/:id
 * @access Private/Admin
 */
exports.deleteCategory = async (req, res, next) => {
  try {
    const category = await Category.findById(req.params.id);

    if (!category) {
      return errorResponse(res, '分类不存在', 404);
    }

    // 检查是否有子分类
    const childCategories = await Category.find({ parent: req.params.id });
    if (childCategories.length > 0) {
      return errorResponse(
        res,
        '该分类下有子分类，无法删除。请先删除或移动子分类',
        400
      );
    }

    // 检查是否有关联的商品（在实际应用中需要实现）

    await category.remove();

    return successResponse(res, {}, '分类删除成功');
  } catch (error) {
    next(error);
  }
};

/**
 * 上传分类图片
 * @route POST /api/categories/:id/image
 * @access Private/Admin
 */
exports.uploadCategoryImage = async (req, res, next) => {
  try {
    if (!req.file) {
      return errorResponse(res, '请选择要上传的图片', 400);
    }

    const category = await Category.findById(req.params.id);

    if (!category) {
      return errorResponse(res, '分类不存在', 404);
    }

    // 更新图片路径
    category.image = `/uploads/categories/${req.file.filename}`;
    await category.save();

    return successResponse(
      res,
      { image: category.image },
      '分类图片上传成功'
    );
  } catch (error) {
    next(error);
  }
};