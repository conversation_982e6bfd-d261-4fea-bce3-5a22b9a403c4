const express = require('express');
const router = express.Router();
const {
  createOrder,
  getMyOrders,
  getOrderById,
  cancelOrder,
  updateOrderToPaid,
  getAllOrders,
  updateOrderStatus
} = require('../controllers/order.controller');
const { protect, authorize } = require('../middleware/auth.middleware');

// 所有订单路由都需要登录
router.use(protect);

// 用户路由
router.post('/', createOrder);
router.get('/myorders', getMyOrders);
router.get('/:id', getOrderById);
router.put('/:id/cancel', cancelOrder);
router.put('/:id/pay', updateOrderToPaid);

// 管理员路由
router.get('/admin/all', authorize('admin'), getAllOrders);
router.put('/:id/status', authorize('admin'), updateOrderStatus);

module.exports = router; 