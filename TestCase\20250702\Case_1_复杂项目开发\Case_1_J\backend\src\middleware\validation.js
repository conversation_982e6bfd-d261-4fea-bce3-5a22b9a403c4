const { body, validationResult } = require('express-validator');

// 处理验证错误
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      message: '输入数据验证失败',
      errors: errors.array()
    });
  }
  next();
};

// 用户注册验证
const validateRegister = [
  body('username')
    .isLength({ min: 3, max: 20 })
    .withMessage('用户名长度必须在3-20个字符之间')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('用户名只能包含字母、数字和下划线'),
  body('email')
    .isEmail()
    .withMessage('请输入有效的邮箱地址')
    .normalizeEmail(),
  body('password')
    .isLength({ min: 6 })
    .withMessage('密码长度至少6个字符')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('密码必须包含大小写字母和数字'),
  handleValidationErrors
];

// 用户登录验证
const validateLogin = [
  body('email')
    .isEmail()
    .withMessage('请输入有效的邮箱地址')
    .normalizeEmail(),
  body('password')
    .notEmpty()
    .withMessage('密码不能为空'),
  handleValidationErrors
];

// 商品创建验证
const validateProduct = [
  body('name')
    .isLength({ min: 1, max: 100 })
    .withMessage('商品名称长度必须在1-100个字符之间'),
  body('description')
    .isLength({ min: 1, max: 2000 })
    .withMessage('商品描述长度必须在1-2000个字符之间'),
  body('category')
    .isMongoId()
    .withMessage('无效的分类ID'),
  body('brand')
    .notEmpty()
    .withMessage('品牌不能为空'),
  body('basePrice')
    .isFloat({ min: 0 })
    .withMessage('基础价格必须大于等于0'),
  body('images')
    .isArray({ min: 1 })
    .withMessage('至少需要一张商品图片'),
  handleValidationErrors
];

// 订单创建验证
const validateOrder = [
  body('items')
    .isArray({ min: 1 })
    .withMessage('订单至少需要一个商品'),
  body('items.*.product')
    .isMongoId()
    .withMessage('无效的商品ID'),
  body('items.*.quantity')
    .isInt({ min: 1 })
    .withMessage('商品数量必须大于0'),
  body('shippingAddress.name')
    .notEmpty()
    .withMessage('收货人姓名不能为空'),
  body('shippingAddress.phone')
    .matches(/^1[3-9]\d{9}$/)
    .withMessage('请输入有效的手机号码'),
  body('paymentMethod')
    .isIn(['alipay', 'wechat', 'bank_card'])
    .withMessage('无效的支付方式'),
  handleValidationErrors
];

// 地址验证
const validateAddress = [
  body('name')
    .notEmpty()
    .withMessage('收货人姓名不能为空'),
  body('phone')
    .matches(/^1[3-9]\d{9}$/)
    .withMessage('请输入有效的手机号码'),
  body('province')
    .notEmpty()
    .withMessage('省份不能为空'),
  body('city')
    .notEmpty()
    .withMessage('城市不能为空'),
  body('district')
    .notEmpty()
    .withMessage('区县不能为空'),
  body('detail')
    .notEmpty()
    .withMessage('详细地址不能为空'),
  handleValidationErrors
];

module.exports = {
  validateRegister,
  validateLogin,
  validateProduct,
  validateOrder,
  validateAddress,
  handleValidationErrors
};
