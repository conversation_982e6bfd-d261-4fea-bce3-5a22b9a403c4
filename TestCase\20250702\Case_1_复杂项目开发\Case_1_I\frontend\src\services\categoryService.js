import apiClient from './apiClient';

// 获取所有分类
const getCategories = async () => {
  const response = await apiClient.get('/categories');
  return response.data;
};

// 获取分类树
const getCategoryTree = async () => {
  const response = await apiClient.get('/categories/tree');
  return response.data;
};

// 获取分类详情
const getCategoryDetails = async (id) => {
  const response = await apiClient.get(`/categories/${id}`);
  return response.data;
};

// 创建分类（管理员）
const createCategory = async (categoryData, token) => {
  const response = await apiClient.post('/categories', categoryData, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

// 更新分类（管理员）
const updateCategory = async (id, categoryData, token) => {
  const response = await apiClient.put(`/categories/${id}`, categoryData, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

// 删除分类（管理员）
const deleteCategory = async (id, token) => {
  const response = await apiClient.delete(`/categories/${id}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

// 上传分类图片（管理员）
const uploadCategoryImage = async (id, formData, token) => {
  const response = await apiClient.post(`/categories/${id}/upload`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

const categoryService = {
  getCategories,
  getCategoryTree,
  getCategoryDetails,
  createCategory,
  updateCategory,
  deleteCategory,
  uploadCategoryImage,
};

export default categoryService;