document.addEventListener('DOMContentLoaded', () => {
    const productList = document.querySelector('.product-list');
    const products = [
        { id: 1, name: '商品1', price: 100 },
        { id: 2, name: '商品2', price: 200 },
        { id: 3, name: '商品3', price: 300 },
    ];

    products.forEach(product => {
        const productElement = document.createElement('div');
        productElement.className = 'product';
        productElement.innerHTML = `
            <h3>${product.name}</h3>
            <p>价格: ¥${product.price}</p>
            <button onclick="addToCart(${product.id})">加入购物车</button>
        `;
        productList.appendChild(productElement);
    });
});

function addToCart(productId) {
    console.log(`商品 ${productId} 已加入购物车`);
}