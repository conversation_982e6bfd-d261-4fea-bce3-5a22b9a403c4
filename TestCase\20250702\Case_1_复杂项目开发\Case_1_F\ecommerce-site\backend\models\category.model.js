const mongoose = require('mongoose');

const CategorySchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, '请提供分类名称'],
    unique: true,
    trim: true,
    maxlength: [50, '分类名称不能超过50个字符']
  },
  description: {
    type: String,
    maxlength: [500, '分类描述不能超过500个字符']
  },
  image: {
    type: String,
    default: 'default-category.jpg'
  },
  parent: {
    type: mongoose.Schema.ObjectId,
    ref: 'Category',
    default: null
  },
  level: {
    type: Number,
    default: 1
  },
  isActive: {
    type: Boolean,
    default: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
}, {
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// 虚拟字段 - 子分类
CategorySchema.virtual('children', {
  ref: 'Category',
  localField: '_id',
  foreignField: 'parent',
  justOne: false
});

// 前置中间件 - 自动设置层级
CategorySchema.pre('save', async function(next) {
  if (this.parent) {
    try {
      const parentCategory = await mongoose.model('Category').findById(this.parent);
      if (parentCategory) {
        this.level = parentCategory.level + 1;
      }
    } catch (err) {
      console.error('设置分类层级失败:', err);
    }
  }
  next();
});

module.exports = mongoose.model('Category', CategorySchema); 