# 电商网站项目

这是一个完整的电商网站项目，包含前后端实现，具备用户管理、商品管理、订单系统和支付功能。

## 项目结构

```
├── backend/             # 后端代码
│   ├── config/         # 配置文件
│   ├── controllers/    # 控制器
│   ├── middleware/     # 中间件
│   ├── models/         # 数据模型
│   ├── routes/         # 路由
│   ├── services/       # 服务
│   ├── utils/          # 工具函数
│   ├── app.js          # 应用入口
│   └── package.json    # 后端依赖
│
├── frontend/           # 前端代码
│   ├── public/         # 静态资源
│   ├── src/            # 源代码
│   │   ├── assets/     # 资源文件
│   │   ├── components/ # 组件
│   │   ├── pages/      # 页面
│   │   ├── redux/      # 状态管理
│   │   ├── services/   # API服务
│   │   ├── utils/      # 工具函数
│   │   ├── App.js      # 应用组件
│   │   └── index.js    # 入口文件
│   └── package.json    # 前端依赖
```

## 核心功能

1. 用户管理
   - 注册/登录
   - 个人信息管理
   - 地址管理

2. 商品管理
   - 商品分类
   - 商品列表/详情
   - 搜索功能

3. 购物车
   - 添加/删除商品
   - 修改数量
   - 结算

4. 订单系统
   - 订单创建
   - 订单状态跟踪
   - 订单历史

5. 支付功能
   - 支付接口集成
   - 支付状态管理

## 技术栈

### 后端
- Node.js
- Express
- MongoDB
- JWT认证

### 前端
- React
- Redux
- Ant Design
- Axios