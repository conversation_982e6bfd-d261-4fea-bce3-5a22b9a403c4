const express = require('express');
const router = express.Router();
const cartController = require('../controllers/cartController');
const { protect } = require('../middleware/auth');

// 所有购物车路由都需要登录
router.use(protect);

// 获取购物车
router.get('/', cartController.getCart);

// 添加商品到购物车
router.post('/', cartController.addToCart);

// 更新购物车商品数量
router.put('/:itemId', cartController.updateCartItem);

// 从购物车中删除商品
router.delete('/:itemId', cartController.removeFromCart);

// 清空购物车
router.delete('/', cartController.clearCart);

module.exports = router;