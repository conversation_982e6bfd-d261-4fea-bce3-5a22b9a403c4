import React, { useState } from 'react';
import { Card, Avatar, Typography, Button, Form, Input, Row, Col, Upload, message } from 'antd';
import { UserOutlined, UploadOutlined, EditOutlined, SaveOutlined } from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { updateUserProfile } from '../../redux/slices/authSlice';
import { uploadAvatar } from '../../services/userService';

const { Title, Text } = Typography;

const UserInfo = ({ user }) => {
  const dispatch = useDispatch();
  const { loading } = useSelector((state) => state.auth);
  const [form] = Form.useForm();
  const [editing, setEditing] = useState(false);
  const [avatarLoading, setAvatarLoading] = useState(false);
  const [avatar, setAvatar] = useState(user?.avatar || null);

  // 切换编辑模式
  const toggleEdit = () => {
    if (editing) {
      setEditing(false);
    } else {
      form.setFieldsValue({
        name: user.name,
        email: user.email,
        phone: user.phone || '',
      });
      setEditing(true);
    }
  };

  // 提交表单
  const handleSubmit = (values) => {
    dispatch(updateUserProfile(values))
      .unwrap()
      .then(() => {
        setEditing(false);
        message.success('个人信息更新成功');
      })
      .catch((error) => {
        message.error(`更新失败: ${error.message}`);
      });
  };

  // 上传头像
  const handleAvatarUpload = async (file) => {
    setAvatarLoading(true);
    try {
      const result = await uploadAvatar(file);
      setAvatar(result.avatar);
      message.success('头像上传成功');
    } catch (error) {
      message.error('头像上传失败');
    } finally {
      setAvatarLoading(false);
    }
  };

  // 自定义上传
  const customUpload = ({ file, onSuccess, onError }) => {
    handleAvatarUpload(file)
      .then(() => onSuccess())
      .catch((error) => onError(error));
  };

  return (
    <Card className="user-info-card">
      <div className="user-info-header" style={{ display: 'flex', alignItems: 'center', marginBottom: '24px' }}>
        <div className="user-avatar" style={{ marginRight: '16px' }}>
          <Upload
            customRequest={customUpload}
            showUploadList={false}
            beforeUpload={(file) => {
              const isImage = file.type.startsWith('image/');
              if (!isImage) {
                message.error('只能上传图片文件!');
                return false;
              }
              const isLt2M = file.size / 1024 / 1024 < 2;
              if (!isLt2M) {
                message.error('图片必须小于 2MB!');
                return false;
              }
              return true;
            }}
          >
            <div className="avatar-wrapper" style={{ position: 'relative' }}>
              <Avatar
                size={80}
                src={avatar}
                icon={<UserOutlined />}
                style={{ cursor: 'pointer' }}
              />
              <div
                className="avatar-upload-overlay"
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  background: 'rgba(0, 0, 0, 0.5)',
                  borderRadius: '50%',
                  opacity: 0,
                  transition: 'opacity 0.3s',
                }}
              >
                <UploadOutlined style={{ color: '#fff', fontSize: '20px' }} />
              </div>
            </div>
          </Upload>
        </div>
        <div className="user-title">
          <Title level={4} style={{ margin: 0 }}>{user.name}</Title>
          <Text type="secondary">{user.role === 'admin' ? '管理员' : '普通用户'}</Text>
        </div>
        <div style={{ marginLeft: 'auto' }}>
          <Button
            type="primary"
            icon={editing ? <SaveOutlined /> : <EditOutlined />}
            onClick={toggleEdit}
          >
            {editing ? '保存' : '编辑'}
          </Button>
        </div>
      </div>

      {editing ? (
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            name: user.name,
            email: user.email,
            phone: user.phone || '',
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="姓名"
                rules={[{ required: true, message: '请输入姓名' }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="email"
                label="邮箱"
                rules={[
                  { required: true, message: '请输入邮箱' },
                  { type: 'email', message: '请输入有效的邮箱地址' },
                ]}
              >
                <Input disabled />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="phone"
                label="手机号码"
                rules={[
                  { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码' },
                ]}
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading}>
              保存
            </Button>
            <Button style={{ marginLeft: '8px' }} onClick={() => setEditing(false)}>
              取消
            </Button>
          </Form.Item>
        </Form>
      ) : (
        <div className="user-info-content">
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <div className="info-item">
                <Text type="secondary">姓名</Text>
                <div>{user.name}</div>
              </div>
            </Col>
            <Col span={12}>
              <div className="info-item">
                <Text type="secondary">邮箱</Text>
                <div>{user.email}</div>
              </div>
            </Col>
            <Col span={12}>
              <div className="info-item">
                <Text type="secondary">手机号码</Text>
                <div>{user.phone || '未设置'}</div>
              </div>
            </Col>
            <Col span={12}>
              <div className="info-item">
                <Text type="secondary">注册时间</Text>
                <div>{new Date(user.createdAt).toLocaleDateString()}</div>
              </div>
            </Col>
          </Row>
        </div>
      )}
    </Card>
  );
};

export default UserInfo;