import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Select, Row, Col, Checkbox } from 'antd';

const { Option } = Select;

// 省份数据（示例）
const provinces = [
  '北京市',
  '上海市',
  '天津市',
  '重庆市',
  '河北省',
  '山西省',
  '辽宁省',
  '吉林省',
  '黑龙江省',
  '江苏省',
  '浙江省',
  '安徽省',
  '福建省',
  '江西省',
  '山东省',
  '河南省',
  '湖北省',
  '湖南省',
  '广东省',
  '海南省',
  '四川省',
  '贵州省',
  '云南省',
  '陕西省',
  '甘肃省',
  '青海省',
  '台湾省',
  '内蒙古自治区',
  '广西壮族自治区',
  '西藏自治区',
  '宁夏回族自治区',
  '新疆维吾尔自治区',
  '香港特别行政区',
  '澳门特别行政区',
];

// 简化版的城市数据（示例）
const getCities = (province) => {
  // 实际应用中应该根据省份返回对应的城市列表
  // 这里仅作示例
  return ['城市1', '城市2', '城市3'];
};

// 简化版的区县数据（示例）
const getDistricts = (city) => {
  // 实际应用中应该根据城市返回对应的区县列表
  // 这里仅作示例
  return ['区县1', '区县2', '区县3'];
};

const AddressForm = ({ initialValues = {}, onSubmit, loading = false }) => {
  const [form] = Form.useForm();
  const [province, setProvince] = useState(initialValues.province || '');
  const [city, setCity] = useState(initialValues.city || '');
  const [cities, setCities] = useState([]);
  const [districts, setDistricts] = useState([]);

  // 当省份变化时，更新城市列表
  useEffect(() => {
    if (province) {
      const newCities = getCities(province);
      setCities(newCities);
      // 如果当前选择的城市不在新的城市列表中，清空城市选择
      if (!newCities.includes(city)) {
        form.setFieldsValue({ city: undefined, district: undefined });
        setCity('');
        setDistricts([]);
      }
    } else {
      setCities([]);
      form.setFieldsValue({ city: undefined, district: undefined });
      setCity('');
      setDistricts([]);
    }
  }, [province, city, form]);

  // 当城市变化时，更新区县列表
  useEffect(() => {
    if (city) {
      const newDistricts = getDistricts(city);
      setDistricts(newDistricts);
      // 如果当前选择的区县不在新的区县列表中，清空区县选择
      if (!newDistricts.includes(form.getFieldValue('district'))) {
        form.setFieldsValue({ district: undefined });
      }
    } else {
      setDistricts([]);
      form.setFieldsValue({ district: undefined });
    }
  }, [city, form]);

  // 初始化表单数据
  useEffect(() => {
    if (initialValues.province) {
      setProvince(initialValues.province);
      const newCities = getCities(initialValues.province);
      setCities(newCities);
      
      if (initialValues.city) {
        setCity(initialValues.city);
        setDistricts(getDistricts(initialValues.city));
      }
    }
    
    form.setFieldsValue(initialValues);
  }, [initialValues, form]);

  // 处理表单提交
  const handleSubmit = (values) => {
    onSubmit && onSubmit(values);
  };

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSubmit}
      initialValues={initialValues}
    >
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            name="name"
            label="收货人"
            rules={[{ required: true, message: '请输入收货人姓名' }]}
          >
            <Input placeholder="请输入收货人姓名" />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item
            name="phone"
            label="手机号码"
            rules={[
              { required: true, message: '请输入手机号码' },
              { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码' },
            ]}
          >
            <Input placeholder="请输入手机号码" />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            name="province"
            label="省份"
            rules={[{ required: true, message: '请选择省份' }]}
          >
            <Select
              placeholder="请选择省份"
              onChange={(value) => setProvince(value)}
            >
              {provinces.map((p) => (
                <Option key={p} value={p}>
                  {p}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            name="city"
            label="城市"
            rules={[{ required: true, message: '请选择城市' }]}
          >
            <Select
              placeholder="请选择城市"
              disabled={!province}
              onChange={(value) => setCity(value)}
            >
              {cities.map((c) => (
                <Option key={c} value={c}>
                  {c}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            name="district"
            label="区/县"
            rules={[{ required: true, message: '请选择区/县' }]}
          >
            <Select placeholder="请选择区/县" disabled={!city}>
              {districts.map((d) => (
                <Option key={d} value={d}>
                  {d}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item
            name="address"
            label="详细地址"
            rules={[{ required: true, message: '请输入详细地址' }]}
          >
            <Input.TextArea
              placeholder="请输入详细地址，如街道、门牌号等"
              rows={3}
            />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item
            name="postalCode"
            label="邮政编码"
            rules={[
              { pattern: /^\d{6}$/, message: '请输入有效的邮政编码' },
            ]}
          >
            <Input placeholder="请输入邮政编码（选填）" />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item name="isDefault" valuePropName="checked">
            <Checkbox>设为默认地址</Checkbox>
          </Form.Item>
        </Col>
      </Row>
      <Form.Item>
        <Button type="primary" htmlType="submit" loading={loading}>
          {initialValues._id ? '更新地址' : '添加地址'}
        </Button>
      </Form.Item>
    </Form>
  );
};

export default AddressForm;