const Category = require('../models/category.model');
const { validationResult } = require('express-validator');

// @desc    获取所有分类
// @route   GET /api/categories
// @access  公开
exports.getCategories = async (req, res) => {
  try {
    const { parentOnly } = req.query;
    const query = parentOnly === 'true' ? { parent: null } : {};

    const categories = await Category.find(query)
      .populate('parent', 'name')
      .sort({ name: 1 });

    res.status(200).json({
      success: true,
      count: categories.length,
      data: categories
    });
  } catch (err) {
    console.error('获取分类失败:', err);
    res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
  }
};

// @desc    获取单个分类
// @route   GET /api/categories/:id
// @access  公开
exports.getCategory = async (req, res) => {
  try {
    const category = await Category.findById(req.params.id)
      .populate('parent', 'name');
    
    if (!category) {
      return res.status(404).json({ success: false, message: '未找到该分类' });
    }
    
    res.status(200).json({ success: true, data: category });
  } catch (err) {
    console.error('获取分类详情失败:', err);
    res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
  }
};

// @desc    获取分类及其子分类
// @route   GET /api/categories/:id/with-children
// @access  公开
exports.getCategoryWithChildren = async (req, res) => {
  try {
    const category = await Category.findById(req.params.id);
    
    if (!category) {
      return res.status(404).json({ success: false, message: '未找到该分类' });
    }
    
    // 获取所有子分类
    const children = await Category.find({ parent: category._id });
    
    res.status(200).json({
      success: true,
      data: {
        ...category.toObject(),
        children
      }
    });
  } catch (err) {
    console.error('获取分类及子分类失败:', err);
    res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
  }
};

// @desc    创建分类
// @route   POST /api/categories
// @access  私有 (仅限管理员)
exports.createCategory = async (req, res) => {
  try {
    // 验证请求数据
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }

    const { name, description, image, parent } = req.body;

    // 检查分类名称是否已存在
    const existingCategory = await Category.findOne({ name });
    if (existingCategory) {
      return res.status(400).json({ success: false, message: '该分类名称已存在' });
    }

    // 创建新分类
    const categoryData = {
      name,
      description,
      image,
    };

    // 如果有父分类，验证它是否存在
    if (parent) {
      const parentCategory = await Category.findById(parent);
      if (!parentCategory) {
        return res.status(404).json({ success: false, message: '父分类不存在' });
      }
      categoryData.parent = parent;
      // 子分类的层级是父分类层级+1
      categoryData.level = parentCategory.level + 1;
    }

    const category = await Category.create(categoryData);
    
    res.status(201).json({ success: true, data: category });
  } catch (err) {
    console.error('创建分类失败:', err);
    res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
  }
};

// @desc    更新分类
// @route   PUT /api/categories/:id
// @access  私有 (仅限管理员)
exports.updateCategory = async (req, res) => {
  try {
    const { name, description, image, isActive } = req.body;
    
    const category = await Category.findById(req.params.id);
    
    if (!category) {
      return res.status(404).json({ success: false, message: '未找到该分类' });
    }

    // 如果要更新名称，检查是否已存在
    if (name && name !== category.name) {
      const existingCategory = await Category.findOne({ name });
      if (existingCategory) {
        return res.status(400).json({ success: false, message: '该分类名称已存在' });
      }
      category.name = name;
    }

    // 更新其他字段
    if (description !== undefined) category.description = description;
    if (image !== undefined) category.image = image;
    if (isActive !== undefined) category.isActive = isActive;

    await category.save();
    
    res.status(200).json({ success: true, data: category });
  } catch (err) {
    console.error('更新分类失败:', err);
    res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
  }
};

// @desc    删除分类
// @route   DELETE /api/categories/:id
// @access  私有 (仅限管理员)
exports.deleteCategory = async (req, res) => {
  try {
    const category = await Category.findById(req.params.id);
    
    if (!category) {
      return res.status(404).json({ success: false, message: '未找到该分类' });
    }

    // 检查是否有子分类
    const childrenCount = await Category.countDocuments({ parent: category._id });
    if (childrenCount > 0) {
      return res.status(400).json({ success: false, message: '该分类包含子分类，无法删除' });
    }

    // 检查是否有关联的商品
    // 在实际项目中，需要检查是否有商品使用此分类
    // const productsCount = await Product.countDocuments({ category: category._id });
    // if (productsCount > 0) {
    //   return res.status(400).json({ success: false, message: '该分类下有商品，无法删除' });
    // }

    await category.deleteOne();
    
    res.status(200).json({ success: true, message: '分类已成功删除' });
  } catch (err) {
    console.error('删除分类失败:', err);
    res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
  }
}; 