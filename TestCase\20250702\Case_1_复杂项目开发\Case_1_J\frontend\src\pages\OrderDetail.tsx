import React, { useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { Card, Button, Typography, Space, Steps, Tag, Image, Descriptions, Spin, Result } from 'antd';
import { ArrowLeftOutlined, PayCircleOutlined, CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../store';
import { fetchOrderByNo, cancelOrder, confirmOrder, clearCurrentOrder } from '../store/slices/orderSlice';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Step } = Steps;

const OrderDetail: React.FC = () => {
  const { orderNo } = useParams<{ orderNo: string }>();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { currentOrder: order, loading } = useSelector((state: RootState) => state.order);

  useEffect(() => {
    if (orderNo) {
      dispatch(fetchOrderByNo(orderNo) as any);
    }
    return () => {
      dispatch(clearCurrentOrder());
    };
  }, [dispatch, orderNo]);

  const handleBack = () => {
    navigate('/orders');
  };

  const handlePayOrder = () => {
    if (order) {
      navigate(`/payment/${order.orderNo}`);
    }
  };

  const handleCancelOrder = async () => {
    if (order) {
      try {
        await dispatch(cancelOrder(order.orderNo) as any).unwrap();
      } catch (error) {
        // 错误已在slice中处理
      }
    }
  };

  const handleConfirmOrder = async () => {
    if (order) {
      try {
        await dispatch(confirmOrder(order.orderNo) as any).unwrap();
      } catch (error) {
        // 错误已在slice中处理
      }
    }
  };

  const getStatusTag = (status: string) => {
    const statusConfig = {
      pending: { color: 'orange', text: '待付款' },
      paid: { color: 'blue', text: '已付款' },
      shipped: { color: 'cyan', text: '已发货' },
      delivered: { color: 'purple', text: '已送达' },
      completed: { color: 'green', text: '已完成' },
      cancelled: { color: 'red', text: '已取消' },
      refunded: { color: 'magenta', text: '已退款' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const getOrderSteps = () => {
    if (!order) return [];

    const steps = [
      {
        title: '订单创建',
        description: dayjs(order.createdAt).format('MM-DD HH:mm'),
        status: 'finish',
      },
    ];

    if (order.status !== 'cancelled') {
      steps.push({
        title: '付款确认',
        description: order.paidAt ? dayjs(order.paidAt).format('MM-DD HH:mm') : '',
        status: order.paidAt ? 'finish' : order.status === 'pending' ? 'process' : 'wait',
      });

      if (order.status !== 'pending') {
        steps.push({
          title: '商品发货',
          description: order.shippedAt ? dayjs(order.shippedAt).format('MM-DD HH:mm') : '',
          status: order.shippedAt ? 'finish' : order.status === 'paid' ? 'process' : 'wait',
        });

        if (['shipped', 'delivered', 'completed'].includes(order.status)) {
          steps.push({
            title: '确认收货',
            description: order.deliveredAt ? dayjs(order.deliveredAt).format('MM-DD HH:mm') : '',
            status: order.deliveredAt ? 'finish' : order.status === 'shipped' ? 'process' : 'wait',
          });
        }

        if (order.status === 'completed') {
          steps.push({
            title: '交易完成',
            description: order.completedAt ? dayjs(order.completedAt).format('MM-DD HH:mm') : '',
            status: 'finish',
          });
        }
      }
    } else {
      steps.push({
        title: '订单取消',
        description: order.cancelledAt ? dayjs(order.cancelledAt).format('MM-DD HH:mm') : '',
        status: 'error',
      });
    }

    return steps;
  };

  const getOrderActions = () => {
    if (!order) return [];

    const actions = [];

    switch (order.status) {
      case 'pending':
        actions.push(
          <Button
            key="pay"
            type="primary"
            icon={<PayCircleOutlined />}
            onClick={handlePayOrder}
          >
            立即付款
          </Button>
        );
        actions.push(
          <Button
            key="cancel"
            danger
            icon={<CloseCircleOutlined />}
            onClick={handleCancelOrder}
          >
            取消订单
          </Button>
        );
        break;
      case 'shipped':
        actions.push(
          <Button
            key="confirm"
            type="primary"
            icon={<CheckCircleOutlined />}
            onClick={handleConfirmOrder}
          >
            确认收货
          </Button>
        );
        break;
    }

    return actions;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Spin size="large" />
      </div>
    );
  }

  if (!order) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Result
          status="404"
          title="订单不存在"
          subTitle="请检查订单号是否正确"
          extra={
            <Button type="primary" onClick={handleBack}>
              返回订单列表
            </Button>
          }
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* 页面头部 */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <Button
              type="text"
              icon={<ArrowLeftOutlined />}
              onClick={handleBack}
            >
              返回
            </Button>
            <Title level={2} className="mb-0">
              订单详情
            </Title>
            {getStatusTag(order.status)}
          </div>
          <Space>
            {getOrderActions()}
          </Space>
        </div>

        {/* 订单进度 */}
        <Card className="mb-6" title="订单进度">
          <Steps current={-1} items={getOrderSteps()} />
        </Card>

        {/* 订单信息 */}
        <Card className="mb-6" title="订单信息">
          <Descriptions column={2} bordered>
            <Descriptions.Item label="订单号">{order.orderNo}</Descriptions.Item>
            <Descriptions.Item label="订单状态">{getStatusTag(order.status)}</Descriptions.Item>
            <Descriptions.Item label="创建时间">
              {dayjs(order.createdAt).format('YYYY-MM-DD HH:mm:ss')}
            </Descriptions.Item>
            <Descriptions.Item label="支付方式">
              {order.paymentMethod === 'alipay' && '支付宝'}
              {order.paymentMethod === 'wechat' && '微信支付'}
              {order.paymentMethod === 'bank_card' && '银行卡'}
            </Descriptions.Item>
            {order.paidAt && (
              <Descriptions.Item label="支付时间">
                {dayjs(order.paidAt).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
            )}
            {order.trackingNumber && (
              <Descriptions.Item label="快递单号">{order.trackingNumber}</Descriptions.Item>
            )}
            {order.courier && (
              <Descriptions.Item label="快递公司">{order.courier}</Descriptions.Item>
            )}
            {order.remark && (
              <Descriptions.Item label="订单备注" span={2}>{order.remark}</Descriptions.Item>
            )}
          </Descriptions>
        </Card>

        {/* 收货地址 */}
        <Card className="mb-6" title="收货地址">
          <div>
            <Text strong>{order.shippingAddress.name}</Text>
            <Text className="ml-4">{order.shippingAddress.phone}</Text>
          </div>
          <div className="mt-2 text-gray-600">
            {order.shippingAddress.province} {order.shippingAddress.city} {order.shippingAddress.district} {order.shippingAddress.detail}
          </div>
        </Card>

        {/* 商品清单 */}
        <Card title="商品清单">
          <div className="space-y-4">
            {order.items.map((item, index) => (
              <div key={index} className="flex items-center space-x-4 p-4 border rounded">
                <Image
                  src={item.image}
                  alt={item.name}
                  width={80}
                  height={80}
                  className="rounded object-cover"
                  preview={false}
                />
                <div className="flex-1">
                  <Text strong className="block text-lg">{item.name}</Text>
                  {item.specs.length > 0 && (
                    <div className="text-sm text-gray-500 mt-1">
                      {item.specs.map((spec, i) => (
                        <span key={i} className="mr-3">
                          {spec.name}: {spec.value}
                        </span>
                      ))}
                    </div>
                  )}
                  <div className="mt-2">
                    <Text>单价: ¥{item.price}</Text>
                    <Text className="ml-4">数量: {item.quantity}</Text>
                  </div>
                </div>
                <div className="text-right">
                  <Text strong className="text-lg">¥{item.subtotal.toFixed(2)}</Text>
                </div>
              </div>
            ))}
          </div>

          {/* 费用明细 */}
          <div className="mt-6 pt-4 border-t">
            <div className="flex justify-end">
              <div className="w-80">
                <div className="flex justify-between mb-2">
                  <Text>商品总价:</Text>
                  <Text>¥{order.totalAmount.toFixed(2)}</Text>
                </div>
                <div className="flex justify-between mb-2">
                  <Text>运费:</Text>
                  <Text>¥{order.shippingFee.toFixed(2)}</Text>
                </div>
                {order.discountAmount > 0 && (
                  <div className="flex justify-between mb-2">
                    <Text>优惠金额:</Text>
                    <Text className="text-red-500">-¥{order.discountAmount.toFixed(2)}</Text>
                  </div>
                )}
                <div className="flex justify-between pt-2 border-t">
                  <Text strong className="text-lg">应付总额:</Text>
                  <Text strong className="text-lg text-red-500">
                    ¥{order.finalAmount.toFixed(2)}
                  </Text>
                </div>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default OrderDetail;
