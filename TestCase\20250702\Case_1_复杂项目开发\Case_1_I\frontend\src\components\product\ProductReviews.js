import React, { useState } from 'react';
import { List, Comment, Avatar, Form, Button, Input, Rate, Divider, Typography } from 'antd';
import { useSelector, useDispatch } from 'react-redux';
import { createProductReview } from '../../redux/slices/productSlice';
import { formatDate } from '../../utils/formatUtils';

const { TextArea } = Input;
const { Title, Text } = Typography;

const ProductReviews = ({ productId, reviews = [] }) => {
  const dispatch = useDispatch();
  const { userInfo } = useSelector((state) => state.auth);
  const { loading } = useSelector((state) => state.product);
  const [form] = Form.useForm();
  const [rating, setRating] = useState(5);

  // 提交评论
  const handleSubmit = (values) => {
    dispatch(createProductReview({
      productId,
      rating,
      comment: values.comment,
    })).then(() => {
      form.resetFields();
      setRating(5);
    });
  };

  // 评论表单
  const ReviewForm = () => {
    if (!userInfo) {
      return (
        <div className="review-login-prompt">
          <Text>请 <a href="/login?redirect=back">登录</a> 后发表评论</Text>
        </div>
      );
    }

    // 检查用户是否已经评论过
    const hasReviewed = reviews.some(review => review.user._id === userInfo._id);
    if (hasReviewed) {
      return (
        <div className="review-already-submitted">
          <Text type="secondary">您已经评论过此商品</Text>
        </div>
      );
    }

    return (
      <Form form={form} onFinish={handleSubmit}>
        <Form.Item name="rating" label="评分">
          <Rate allowHalf value={rating} onChange={setRating} />
        </Form.Item>
        <Form.Item
          name="comment"
          rules={[{ required: true, message: '请输入评论内容' }]}
        >
          <TextArea rows={4} placeholder="分享您对这个商品的看法..." />
        </Form.Item>
        <Form.Item>
          <Button type="primary" htmlType="submit" loading={loading}>
            提交评论
          </Button>
        </Form.Item>
      </Form>
    );
  };

  return (
    <div className="product-reviews">
      <Title level={3}>商品评价 ({reviews.length})</Title>
      
      <Divider />
      
      {/* 评论表单 */}
      <div className="review-form-container" style={{ marginBottom: '24px' }}>
        <ReviewForm />
      </div>
      
      {/* 评论列表 */}
      {reviews.length > 0 ? (
        <List
          className="comment-list"
          header={`${reviews.length} 条评论`}
          itemLayout="horizontal"
          dataSource={reviews}
          renderItem={(review) => (
            <li>
              <Comment
                author={<Text strong>{review.user.name}</Text>}
                avatar={<Avatar src={review.user.avatar || 'https://joeschmoe.io/api/v1/random'} alt={review.user.name} />}
                content={
                  <div>
                    <Rate disabled defaultValue={review.rating} />
                    <p style={{ marginTop: '8px' }}>{review.comment}</p>
                  </div>
                }
                datetime={
                  <Text type="secondary">{formatDate(review.createdAt)}</Text>
                }
              />
            </li>
          )}
        />
      ) : (
        <div className="no-reviews">
          <Text type="secondary">暂无评论</Text>
        </div>
      )}
    </div>
  );
};

export default ProductReviews;