import React, { useState } from 'react';
import { Card, Form, Input, Button, Avatar, Upload, DatePicker, Select, Typography, Space, message } from 'antd';
import { UserOutlined, CameraOutlined, SaveOutlined } from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../store';
import { updateProfile } from '../store/slices/authSlice';
import dayjs from 'dayjs';

const { Title } = Typography;
const { Option } = Select;

const Profile: React.FC = () => {
  const dispatch = useDispatch();
  const { user, loading } = useSelector((state: RootState) => state.auth);
  const [form] = Form.useForm();
  const [avatarUrl, setAvatarUrl] = useState(user?.avatar || '');

  const handleSubmit = async (values: any) => {
    try {
      const updateData = {
        ...values,
        birthday: values.birthday ? values.birthday.format('YYYY-MM-DD') : undefined,
        avatar: avatarUrl,
      };
      
      await dispatch(updateProfile(updateData) as any).unwrap();
    } catch (error) {
      // 错误已在slice中处理
    }
  };

  const handleAvatarChange = (info: any) => {
    if (info.file.status === 'uploading') {
      return;
    }
    if (info.file.status === 'done') {
      // 这里应该是上传成功后的URL
      setAvatarUrl(info.file.response?.url || '');
      message.success('头像上传成功');
    } else if (info.file.status === 'error') {
      message.error('头像上传失败');
    }
  };

  const beforeUpload = (file: File) => {
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
    if (!isJpgOrPng) {
      message.error('只能上传 JPG/PNG 格式的图片!');
      return false;
    }
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error('图片大小不能超过 2MB!');
      return false;
    }
    return true;
  };

  // 模拟上传
  const customRequest = (options: any) => {
    const { onSuccess, onError, file } = options;
    
    // 模拟上传过程
    setTimeout(() => {
      if (Math.random() > 0.1) { // 90% 成功率
        const reader = new FileReader();
        reader.onload = () => {
          setAvatarUrl(reader.result as string);
          onSuccess({ url: reader.result });
        };
        reader.readAsDataURL(file);
      } else {
        onError(new Error('上传失败'));
      }
    }, 1000);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <Title level={2} className="mb-6">个人中心</Title>

        <div className="max-w-2xl mx-auto">
          <Card>
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSubmit}
              initialValues={{
                username: user?.username,
                email: user?.email,
                phone: user?.phone,
                gender: user?.gender,
                birthday: user?.birthday ? dayjs(user.birthday) : undefined,
              }}
            >
              {/* 头像上传 */}
              <Form.Item label="头像" className="text-center">
                <div className="flex flex-col items-center">
                  <Avatar
                    size={100}
                    src={avatarUrl}
                    icon={<UserOutlined />}
                    className="mb-4"
                  />
                  <Upload
                    name="avatar"
                    showUploadList={false}
                    beforeUpload={beforeUpload}
                    customRequest={customRequest}
                    onChange={handleAvatarChange}
                  >
                    <Button icon={<CameraOutlined />}>
                      更换头像
                    </Button>
                  </Upload>
                </div>
              </Form.Item>

              {/* 基本信息 */}
              <Form.Item
                name="username"
                label="用户名"
                rules={[
                  { required: true, message: '请输入用户名' },
                  { min: 3, message: '用户名长度至少3位' },
                  { max: 20, message: '用户名长度最多20位' },
                  { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' },
                ]}
              >
                <Input placeholder="请输入用户名" />
              </Form.Item>

              <Form.Item
                name="email"
                label="邮箱"
                rules={[
                  { required: true, message: '请输入邮箱' },
                  { type: 'email', message: '请输入有效的邮箱地址' },
                ]}
              >
                <Input placeholder="请输入邮箱" disabled />
              </Form.Item>

              <Form.Item
                name="phone"
                label="手机号"
                rules={[
                  { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码' },
                ]}
              >
                <Input placeholder="请输入手机号" />
              </Form.Item>

              <Form.Item
                name="gender"
                label="性别"
              >
                <Select placeholder="请选择性别">
                  <Option value="male">男</Option>
                  <Option value="female">女</Option>
                  <Option value="other">保密</Option>
                </Select>
              </Form.Item>

              <Form.Item
                name="birthday"
                label="生日"
              >
                <DatePicker
                  placeholder="请选择生日"
                  className="w-full"
                  disabledDate={(current) => current && current > dayjs().endOf('day')}
                />
              </Form.Item>

              {/* 提交按钮 */}
              <Form.Item>
                <Space className="w-full justify-center">
                  <Button
                    type="primary"
                    htmlType="submit"
                    icon={<SaveOutlined />}
                    loading={loading}
                    size="large"
                  >
                    保存修改
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Card>

          {/* 账户安全 */}
          <Card className="mt-6" title="账户安全">
            <Space direction="vertical" size="middle" className="w-full">
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">登录密码</div>
                  <div className="text-sm text-gray-500">定期更换密码可以提高账户安全性</div>
                </div>
                <Button type="link">修改密码</Button>
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">手机绑定</div>
                  <div className="text-sm text-gray-500">
                    {user?.phone ? `已绑定手机号: ${user.phone}` : '未绑定手机号'}
                  </div>
                </div>
                <Button type="link">
                  {user?.phone ? '更换手机' : '绑定手机'}
                </Button>
              </div>
            </Space>
          </Card>

          {/* 账户统计 */}
          <Card className="mt-6" title="账户统计">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-blue-600">0</div>
                <div className="text-sm text-gray-500">待付款订单</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-orange-600">0</div>
                <div className="text-sm text-gray-500">待发货订单</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-600">0</div>
                <div className="text-sm text-gray-500">已完成订单</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-purple-600">0</div>
                <div className="text-sm text-gray-500">收藏商品</div>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Profile;
