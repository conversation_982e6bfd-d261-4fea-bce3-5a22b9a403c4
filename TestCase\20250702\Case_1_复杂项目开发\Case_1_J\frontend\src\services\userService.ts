import { request } from './api';
import { Address } from '../store/slices/authSlice';

interface AddressListResponse {
  addresses: Address[];
}

class UserService {
  // 获取用户地址列表
  async getAddresses(): Promise<AddressListResponse> {
    return request.get('/users/addresses');
  }

  // 添加地址
  async addAddress(addressData: Omit<Address, '_id'>): Promise<AddressListResponse> {
    return request.post('/users/addresses', addressData);
  }

  // 更新地址
  async updateAddress(addressId: string, addressData: Omit<Address, '_id'>): Promise<AddressListResponse> {
    return request.put(`/users/addresses/${addressId}`, addressData);
  }

  // 删除地址
  async deleteAddress(addressId: string): Promise<AddressListResponse> {
    return request.delete(`/users/addresses/${addressId}`);
  }
}

export default new UserService();
