import { request } from './api';

interface PaymentData {
  method: string;
  orderNo: string;
  amount: number;
  subject: string;
  body: string;
  timestamp: number;
  payUrl?: string;
  qrCode?: string;
  prepayId?: string;
}

interface PaymentStatusResponse {
  orderNo: string;
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  paymentMethod: 'alipay' | 'wechat' | 'bank_card';
  finalAmount: number;
  paidAt?: string;
}

interface RefundResponse {
  message: string;
  refundId?: string;
  error?: string;
}

class PaymentService {
  // 创建支付订单
  async createPayment(orderNo: string, paymentMethod: 'alipay' | 'wechat' | 'bank_card'): Promise<{ message: string; paymentData: PaymentData }> {
    return request.post('/payments/create', {
      orderNo,
      paymentMethod
    });
  }

  // 查询支付状态
  async getPaymentStatus(orderNo: string): Promise<PaymentStatusResponse> {
    return request.get(`/payments/status/${orderNo}`);
  }

  // 申请退款
  async requestRefund(orderNo: string, reason: string): Promise<RefundResponse> {
    return request.post('/payments/refund', {
      orderNo,
      reason
    });
  }

  // 轮询支付状态
  async pollPaymentStatus(orderNo: string, maxAttempts: number = 30, interval: number = 2000): Promise<PaymentStatusResponse> {
    return new Promise((resolve, reject) => {
      let attempts = 0;
      
      const poll = async () => {
        try {
          attempts++;
          const status = await this.getPaymentStatus(orderNo);
          
          if (status.paymentStatus === 'paid') {
            resolve(status);
          } else if (status.paymentStatus === 'failed') {
            reject(new Error('支付失败'));
          } else if (attempts >= maxAttempts) {
            reject(new Error('支付超时'));
          } else {
            setTimeout(poll, interval);
          }
        } catch (error) {
          if (attempts >= maxAttempts) {
            reject(error);
          } else {
            setTimeout(poll, interval);
          }
        }
      };
      
      poll();
    });
  }

  // 处理支付宝支付
  async handleAlipayPayment(paymentData: PaymentData): Promise<void> {
    if (paymentData.payUrl) {
      // 跳转到支付宝支付页面
      window.location.href = paymentData.payUrl;
    } else {
      throw new Error('支付链接无效');
    }
  }

  // 处理微信支付
  async handleWechatPayment(paymentData: PaymentData): Promise<string> {
    if (paymentData.qrCode) {
      // 返回二维码链接，由组件显示二维码
      return paymentData.qrCode;
    } else {
      throw new Error('二维码无效');
    }
  }

  // 生成支付二维码数据URL
  generateQRCodeDataURL(text: string): Promise<string> {
    return new Promise((resolve) => {
      // 这里应该使用二维码生成库，如qrcode
      // 为了简化，直接返回文本
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      if (ctx) {
        canvas.width = 200;
        canvas.height = 200;
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, 200, 200);
        ctx.fillStyle = '#000000';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('微信支付二维码', 100, 100);
        ctx.fillText('(模拟)', 100, 120);
        
        resolve(canvas.toDataURL());
      } else {
        resolve('');
      }
    });
  }
}

export default new PaymentService();
