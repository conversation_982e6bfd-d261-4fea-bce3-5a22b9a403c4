import React, { useContext } from 'react';
import { Link } from 'react-router-dom';
import { Card, Button, message, Rate, Tag } from 'antd';
import { ShoppingCartOutlined, EyeOutlined } from '@ant-design/icons';
import { CartContext } from '../../context/CartContext';

const { Meta } = Card;

const ProductCard = ({ product }) => {
  const { addToCart } = useContext(CartContext);

  // 处理添加到购物车
  const handleAddToCart = async (e) => {
    e.preventDefault();
    const success = await addToCart(product._id, 1);
    if (success) {
      message.success('已添加到购物车');
    }
  };

  // 计算折扣百分比
  const getDiscountPercent = () => {
    if (product.originalPrice && product.originalPrice > product.price) {
      return Math.round((1 - product.price / product.originalPrice) * 100);
    }
    return 0;
  };

  const discountPercent = getDiscountPercent();

  return (
    <Card
      hoverable
      cover={
        <div style={{ position: 'relative' }}>
          <img 
            alt={product.name} 
            src={product.images && product.images.length > 0 ? product.images[0] : 'https://via.placeholder.com/300'} 
          />
          {product.isOnSale && discountPercent > 0 && (
            <div 
              style={{
                position: 'absolute',
                top: '10px',
                right: '10px',
                background: '#ff4d4f',
                color: 'white',
                padding: '5px 10px',
                borderRadius: '15px',
                fontWeight: 'bold',
                fontSize: '12px'
              }}
            >
              -{discountPercent}%
            </div>
          )}
          {product.stock <= 0 && (
            <div 
              style={{
                position: 'absolute',
                top: '0',
                left: '0',
                width: '100%',
                height: '100%',
                background: 'rgba(0,0,0,0.5)',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center'
              }}
            >
              <span style={{ color: 'white', fontSize: '18px', fontWeight: 'bold' }}>
                已售罄
              </span>
            </div>
          )}
        </div>
      }
      actions={[
        <Link to={`/products/${product._id}`}>
          <Button type="text" icon={<EyeOutlined />}>查看详情</Button>
        </Link>,
        <Button 
          type="primary" 
          icon={<ShoppingCartOutlined />} 
          onClick={handleAddToCart}
          disabled={product.stock <= 0}
        >
          加入购物车
        </Button>
      ]}
    >
      <Meta
        title={
          <Link to={`/products/${product._id}`}>
            {product.name}
          </Link>
        }
        description={
          <div>
            <div style={{ marginBottom: '8px' }}>
              {product.isOnSale && product.originalPrice ? (
                <div>
                  <span style={{ color: '#ff4d4f', fontWeight: 'bold', fontSize: '16px' }}>
                    ¥{product.price.toFixed(2)}
                  </span>
                  <span style={{ marginLeft: '5px', textDecoration: 'line-through', color: '#aaa' }}>
                    ¥{product.originalPrice.toFixed(2)}
                  </span>
                </div>
              ) : (
                <span style={{ color: '#ff4d4f', fontWeight: 'bold', fontSize: '16px' }}>
                  ¥{product.price.toFixed(2)}
                </span>
              )}
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Rate disabled defaultValue={product.rating} style={{ fontSize: '12px' }} />
              <span style={{ fontSize: '12px', color: '#999' }}>
                已售 {product.sold || 0}
              </span>
            </div>
            {product.isFeatured && (
              <Tag color="blue" style={{ marginTop: '5px' }}>热门推荐</Tag>
            )}
          </div>
        }
      />
    </Card>
  );
};

export default ProductCard; 