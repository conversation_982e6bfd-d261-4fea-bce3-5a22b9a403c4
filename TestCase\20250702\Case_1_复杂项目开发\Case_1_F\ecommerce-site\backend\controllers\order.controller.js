const Order = require('../models/order.model');
const Cart = require('../models/cart.model');
const Product = require('../models/product.model');

// @desc    创建新订单
// @route   POST /api/orders
// @access  私有
exports.createOrder = async (req, res) => {
  try {
    const { shippingAddress, paymentMethod } = req.body;

    // 验证必要的字段
    if (!shippingAddress || !paymentMethod) {
      return res.status(400).json({
        success: false,
        message: '请提供配送地址和支付方式'
      });
    }

    // 获取用户购物车
    const cart = await Cart.findOne({ user: req.user.id }).populate('items.product');
    if (!cart || cart.items.length === 0) {
      return res.status(400).json({
        success: false,
        message: '购物车为空，无法创建订单'
      });
    }

    // 验证库存是否充足
    for (const item of cart.items) {
      if (item.quantity > item.product.stock) {
        return res.status(400).json({
          success: false,
          message: `商品 ${item.product.name} 库存不足，仅剩 ${item.product.stock} 件`
        });
      }
    }

    // 创建订单项
    const orderItems = cart.items.map(item => {
      return {
        product: item.product._id,
        name: item.product.name,
        quantity: item.quantity,
        price: item.price,
        image: item.product.images[0] || ''
      };
    });

    // 计算价格
    const itemsPrice = cart.totalPrice;
    
    // 配送费计算 (示例：订单金额满99元免运费，否则10元运费)
    const shippingPrice = itemsPrice >= 99 ? 0 : 10;
    
    // 税费计算 (示例：商品总价的5%)
    const taxPrice = itemsPrice * 0.05;
    
    // 创建新订单
    const order = new Order({
      user: req.user.id,
      orderItems,
      shippingAddress,
      paymentMethod,
      itemsPrice,
      shippingPrice,
      taxPrice,
      totalPrice: itemsPrice + shippingPrice + taxPrice
    });

    const createdOrder = await order.save();

    // 更新产品库存
    for (const item of cart.items) {
      await Product.findByIdAndUpdate(
        item.product._id, 
        { 
          $inc: { 
            stock: -item.quantity,
            sold: +item.quantity
          } 
        }
      );
    }

    // 清空购物车
    cart.items = [];
    cart.totalPrice = 0;
    await cart.save();

    res.status(201).json({
      success: true,
      message: '订单创建成功',
      data: createdOrder
    });
  } catch (err) {
    console.error('创建订单失败:', err);
    res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
  }
};

// @desc    获取当前用户的所有订单
// @route   GET /api/orders
// @access  私有
exports.getMyOrders = async (req, res) => {
  try {
    const orders = await Order.find({ user: req.user.id }).sort({ createdAt: -1 });
    
    res.status(200).json({
      success: true,
      count: orders.length,
      data: orders
    });
  } catch (err) {
    console.error('获取订单失败:', err);
    res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
  }
};

// @desc    获取单个订单详情
// @route   GET /api/orders/:id
// @access  私有
exports.getOrderById = async (req, res) => {
  try {
    const order = await Order.findById(req.params.id);

    // 检查订单是否存在
    if (!order) {
      return res.status(404).json({
        success: false,
        message: '未找到该订单'
      });
    }

    // 验证用户只能查看自己的订单，管理员可以查看所有订单
    if (order.user.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '无权限查看此订单'
      });
    }

    res.status(200).json({
      success: true,
      data: order
    });
  } catch (err) {
    console.error('获取订单详情失败:', err);
    res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
  }
};

// @desc    取消订单
// @route   PUT /api/orders/:id/cancel
// @access  私有
exports.cancelOrder = async (req, res) => {
  try {
    const order = await Order.findById(req.params.id);

    // 检查订单是否存在
    if (!order) {
      return res.status(404).json({
        success: false,
        message: '未找到该订单'
      });
    }

    // 验证用户只能取消自己的订单，管理员可以取消所有订单
    if (order.user.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '无权限取消此订单'
      });
    }

    // 检查订单是否可以取消
    if (order.status !== '待付款' && req.user.role !== 'admin') {
      return res.status(400).json({
        success: false,
        message: '只能取消待付款的订单'
      });
    }

    // 更新订单状态
    order.status = '已取消';
    await order.save();

    // 如果订单已经扣减了库存，需要恢复库存
    if (order.isPaid || order.status === '待发货') {
      for (const item of order.orderItems) {
        await Product.findByIdAndUpdate(
          item.product,
          {
            $inc: {
              stock: +item.quantity,
              sold: -item.quantity
            }
          }
        );
      }
    }

    res.status(200).json({
      success: true,
      message: '订单已取消',
      data: order
    });
  } catch (err) {
    console.error('取消订单失败:', err);
    res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
  }
};

// @desc    更新订单为已支付
// @route   PUT /api/orders/:id/pay
// @access  私有
exports.updateOrderToPaid = async (req, res) => {
  try {
    const { paymentResult } = req.body;
    const order = await Order.findById(req.params.id);

    if (!order) {
      return res.status(404).json({
        success: false,
        message: '未找到该订单'
      });
    }

    // 验证用户只能支付自己的订单
    if (order.user.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: '无权限支付此订单'
      });
    }

    // 检查订单是否已支付
    if (order.isPaid) {
      return res.status(400).json({
        success: false,
        message: '订单已支付'
      });
    }

    // 更新订单支付状态
    order.isPaid = true;
    order.paidAt = Date.now();
    order.status = '待发货';
    order.paymentResult = paymentResult;

    const updatedOrder = await order.save();

    res.status(200).json({
      success: true,
      message: '订单支付成功',
      data: updatedOrder
    });
  } catch (err) {
    console.error('更新订单支付状态失败:', err);
    res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
  }
};

// 以下是管理员专用接口

// @desc    获取所有订单
// @route   GET /api/orders/admin
// @access  私有 (仅限管理员)
exports.getAllOrders = async (req, res) => {
  try {
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;
    
    // 构建查询条件
    const query = {};
    
    // 按状态过滤
    if (req.query.status) {
      query.status = req.query.status;
    }
    
    // 按支付状态过滤
    if (req.query.isPaid) {
      query.isPaid = req.query.isPaid === 'true';
    }
    
    // 按配送状态过滤
    if (req.query.isDelivered) {
      query.isDelivered = req.query.isDelivered === 'true';
    }
    
    const total = await Order.countDocuments(query);
    const orders = await Order.find(query)
      .populate('user', 'id username email')
      .sort({ createdAt: -1 })
      .skip(startIndex)
      .limit(limit);
    
    // 构建分页信息
    const pagination = {};
    
    if (endIndex < total) {
      pagination.next = {
        page: page + 1,
        limit
      };
    }
    
    if (startIndex > 0) {
      pagination.prev = {
        page: page - 1,
        limit
      };
    }
    
    res.status(200).json({
      success: true,
      count: orders.length,
      pagination,
      total,
      data: orders
    });
  } catch (err) {
    console.error('获取所有订单失败:', err);
    res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
  }
};

// @desc    更新订单状态
// @route   PUT /api/orders/:id/status
// @access  私有 (仅限管理员)
exports.updateOrderStatus = async (req, res) => {
  try {
    const { status } = req.body;
    const order = await Order.findById(req.params.id);

    if (!order) {
      return res.status(404).json({
        success: false,
        message: '未找到该订单'
      });
    }

    // 验证状态值有效
    const validStatus = ['待付款', '待发货', '已发货', '已完成', '已取消'];
    if (!validStatus.includes(status)) {
      return res.status(400).json({
        success: false,
        message: '无效的订单状态'
      });
    }

    // 更新订单状态
    order.status = status;
    
    // 处理特殊状态
    if (status === '已发货') {
      order.isDelivered = true;
      order.deliveredAt = Date.now();
    } else if (status === '已完成') {
      if (!order.isDelivered) {
        order.isDelivered = true;
        order.deliveredAt = Date.now();
      }
    }

    const updatedOrder = await order.save();

    res.status(200).json({
      success: true,
      message: '订单状态已更新',
      data: updatedOrder
    });
  } catch (err) {
    console.error('更新订单状态失败:', err);
    res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
  }
}; 