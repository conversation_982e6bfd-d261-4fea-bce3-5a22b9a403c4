import React from 'react';
import { Layout, Row, Col, Space, Typography } from 'antd';
import {
  PhoneOutlined,
  MailOutlined,
  EnvironmentOutlined,
  WechatOutlined,
  QqOutlined,
  WeiboOutlined,
} from '@ant-design/icons';

const { Footer: AntFooter } = Layout;
const { Title, Text, Link } = Typography;

const Footer: React.FC = () => {
  return (
    <AntFooter className="bg-gray-800 text-white mt-auto">
      <div className="container mx-auto py-8">
        <Row gutter={[32, 32]}>
          {/* 公司信息 */}
          <Col xs={24} sm={12} md={6}>
            <Title level={4} className="text-white mb-4">
              关于我们
            </Title>
            <Space direction="vertical" size="small">
              <Text className="text-gray-300">
                专业的电商平台，为您提供优质的商品和服务
              </Text>
              <div className="flex items-center text-gray-300">
                <EnvironmentOutlined className="mr-2" />
                <Text className="text-gray-300">北京市朝阳区xxx街道xxx号</Text>
              </div>
              <div className="flex items-center text-gray-300">
                <PhoneOutlined className="mr-2" />
                <Text className="text-gray-300">************</Text>
              </div>
              <div className="flex items-center text-gray-300">
                <MailOutlined className="mr-2" />
                <Text className="text-gray-300"><EMAIL></Text>
              </div>
            </Space>
          </Col>

          {/* 客户服务 */}
          <Col xs={24} sm={12} md={6}>
            <Title level={4} className="text-white mb-4">
              客户服务
            </Title>
            <Space direction="vertical" size="small">
              <Link className="text-gray-300 hover:text-white">帮助中心</Link>
              <Link className="text-gray-300 hover:text-white">售后服务</Link>
              <Link className="text-gray-300 hover:text-white">退换货政策</Link>
              <Link className="text-gray-300 hover:text-white">配送说明</Link>
              <Link className="text-gray-300 hover:text-white">支付方式</Link>
            </Space>
          </Col>

          {/* 购物指南 */}
          <Col xs={24} sm={12} md={6}>
            <Title level={4} className="text-white mb-4">
              购物指南
            </Title>
            <Space direction="vertical" size="small">
              <Link className="text-gray-300 hover:text-white">新手指南</Link>
              <Link className="text-gray-300 hover:text-white">会员介绍</Link>
              <Link className="text-gray-300 hover:text-white">积分规则</Link>
              <Link className="text-gray-300 hover:text-white">优惠券说明</Link>
              <Link className="text-gray-300 hover:text-white">团购说明</Link>
            </Space>
          </Col>

          {/* 关注我们 */}
          <Col xs={24} sm={12} md={6}>
            <Title level={4} className="text-white mb-4">
              关注我们
            </Title>
            <Space direction="vertical" size="middle">
              <Text className="text-gray-300">
                关注我们的社交媒体，获取最新优惠信息
              </Text>
              <Space size="large">
                <WechatOutlined className="text-2xl text-gray-300 hover:text-green-500 cursor-pointer" />
                <QqOutlined className="text-2xl text-gray-300 hover:text-blue-500 cursor-pointer" />
                <WeiboOutlined className="text-2xl text-gray-300 hover:text-red-500 cursor-pointer" />
              </Space>
              <div>
                <Text className="text-gray-300 text-sm">
                  客服时间：周一至周日 9:00-22:00
                </Text>
              </div>
            </Space>
          </Col>
        </Row>

        {/* 底部版权信息 */}
        <div className="border-t border-gray-700 mt-8 pt-6 text-center">
          <Space direction="vertical" size="small">
            <Space split={<span className="text-gray-500">|</span>}>
              <Link className="text-gray-400 hover:text-white">隐私政策</Link>
              <Link className="text-gray-400 hover:text-white">服务条款</Link>
              <Link className="text-gray-400 hover:text-white">网站地图</Link>
              <Link className="text-gray-400 hover:text-white">友情链接</Link>
            </Space>
            <Text className="text-gray-400 text-sm">
              © 2024 电商网站. All rights reserved. 京ICP备xxxxxxxx号
            </Text>
          </Space>
        </div>
      </div>
    </AntFooter>
  );
};

export default Footer;
