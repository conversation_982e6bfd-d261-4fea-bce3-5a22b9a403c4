# 电商网站系统

这是一个完整的电商网站系统，包含前端和后端实现。

## 项目结构

```
ecommerce-site/
├── frontend/                # React 前端
│   ├── public/              # 静态资源
│   └── src/                 # 源代码
│       ├── components/      # 可复用组件
│       ├── pages/           # 页面组件
│       ├── services/        # API服务
│       ├── utils/           # 工具函数
│       ├── assets/          # 静态资源
│       └── context/         # React上下文
└── backend/                 # Node.js 后端
    ├── controllers/         # 控制器
    ├── models/              # 数据模型
    ├── routes/              # 路由定义
    ├── middleware/          # 中间件
    ├── config/              # 配置文件
    └── utils/               # 工具函数
```

## 功能特性

- **用户管理**：注册、登录、个人信息管理
- **商品管理**：商品分类、商品列表、商品详情
- **购物车**：添加商品、修改数量、删除商品
- **订单系统**：创建订单、订单状态跟踪、订单历史
- **支付功能**：模拟支付流程、支付状态管理

## 技术栈

- **前端**：React、React Router、Axios、Ant Design
- **后端**：Node.js、Express
- **数据库**：MongoDB
- **认证**：JWT (JSON Web Tokens) 