// 数据库种子数据脚本
const mongoose = require('mongoose');
require('dotenv').config();

// 连接数据库
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/ecommerce');

// 导入模型
const Category = require('../backend/src/models/Category');
const Product = require('../backend/src/models/Product');
const User = require('../backend/src/models/User');

async function seedData() {
  try {
    console.log('🌱 开始插入种子数据...');

    // 清空现有数据
    await Product.deleteMany({});
    console.log('✅ 清空商品数据');

    // 获取分类
    const categories = await Category.find();
    if (categories.length === 0) {
      console.log('❌ 请先运行 MongoDB 初始化脚本');
      return;
    }

    // 获取管理员用户
    const admin = await User.findOne({ role: 'admin' });
    if (!admin) {
      console.log('❌ 未找到管理员用户');
      return;
    }

    // 示例商品数据
    const products = [
      {
        name: 'iPhone 15 Pro',
        description: '苹果最新旗舰手机，搭载A17 Pro芯片，支持5G网络，拍照效果出色。',
        category: categories.find(c => c.slug === 'electronics')?._id,
        brand: 'Apple',
        images: [
          'https://via.placeholder.com/400x400/1890ff/ffffff?text=iPhone+15+Pro',
          'https://via.placeholder.com/400x400/52c41a/ffffff?text=iPhone+15+Pro+2'
        ],
        basePrice: 7999,
        originalPrice: 8999,
        skus: [
          {
            sku: 'IP15P-128-BLU',
            specs: [
              { name: '容量', value: '128GB' },
              { name: '颜色', value: '深空蓝色' }
            ],
            price: 7999,
            stock: 50
          },
          {
            sku: 'IP15P-256-BLU',
            specs: [
              { name: '容量', value: '256GB' },
              { name: '颜色', value: '深空蓝色' }
            ],
            price: 8999,
            stock: 30
          },
          {
            sku: 'IP15P-128-WHT',
            specs: [
              { name: '容量', value: '128GB' },
              { name: '颜色', value: '白色' }
            ],
            price: 7999,
            stock: 40
          }
        ],
        tags: ['手机', '苹果', '5G', '拍照'],
        sales: 1250,
        rating: 4.8,
        reviewCount: 856,
        createdBy: admin._id
      },
      {
        name: '小米13 Ultra',
        description: '小米旗舰手机，徕卡影像系统，骁龙8 Gen2处理器，性能强劲。',
        category: categories.find(c => c.slug === 'electronics')?._id,
        brand: '小米',
        images: [
          'https://via.placeholder.com/400x400/fa8c16/ffffff?text=小米13+Ultra'
        ],
        basePrice: 5999,
        originalPrice: 6499,
        skus: [
          {
            sku: 'MI13U-256-BLK',
            specs: [
              { name: '容量', value: '256GB' },
              { name: '颜色', value: '黑色' }
            ],
            price: 5999,
            stock: 80
          },
          {
            sku: 'MI13U-512-BLK',
            specs: [
              { name: '容量', value: '512GB' },
              { name: '颜色', value: '黑色' }
            ],
            price: 6999,
            stock: 25
          }
        ],
        tags: ['手机', '小米', '徕卡', '拍照'],
        sales: 890,
        rating: 4.6,
        reviewCount: 432,
        createdBy: admin._id
      },
      {
        name: '优衣库基础款T恤',
        description: '100%纯棉材质，舒适透气，多色可选，日常百搭必备。',
        category: categories.find(c => c.slug === 'clothing')?._id,
        brand: '优衣库',
        images: [
          'https://via.placeholder.com/400x400/52c41a/ffffff?text=优衣库T恤'
        ],
        basePrice: 59,
        originalPrice: 79,
        skus: [
          {
            sku: 'UQ-TEE-M-WHT',
            specs: [
              { name: '尺码', value: 'M' },
              { name: '颜色', value: '白色' }
            ],
            price: 59,
            stock: 200
          },
          {
            sku: 'UQ-TEE-L-WHT',
            specs: [
              { name: '尺码', value: 'L' },
              { name: '颜色', value: '白色' }
            ],
            price: 59,
            stock: 150
          },
          {
            sku: 'UQ-TEE-M-BLK',
            specs: [
              { name: '尺码', value: 'M' },
              { name: '颜色', value: '黑色' }
            ],
            price: 59,
            stock: 180
          }
        ],
        tags: ['T恤', '纯棉', '基础款', '百搭'],
        sales: 2340,
        rating: 4.5,
        reviewCount: 1205,
        createdBy: admin._id
      },
      {
        name: '宜家简约书桌',
        description: '北欧简约风格书桌，环保材质，结构稳固，适合家庭办公学习。',
        category: categories.find(c => c.slug === 'home')?._id,
        brand: '宜家',
        images: [
          'https://via.placeholder.com/400x400/722ed1/ffffff?text=宜家书桌'
        ],
        basePrice: 299,
        originalPrice: 399,
        skus: [
          {
            sku: 'IKEA-DESK-120-WHT',
            specs: [
              { name: '尺寸', value: '120cm' },
              { name: '颜色', value: '白色' }
            ],
            price: 299,
            stock: 45
          },
          {
            sku: 'IKEA-DESK-140-WHT',
            specs: [
              { name: '尺寸', value: '140cm' },
              { name: '颜色', value: '白色' }
            ],
            price: 399,
            stock: 30
          }
        ],
        tags: ['书桌', '家具', '简约', '环保'],
        sales: 567,
        rating: 4.3,
        reviewCount: 234,
        weight: 15000,
        dimensions: {
          length: 120,
          width: 60,
          height: 75
        },
        createdBy: admin._id
      },
      {
        name: '《JavaScript高级程序设计》',
        description: '前端开发必读经典，深入讲解JavaScript核心概念和高级特性。',
        category: categories.find(c => c.slug === 'books')?._id,
        brand: '人民邮电出版社',
        images: [
          'https://via.placeholder.com/400x400/13c2c2/ffffff?text=JS高程'
        ],
        basePrice: 89,
        originalPrice: 109,
        skus: [
          {
            sku: 'BOOK-JS-4TH',
            specs: [
              { name: '版本', value: '第4版' },
              { name: '语言', value: '中文' }
            ],
            price: 89,
            stock: 120
          }
        ],
        tags: ['编程', 'JavaScript', '前端', '技术'],
        sales: 1890,
        rating: 4.9,
        reviewCount: 967,
        weight: 800,
        createdBy: admin._id
      }
    ];

    // 插入商品数据
    await Product.insertMany(products);
    console.log(`✅ 插入 ${products.length} 个商品`);

    console.log('🎉 种子数据插入完成！');
    
  } catch (error) {
    console.error('❌ 插入种子数据失败:', error);
  } finally {
    mongoose.connection.close();
  }
}

// 运行脚本
seedData();
