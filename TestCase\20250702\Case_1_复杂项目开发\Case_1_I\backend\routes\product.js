const express = require('express');
const router = express.Router();
const productController = require('../controllers/productController');
const { protect, restrictTo } = require('../middleware/auth');
const upload = require('../middleware/upload');

// 公共路由
router.get('/', productController.getAllProducts);
router.get('/:id', productController.getProductById);
router.get('/:id/reviews', productController.getProductReviews);

// 需要登录的路由
router.post('/:id/reviews', protect, productController.addProductReview);

// 管理员路由
router.post('/', protect, restrictTo('admin'), productController.createProduct);
router.put('/:id', protect, restrictTo('admin'), productController.updateProduct);
router.delete('/:id', protect, restrictTo('admin'), productController.deleteProduct);
router.post(
  '/:id/images',
  protect,
  restrictTo('admin'),
  upload.array('product', 5),
  productController.uploadProductImages
);
router.delete(
  '/:id/images/:imageIndex',
  protect,
  restrictTo('admin'),
  productController.deleteProductImage
);

module.exports = router;