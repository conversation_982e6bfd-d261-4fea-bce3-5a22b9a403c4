{"name": "ecommerce-backend", "version": "1.0.0", "description": "Backend for e-commerce website", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-async-handler": "^1.2.0", "jsonwebtoken": "^9.0.0", "mongoose": "^7.0.3", "stripe": "^12.0.0"}, "devDependencies": {"nodemon": "^2.0.22"}}