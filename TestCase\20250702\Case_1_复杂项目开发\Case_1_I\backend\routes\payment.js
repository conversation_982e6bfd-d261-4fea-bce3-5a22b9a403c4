const express = require('express');
const router = express.Router();
const paymentController = require('../controllers/paymentController');
const { protect, restrictTo } = require('../middleware/auth');

// 公共路由
router.get('/methods', paymentController.getPaymentMethods);

// 用户路由
router.post('/create-payment-intent', protect, paymentController.createPaymentIntent);
router.post('/confirm', protect, paymentController.confirmPayment);
router.get('/:paymentId', protect, paymentController.getPaymentStatus);

// 管理员路由
router.post('/refund', protect, restrictTo('admin'), paymentController.refundPayment);

module.exports = router;