import React, { useState } from 'react';
import { Card, Button, InputNumber, Typography, Image, Space, Popconfirm } from 'antd';
import { DeleteOutlined } from '@ant-design/icons';
import { Link } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { updateCartItem, removeFromCart } from '../../redux/slices/cartSlice';
import Price from '../common/Price';

const { Text } = Typography;

const CartItem = ({ item }) => {
  const dispatch = useDispatch();
  const [quantity, setQuantity] = useState(item.quantity);
  const [loading, setLoading] = useState(false);

  // 处理数量变化
  const handleQuantityChange = (value) => {
    setQuantity(value);
    setLoading(true);
    dispatch(updateCartItem({ productId: item.product._id, quantity: value }))
      .finally(() => setLoading(false));
  };

  // 处理删除商品
  const handleRemoveItem = () => {
    dispatch(removeFromCart(item.product._id));
  };

  return (
    <Card className="cart-item" bordered={false} style={{ marginBottom: '16px' }}>
      <div style={{ display: 'flex', alignItems: 'center' }}>
        {/* 商品图片 */}
        <div className="cart-item-image" style={{ marginRight: '16px' }}>
          <Link to={`/product/${item.product._id}`}>
            <Image
              src={item.product.images && item.product.images.length > 0 ? item.product.images[0] : '/placeholder.png'}
              alt={item.product.name}
              width={80}
              height={80}
              style={{ objectFit: 'cover' }}
              preview={false}
            />
          </Link>
        </div>

        {/* 商品信息 */}
        <div className="cart-item-info" style={{ flex: 1 }}>
          <Link to={`/product/${item.product._id}`}>
            <Text strong style={{ fontSize: '16px', marginBottom: '8px', display: 'block' }}>
              {item.product.name}
            </Text>
          </Link>

          {/* 价格信息 */}
          <div className="cart-item-price" style={{ marginBottom: '8px' }}>
            <Price
              price={item.product.price}
              originalPrice={item.product.originalPrice}
            />
          </div>

          {/* 库存信息 */}
          {item.product.countInStock > 0 ? (
            <Text type="secondary" style={{ fontSize: '12px' }}>
              库存: {item.product.countInStock}
            </Text>
          ) : (
            <Text type="danger" style={{ fontSize: '12px' }}>
              缺货
            </Text>
          )}
        </div>

        {/* 数量控制 */}
        <div className="cart-item-quantity" style={{ marginRight: '16px' }}>
          <InputNumber
            min={1}
            max={item.product.countInStock}
            value={quantity}
            onChange={handleQuantityChange}
            disabled={loading || item.product.countInStock === 0}
          />
        </div>

        {/* 小计 */}
        <div className="cart-item-subtotal" style={{ marginRight: '16px', minWidth: '80px', textAlign: 'right' }}>
          <Text strong>
            ¥{(item.product.price * quantity).toFixed(2)}
          </Text>
        </div>

        {/* 删除按钮 */}
        <div className="cart-item-actions">
          <Popconfirm
            title="确定要从购物车中删除此商品吗？"
            onConfirm={handleRemoveItem}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
            />
          </Popconfirm>
        </div>
      </div>
    </Card>
  );
};

export default CartItem;