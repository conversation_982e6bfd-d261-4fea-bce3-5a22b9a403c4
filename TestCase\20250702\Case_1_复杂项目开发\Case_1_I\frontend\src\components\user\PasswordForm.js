import React from 'react';
import { Form, Input, But<PERSON>, Card, Typography } from 'antd';
import { LockOutlined } from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { updatePassword } from '../../redux/slices/authSlice';
import { validatePassword } from '../../utils/validationUtils';

const { Title, Text } = Typography;

const PasswordForm = () => {
  const dispatch = useDispatch();
  const { loading } = useSelector((state) => state.auth);
  const [form] = Form.useForm();

  // 处理表单提交
  const handleSubmit = (values) => {
    dispatch(updatePassword({
      currentPassword: values.currentPassword,
      newPassword: values.newPassword,
    }));
    form.resetFields();
  };

  return (
    <Card className="password-form-card">
      <Title level={4}>修改密码</Title>
      <Text type="secondary" style={{ marginBottom: '16px', display: 'block' }}>
        为了保证账户安全，请定期更换密码
      </Text>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
      >
        <Form.Item
          name="currentPassword"
          label="当前密码"
          rules={[{ required: true, message: '请输入当前密码' }]}
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder="请输入当前密码"
          />
        </Form.Item>

        <Form.Item
          name="newPassword"
          label="新密码"
          rules={[
            { required: true, message: '请输入新密码' },
            {
              validator: (_, value) => {
                if (!value || validatePassword(value)) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error('密码必须包含至少8个字符，包括字母、数字和特殊字符'));
              },
            },
          ]}
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder="请输入新密码"
          />
        </Form.Item>

        <Form.Item
          name="confirmPassword"
          label="确认新密码"
          dependencies={['newPassword']}
          rules={[
            { required: true, message: '请确认新密码' },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('newPassword') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error('两次输入的密码不一致'));
              },
            }),
          ]}
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder="请确认新密码"
          />
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit" loading={loading}>
            更新密码
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default PasswordForm;