/**
 * 统一API响应格式
 */

// 成功响应
exports.successResponse = (res, data, message = '操作成功', statusCode = 200) => {
  return res.status(statusCode).json({
    success: true,
    message,
    data,
  });
};

// 错误响应
exports.errorResponse = (res, message = '操作失败', statusCode = 400) => {
  return res.status(statusCode).json({
    success: false,
    message,
  });
};

// 分页响应
exports.paginatedResponse = (
  res,
  data,
  page,
  limit,
  total,
  message = '获取数据成功'
) => {
  return res.status(200).json({
    success: true,
    message,
    data,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total,
      pages: Math.ceil(total / limit),
    },
  });
};