import { configureStore, combineReducers } from '@reduxjs/toolkit';
import { persistStore, persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import thunk from 'redux-thunk';

// 导入各个slice的reducer
import authReducer from './slices/authSlice';
import productReducer from './slices/productSlice';
import cartReducer from './slices/cartSlice';
import orderReducer from './slices/orderSlice';
import categoryReducer from './slices/categorySlice';
import uiReducer from './slices/uiSlice';

// 配置Redux Persist
const persistConfig = {
  key: 'root',
  storage,
  whitelist: ['auth', 'cart'], // 只持久化这些reducer的状态
};

// 合并所有reducer
const rootReducer = combineReducers({
  auth: authReducer,
  product: productReducer,
  cart: cartReducer,
  order: orderReducer,
  category: categoryReducer,
  ui: uiReducer,
});

// 创建持久化的reducer
const persistedReducer = persistReducer(persistConfig, rootReducer);

// 创建store
export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }).concat(thunk),
  devTools: process.env.NODE_ENV !== 'production',
});

// 创建persistor
export const persistor = persistStore(store);