const express = require('express');
const Category = require('../models/Category');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

const router = express.Router();

// 获取分类列表（树形结构）
router.get('/', async (req, res) => {
  try {
    const { includeInactive = false } = req.query;
    
    const filter = {};
    if (!includeInactive) {
      filter.isActive = true;
    }

    const categories = await Category.find(filter)
      .sort({ level: 1, sort: 1, createdAt: 1 });

    // 构建树形结构
    const categoryTree = buildCategoryTree(categories);

    res.json({
      categories: categoryTree
    });
  } catch (error) {
    console.error('获取分类列表错误:', error);
    res.status(500).json({ message: '获取分类列表失败' });
  }
});

// 获取扁平分类列表
router.get('/flat', async (req, res) => {
  try {
    const { level, parent, includeInactive = false } = req.query;
    
    const filter = {};
    if (!includeInactive) {
      filter.isActive = true;
    }
    if (level) {
      filter.level = parseInt(level);
    }
    if (parent) {
      filter.parent = parent === 'null' ? null : parent;
    }

    const categories = await Category.find(filter)
      .populate('parent', 'name slug')
      .sort({ level: 1, sort: 1, createdAt: 1 });

    res.json({
      categories
    });
  } catch (error) {
    console.error('获取分类列表错误:', error);
    res.status(500).json({ message: '获取分类列表失败' });
  }
});

// 获取分类详情
router.get('/:id', async (req, res) => {
  try {
    const category = await Category.findById(req.params.id)
      .populate('parent', 'name slug');

    if (!category) {
      return res.status(404).json({ message: '分类不存在' });
    }

    res.json({ category });
  } catch (error) {
    console.error('获取分类详情错误:', error);
    res.status(500).json({ message: '获取分类详情失败' });
  }
});

// 创建分类（管理员）
router.post('/', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const {
      name,
      slug,
      description,
      image,
      parent,
      sort,
      seoTitle,
      seoDescription,
      seoKeywords
    } = req.body;

    // 检查slug是否已存在
    if (slug) {
      const existingCategory = await Category.findOne({ slug });
      if (existingCategory) {
        return res.status(400).json({ message: 'URL别名已存在' });
      }
    }

    // 确定分类层级
    let level = 1;
    if (parent) {
      const parentCategory = await Category.findById(parent);
      if (!parentCategory) {
        return res.status(400).json({ message: '父分类不存在' });
      }
      level = parentCategory.level + 1;
      if (level > 3) {
        return res.status(400).json({ message: '分类层级不能超过3级' });
      }
    }

    const category = new Category({
      name,
      slug,
      description,
      image,
      parent: parent || null,
      level,
      sort: sort || 0,
      seoTitle,
      seoDescription,
      seoKeywords: seoKeywords || []
    });

    await category.save();
    await category.populate('parent', 'name slug');

    res.status(201).json({
      message: '分类创建成功',
      category
    });
  } catch (error) {
    console.error('创建分类错误:', error);
    if (error.name === 'ValidationError') {
      return res.status(400).json({ message: '分类数据验证失败' });
    }
    res.status(500).json({ message: '创建分类失败' });
  }
});

// 更新分类（管理员）
router.put('/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const categoryId = req.params.id;
    const updateData = req.body;

    // 检查slug是否已被其他分类使用
    if (updateData.slug) {
      const existingCategory = await Category.findOne({
        slug: updateData.slug,
        _id: { $ne: categoryId }
      });
      if (existingCategory) {
        return res.status(400).json({ message: 'URL别名已存在' });
      }
    }

    // 如果更新父分类，重新计算层级
    if (updateData.parent !== undefined) {
      if (updateData.parent) {
        const parentCategory = await Category.findById(updateData.parent);
        if (!parentCategory) {
          return res.status(400).json({ message: '父分类不存在' });
        }
        updateData.level = parentCategory.level + 1;
        if (updateData.level > 3) {
          return res.status(400).json({ message: '分类层级不能超过3级' });
        }
      } else {
        updateData.level = 1;
      }
    }

    const category = await Category.findByIdAndUpdate(
      categoryId,
      updateData,
      { new: true, runValidators: true }
    ).populate('parent', 'name slug');

    if (!category) {
      return res.status(404).json({ message: '分类不存在' });
    }

    res.json({
      message: '分类更新成功',
      category
    });
  } catch (error) {
    console.error('更新分类错误:', error);
    if (error.name === 'ValidationError') {
      return res.status(400).json({ message: '分类数据验证失败' });
    }
    res.status(500).json({ message: '更新分类失败' });
  }
});

// 删除分类（管理员）
router.delete('/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const categoryId = req.params.id;

    // 检查是否有子分类
    const childCategories = await Category.find({ parent: categoryId });
    if (childCategories.length > 0) {
      return res.status(400).json({ message: '请先删除子分类' });
    }

    // 检查是否有商品使用此分类
    const Product = require('../models/Product');
    const productsCount = await Product.countDocuments({ category: categoryId });
    if (productsCount > 0) {
      return res.status(400).json({ message: '该分类下还有商品，无法删除' });
    }

    const category = await Category.findByIdAndDelete(categoryId);
    if (!category) {
      return res.status(404).json({ message: '分类不存在' });
    }

    res.json({ message: '分类删除成功' });
  } catch (error) {
    console.error('删除分类错误:', error);
    res.status(500).json({ message: '删除分类失败' });
  }
});

// 构建分类树形结构
function buildCategoryTree(categories) {
  const categoryMap = new Map();
  const rootCategories = [];

  // 创建分类映射
  categories.forEach(category => {
    categoryMap.set(category._id.toString(), {
      ...category.toObject(),
      children: []
    });
  });

  // 构建树形结构
  categories.forEach(category => {
    const categoryObj = categoryMap.get(category._id.toString());
    
    if (category.parent) {
      const parentCategory = categoryMap.get(category.parent.toString());
      if (parentCategory) {
        parentCategory.children.push(categoryObj);
      }
    } else {
      rootCategories.push(categoryObj);
    }
  });

  return rootCategories;
}

module.exports = router;
