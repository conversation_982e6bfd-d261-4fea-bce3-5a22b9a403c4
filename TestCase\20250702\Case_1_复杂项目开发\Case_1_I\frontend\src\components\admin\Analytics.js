import React, { useState, useEffect } from 'react';
import {
  Row,
  Col,
  Card,
  Statistic,
  Typography,
  Tabs,
  Table,
  DatePicker,
  Button,
  Space,
  Spin,
  Empty,
  message,
} from 'antd';
import {
  ShoppingCartOutlined,
  UserOutlined,
  DollarOutlined,
  ShoppingOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  Pie<PERSON><PERSON>Outlined,
  ReloadOutlined,
  DownloadOutlined,
} from '@ant-design/icons';
import { formatPrice, formatDate } from '../../utils/formatUtils';
import { getAnalyticsData } from '../../services/analyticsService';

// 引入图表库
import {
  Chart,
  LineElement,
  BarElement,
  PointElement,
  PieElement,
  ArcElement,
  CategoryScale,
  LinearScale,
  TimeScale,
  Tooltip,
  Legend,
  Title as ChartTitle,
} from 'chart.js';
import { Line, Bar, Pie } from 'react-chartjs-2';
import 'chartjs-adapter-date-fns';

// 注册图表组件
Chart.register(
  LineElement,
  BarElement,
  PointElement,
  PieElement,
  ArcElement,
  CategoryScale,
  LinearScale,
  TimeScale,
  Tooltip,
  Legend,
  ChartTitle
);

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

const Analytics = () => {
  const [loading, setLoading] = useState(false);
  const [dateRange, setDateRange] = useState([null, null]);
  const [analyticsData, setAnalyticsData] = useState(null);
  const [activeTab, setActiveTab] = useState('1');

  // 获取分析数据
  const fetchAnalyticsData = async (startDate = null, endDate = null) => {
    setLoading(true);
    try {
      // 这里应该调用实际的API
      // const data = await getAnalyticsData(startDate, endDate);
      // setAnalyticsData(data);
      
      // 模拟数据
      setTimeout(() => {
        const mockData = generateMockData(startDate, endDate);
        setAnalyticsData(mockData);
        setLoading(false);
      }, 1000);
    } catch (error) {
      message.error('获取分析数据失败');
      setLoading(false);
    }
  };

  // 生成模拟数据
  const generateMockData = (startDate, endDate) => {
    // 设置默认日期范围为过去30天
    const end = endDate ? new Date(endDate) : new Date();
    const start = startDate ? new Date(startDate) : new Date(end.getTime() - 30 * 24 * 60 * 60 * 1000);
    
    // 生成日期范围内的每一天
    const dateLabels = [];
    const currentDate = new Date(start);
    while (currentDate <= end) {
      dateLabels.push(new Date(currentDate));
      currentDate.setDate(currentDate.getDate() + 1);
    }
    
    // 生成销售数据
    const salesData = dateLabels.map(() => Math.floor(Math.random() * 10000) + 1000);
    const totalSales = salesData.reduce((sum, value) => sum + value, 0);
    
    // 生成订单数据
    const orderData = dateLabels.map(() => Math.floor(Math.random() * 50) + 5);
    const totalOrders = orderData.reduce((sum, value) => sum + value, 0);
    
    // 生成用户数据
    const newUserData = dateLabels.map(() => Math.floor(Math.random() * 20) + 1);
    const totalNewUsers = newUserData.reduce((sum, value) => sum + value, 0);
    const totalUsers = totalNewUsers + 500; // 假设基础用户数为500
    
    // 生成产品数据
    const productCategories = ['电子产品', '服装', '家居', '食品', '美妆'];
    const productSalesData = productCategories.map(() => Math.floor(Math.random() * 50000) + 10000);
    const totalProducts = 200;
    
    // 生成支付方式数据
    const paymentMethods = ['支付宝', '微信支付', '信用卡', '其他'];
    const paymentData = paymentMethods.map(() => Math.floor(Math.random() * 100) + 20);
    
    // 生成热门产品
    const topProducts = Array.from({ length: 10 }, (_, i) => ({
      _id: `product-${i}`,
      name: `热门商品 ${i + 1}`,
      sales: Math.floor(Math.random() * 1000) + 100,
      revenue: Math.floor(Math.random() * 50000) + 5000,
    }));
    
    // 生成最近订单
    const recentOrders = Array.from({ length: 10 }, (_, i) => ({
      _id: `order-${i}`,
      user: `用户${i}`,
      amount: Math.floor(Math.random() * 2000) + 100,
      date: new Date(end.getTime() - Math.floor(Math.random() * 10) * 24 * 60 * 60 * 1000).toISOString(),
      status: ['pending', 'processing', 'shipped', 'delivered'][Math.floor(Math.random() * 4)],
    }));
    
    return {
      summary: {
        totalSales,
        totalOrders,
        totalUsers,
        totalNewUsers,
        totalProducts,
        averageOrderValue: totalSales / totalOrders,
      },
      charts: {
        salesByDate: {
          labels: dateLabels,
          data: salesData,
        },
        ordersByDate: {
          labels: dateLabels,
          data: orderData,
        },
        newUsersByDate: {
          labels: dateLabels,
          data: newUserData,
        },
        salesByCategory: {
          labels: productCategories,
          data: productSalesData,
        },
        paymentMethods: {
          labels: paymentMethods,
          data: paymentData,
        },
      },
      tables: {
        topProducts,
        recentOrders,
      },
    };
  };

  // 初始加载数据
  useEffect(() => {
    fetchAnalyticsData();
  }, []);

  // 处理日期范围变化
  const handleDateRangeChange = (dates) => {
    if (dates && dates.length === 2) {
      setDateRange(dates);
    } else {
      setDateRange([null, null]);
    }
  };

  // 应用日期筛选
  const applyDateFilter = () => {
    const [startDate, endDate] = dateRange;
    fetchAnalyticsData(
      startDate ? startDate.format('YYYY-MM-DD') : null,
      endDate ? endDate.format('YYYY-MM-DD') : null
    );
  };

  // 重置日期筛选
  const resetDateFilter = () => {
    setDateRange([null, null]);
    fetchAnalyticsData();
  };

  // 导出数据
  const exportData = () => {
    message.success('数据导出功能将在后续版本中提供');
  };

  // 销售额图表配置
  const salesChartConfig = {
    data: analyticsData ? {
      labels: analyticsData.charts.salesByDate.labels,
      datasets: [
        {
          label: '销售额',
          data: analyticsData.charts.salesByDate.data,
          fill: false,
          borderColor: '#1890ff',
          tension: 0.1,
        },
      ],
    } : {},
    options: {
      responsive: true,
      plugins: {
        legend: {
          position: 'top',
        },
        title: {
          display: true,
          text: '销售额趋势',
        },
      },
      scales: {
        x: {
          type: 'time',
          time: {
            unit: 'day',
            tooltipFormat: 'yyyy-MM-dd',
          },
          title: {
            display: true,
            text: '日期',
          },
        },
        y: {
          beginAtZero: true,
          title: {
            display: true,
            text: '销售额 (¥)',
          },
        },
      },
    },
  };

  // 订单数图表配置
  const ordersChartConfig = {
    data: analyticsData ? {
      labels: analyticsData.charts.ordersByDate.labels,
      datasets: [
        {
          label: '订单数',
          data: analyticsData.charts.ordersByDate.data,
          backgroundColor: '#52c41a',
        },
      ],
    } : {},
    options: {
      responsive: true,
      plugins: {
        legend: {
          position: 'top',
        },
        title: {
          display: true,
          text: '订单数趋势',
        },
      },
      scales: {
        x: {
          type: 'time',
          time: {
            unit: 'day',
            tooltipFormat: 'yyyy-MM-dd',
          },
          title: {
            display: true,
            text: '日期',
          },
        },
        y: {
          beginAtZero: true,
          title: {
            display: true,
            text: '订单数',
          },
        },
      },
    },
  };

  // 新用户图表配置
  const usersChartConfig = {
    data: analyticsData ? {
      labels: analyticsData.charts.newUsersByDate.labels,
      datasets: [
        {
          label: '新用户',
          data: analyticsData.charts.newUsersByDate.data,
          fill: false,
          borderColor: '#722ed1',
          tension: 0.1,
        },
      ],
    } : {},
    options: {
      responsive: true,
      plugins: {
        legend: {
          position: 'top',
        },
        title: {
          display: true,
          text: '新用户趋势',
        },
      },
      scales: {
        x: {
          type: 'time',
          time: {
            unit: 'day',
            tooltipFormat: 'yyyy-MM-dd',
          },
          title: {
            display: true,
            text: '日期',
          },
        },
        y: {
          beginAtZero: true,
          title: {
            display: true,
            text: '新用户数',
          },
        },
      },
    },
  };

  // 分类销售图表配置
  const categoryChartConfig = {
    data: analyticsData ? {
      labels: analyticsData.charts.salesByCategory.labels,
      datasets: [
        {
          label: '销售额',
          data: analyticsData.charts.salesByCategory.data,
          backgroundColor: [
            '#1890ff',
            '#52c41a',
            '#faad14',
            '#f5222d',
            '#722ed1',
          ],
        },
      ],
    } : {},
    options: {
      responsive: true,
      plugins: {
        legend: {
          position: 'right',
        },
        title: {
          display: true,
          text: '各分类销售额',
        },
      },
    },
  };

  // 支付方式图表配置
  const paymentChartConfig = {
    data: analyticsData ? {
      labels: analyticsData.charts.paymentMethods.labels,
      datasets: [
        {
          label: '订单数',
          data: analyticsData.charts.paymentMethods.data,
          backgroundColor: [
            '#1890ff',
            '#52c41a',
            '#faad14',
            '#f5222d',
          ],
        },
      ],
    } : {},
    options: {
      responsive: true,
      plugins: {
        legend: {
          position: 'right',
        },
        title: {
          display: true,
          text: '支付方式分布',
        },
      },
    },
  };

  // 热门产品表格列
  const topProductsColumns = [
    {
      title: '商品名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '销量',
      dataIndex: 'sales',
      key: 'sales',
      sorter: (a, b) => a.sales - b.sales,
    },
    {
      title: '销售额',
      dataIndex: 'revenue',
      key: 'revenue',
      render: (revenue) => formatPrice(revenue),
      sorter: (a, b) => a.revenue - b.revenue,
    },
  ];

  // 最近订单表格列
  const recentOrdersColumns = [
    {
      title: '订单号',
      dataIndex: '_id',
      key: '_id',
    },
    {
      title: '用户',
      dataIndex: 'user',
      key: 'user',
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount) => formatPrice(amount),
    },
    {
      title: '日期',
      dataIndex: 'date',
      key: 'date',
      render: (date) => formatDate(date),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const statusMap = {
          pending: { text: '待处理', color: 'gold' },
          processing: { text: '处理中', color: 'blue' },
          shipped: { text: '已发货', color: 'cyan' },
          delivered: { text: '已送达', color: 'green' },
          cancelled: { text: '已取消', color: 'red' },
        };
        return (
          <span style={{ color: statusMap[status]?.color }}>
            {statusMap[status]?.text || status}
          </span>
        );
      },
    },
  ];

  return (
    <div className="analytics">
      <div className="page-header" style={{ marginBottom: '16px' }}>
        <Title level={2}>数据分析</Title>
      </div>

      <div className="date-filter" style={{ marginBottom: '24px' }}>
        <Space>
          <RangePicker
            value={dateRange}
            onChange={handleDateRangeChange}
          />
          <Button type="primary" onClick={applyDateFilter}>应用筛选</Button>
          <Button onClick={resetDateFilter}>重置</Button>
          <Button icon={<DownloadOutlined />} onClick={exportData}>导出数据</Button>
        </Space>
      </div>

      <Spin spinning={loading}>
        {analyticsData ? (
          <>
            <Row gutter={[16, 16]} className="summary-cards">
              <Col xs={24} sm={12} md={6}>
                <Card>
                  <Statistic
                    title="总销售额"
                    value={analyticsData.summary.totalSales}
                    precision={2}
                    prefix="¥"
                    valueStyle={{ color: '#1890ff' }}
                    prefix={<DollarOutlined />}
                  />
                </Card>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Card>
                  <Statistic
                    title="总订单数"
                    value={analyticsData.summary.totalOrders}
                    valueStyle={{ color: '#52c41a' }}
                    prefix={<ShoppingCartOutlined />}
                  />
                </Card>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Card>
                  <Statistic
                    title="总用户数"
                    value={analyticsData.summary.totalUsers}
                    valueStyle={{ color: '#722ed1' }}
                    prefix={<UserOutlined />}
                  />
                </Card>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Card>
                  <Statistic
                    title="平均订单金额"
                    value={analyticsData.summary.averageOrderValue}
                    precision={2}
                    prefix="¥"
                    valueStyle={{ color: '#faad14' }}
                    prefix={<ShoppingOutlined />}
                  />
                </Card>
              </Col>
            </Row>

            <div className="charts-section" style={{ marginTop: '24px' }}>
              <Tabs activeKey={activeTab} onChange={setActiveTab}>
                <TabPane
                  tab={
                    <span>
                      <LineChartOutlined />
                      销售趋势
                    </span>
                  }
                  key="1"
                >
                  <div style={{ height: '400px' }}>
                    <Line data={salesChartConfig.data} options={salesChartConfig.options} />
                  </div>
                </TabPane>
                <TabPane
                  tab={
                    <span>
                      <BarChartOutlined />
                      订单趋势
                    </span>
                  }
                  key="2"
                >
                  <div style={{ height: '400px' }}>
                    <Bar data={ordersChartConfig.data} options={ordersChartConfig.options} />
                  </div>
                </TabPane>
                <TabPane
                  tab={
                    <span>
                      <LineChartOutlined />
                      用户增长
                    </span>
                  }
                  key="3"
                >
                  <div style={{ height: '400px' }}>
                    <Line data={usersChartConfig.data} options={usersChartConfig.options} />
                  </div>
                </TabPane>
              </Tabs>
            </div>

            <Row gutter={[16, 16]} style={{ marginTop: '24px' }}>
              <Col xs={24} md={12}>
                <Card title="各分类销售额" extra={<ReloadOutlined onClick={() => fetchAnalyticsData()} />}>
                  <div style={{ height: '300px' }}>
                    <Pie data={categoryChartConfig.data} options={categoryChartConfig.options} />
                  </div>
                </Card>
              </Col>
              <Col xs={24} md={12}>
                <Card title="支付方式分布" extra={<ReloadOutlined onClick={() => fetchAnalyticsData()} />}>
                  <div style={{ height: '300px' }}>
                    <Pie data={paymentChartConfig.data} options={paymentChartConfig.options} />
                  </div>
                </Card>
              </Col>
            </Row>

            <Row gutter={[16, 16]} style={{ marginTop: '24px' }}>
              <Col xs={24} md={12}>
                <Card title="热门商品" extra={<ReloadOutlined onClick={() => fetchAnalyticsData()} />}>
                  <Table
                    columns={topProductsColumns}
                    dataSource={analyticsData.tables.topProducts}
                    rowKey="_id"
                    pagination={false}
                    size="small"
                  />
                </Card>
              </Col>
              <Col xs={24} md={12}>
                <Card title="最近订单" extra={<ReloadOutlined onClick={() => fetchAnalyticsData()} />}>
                  <Table
                    columns={recentOrdersColumns}
                    dataSource={analyticsData.tables.recentOrders}
                    rowKey="_id"
                    pagination={false}
                    size="small"
                  />
                </Card>
              </Col>
            </Row>
          </>
        ) : (
          <Empty description="暂无数据" />
        )}
      </Spin>
    </div>
  );
};

export default Analytics;