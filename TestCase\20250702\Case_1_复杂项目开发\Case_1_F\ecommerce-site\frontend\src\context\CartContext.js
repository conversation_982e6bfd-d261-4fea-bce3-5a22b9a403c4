import React, { createContext, useState, useEffect, useContext } from 'react';
import axios from 'axios';
import { AuthContext } from './AuthContext';

export const CartContext = createContext();

export const CartProvider = ({ children }) => {
  const [cart, setCart] = useState({ items: [], totalPrice: 0 });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const { isAuthenticated } = useContext(AuthContext);

  // 获取购物车数据
  const getCart = async () => {
    try {
      setLoading(true);
      setError(null);
      
      if (!isAuthenticated) {
        const localCart = localStorage.getItem('cart');
        if (localCart) {
          setCart(JSON.parse(localCart));
        }
      } else {
        const response = await axios.get('/cart');
        if (response.data.success) {
          setCart(response.data.data);
        }
      }
    } catch (err) {
      setError(err.response?.data?.message || '获取购物车失败，请稍后再试');
      console.error('获取购物车失败:', err);
    } finally {
      setLoading(false);
    }
  };

  // 添加商品到购物车
  const addToCart = async (productId, quantity = 1) => {
    try {
      setLoading(true);
      setError(null);
      
      if (!isAuthenticated) {
        // 本地购物车处理
        // 先获取商品信息
        const productResponse = await axios.get(`/products/${productId}`);
        if (!productResponse.data.success) {
          throw new Error('获取商品信息失败');
        }
        
        const product = productResponse.data.data;
        
        // 检查库存
        if (product.stock < quantity) {
          setError('商品库存不足');
          return false;
        }
        
        const existingItemIndex = cart.items.findIndex(
          item => item.product._id === productId
        );
        
        const updatedItems = [...cart.items];
        
        if (existingItemIndex > -1) {
          // 更新已有商品的数量
          const newQuantity = updatedItems[existingItemIndex].quantity + quantity;
          
          if (product.stock < newQuantity) {
            setError('商品库存不足');
            return false;
          }
          
          updatedItems[existingItemIndex].quantity = newQuantity;
        } else {
          // 添加新商品
          updatedItems.push({
            product: {
              _id: product._id,
              name: product.name,
              price: product.price,
              images: product.images,
              stock: product.stock
            },
            quantity,
            price: product.price
          });
        }
        
        // 计算总价
        const totalPrice = updatedItems.reduce((total, item) => {
          return total + (item.price * item.quantity);
        }, 0);
        
        const updatedCart = { items: updatedItems, totalPrice };
        setCart(updatedCart);
        localStorage.setItem('cart', JSON.stringify(updatedCart));
        return true;
      } else {
        // 服务器购物车处理
        const response = await axios.post('/cart', { productId, quantity });
        if (response.data.success) {
          setCart(response.data.data);
          return true;
        }
      }
    } catch (err) {
      setError(err.response?.data?.message || '添加商品到购物车失败，请稍后再试');
      return false;
    } finally {
      setLoading(false);
    }
  };

  // 更新购物车项数量
  const updateCartItem = async (itemId, quantity) => {
    try {
      setLoading(true);
      setError(null);
      
      if (!isAuthenticated) {
        // 本地购物车处理
        const updatedItems = [...cart.items];
        const itemIndex = updatedItems.findIndex(item => item.product._id === itemId);
        
        if (itemIndex === -1) {
          setError('商品不存在');
          return false;
        }
        
        // 检查库存
        if (updatedItems[itemIndex].product.stock < quantity) {
          setError('商品库存不足');
          return false;
        }
        
        updatedItems[itemIndex].quantity = quantity;
        
        // 计算总价
        const totalPrice = updatedItems.reduce((total, item) => {
          return total + (item.price * item.quantity);
        }, 0);
        
        const updatedCart = { items: updatedItems, totalPrice };
        setCart(updatedCart);
        localStorage.setItem('cart', JSON.stringify(updatedCart));
        return true;
      } else {
        // 服务器购物车处理
        const response = await axios.put(`/cart/${itemId}`, { quantity });
        if (response.data.success) {
          setCart(response.data.data);
          return true;
        }
      }
    } catch (err) {
      setError(err.response?.data?.message || '更新购物车失败，请稍后再试');
      return false;
    } finally {
      setLoading(false);
    }
  };

  // 移除购物车项
  const removeCartItem = async (itemId) => {
    try {
      setLoading(true);
      setError(null);
      
      if (!isAuthenticated) {
        // 本地购物车处理
        const updatedItems = cart.items.filter(item => item.product._id !== itemId);
        
        // 计算总价
        const totalPrice = updatedItems.reduce((total, item) => {
          return total + (item.price * item.quantity);
        }, 0);
        
        const updatedCart = { items: updatedItems, totalPrice };
        setCart(updatedCart);
        localStorage.setItem('cart', JSON.stringify(updatedCart));
        return true;
      } else {
        // 服务器购物车处理
        const response = await axios.delete(`/cart/${itemId}`);
        if (response.data.success) {
          setCart(response.data.data);
          return true;
        }
      }
    } catch (err) {
      setError(err.response?.data?.message || '从购物车移除商品失败，请稍后再试');
      return false;
    } finally {
      setLoading(false);
    }
  };

  // 清空购物车
  const clearCart = async () => {
    try {
      setLoading(true);
      setError(null);
      
      if (!isAuthenticated) {
        // 本地购物车处理
        const emptyCart = { items: [], totalPrice: 0 };
        setCart(emptyCart);
        localStorage.setItem('cart', JSON.stringify(emptyCart));
        return true;
      } else {
        // 服务器购物车处理
        const response = await axios.delete('/cart');
        if (response.data.success) {
          setCart(response.data.data);
          return true;
        }
      }
    } catch (err) {
      setError(err.response?.data?.message || '清空购物车失败，请稍后再试');
      return false;
    } finally {
      setLoading(false);
    }
  };

  // 登录状态变化时获取购物车
  useEffect(() => {
    getCart();
  }, [isAuthenticated]);

  return (
    <CartContext.Provider
      value={{
        cart,
        loading,
        error,
        addToCart,
        updateCartItem,
        removeCartItem,
        clearCart,
        getCart
      }}
    >
      {children}
    </CartContext.Provider>
  );
}; 