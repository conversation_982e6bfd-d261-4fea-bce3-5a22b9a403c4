const express = require('express');
const router = express.Router();
const { check } = require('express-validator');
const {
  getCategories,
  getCategory,
  getCategoryWithChildren,
  createCategory,
  updateCategory,
  deleteCategory
} = require('../controllers/category.controller');
const { protect, authorize } = require('../middleware/auth.middleware');

// 公开路由
router.get('/', getCategories);
router.get('/:id', getCategory);
router.get('/:id/with-children', getCategoryWithChildren);

// 受保护的路由 - 仅限管理员访问
router.post(
  '/',
  [
    protect, 
    authorize('admin'),
    check('name', '请提供分类名称').not().isEmpty(),
    check('description', '请提供分类描述').optional()
  ],
  createCategory
);

router.put('/:id', protect, authorize('admin'), updateCategory);
router.delete('/:id', protect, authorize('admin'), deleteCategory);

module.exports = router; 