import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Typography,
  Popconfirm,
  message,
  Upload,
  Image,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UploadOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import {
  getAllCategories,
  createCategory,
  updateCategory,
  deleteCategory,
} from '../../redux/slices/categorySlice';
import { formatDate } from '../../utils/formatUtils';

const { Title, Text } = Typography;
const { TextArea } = Input;

const CategoryManagement = () => {
  const dispatch = useDispatch();
  const { categories, loading } = useSelector((state) => state.category);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingCategory, setEditingCategory] = useState(null);
  const [form] = Form.useForm();
  const [fileList, setFileList] = useState([]);

  // 获取所有分类
  useEffect(() => {
    dispatch(getAllCategories());
  }, [dispatch]);

  // 打开添加分类模态框
  const showAddModal = () => {
    setEditingCategory(null);
    form.resetFields();
    setFileList([]);
    setModalVisible(true);
  };

  // 打开编辑分类模态框
  const showEditModal = (category) => {
    setEditingCategory(category);
    form.setFieldsValue({
      name: category.name,
      description: category.description,
    });
    
    // 设置图片列表
    if (category.image) {
      setFileList([{
        uid: '-1',
        name: 'category-image.jpg',
        status: 'done',
        url: category.image,
      }]);
    } else {
      setFileList([]);
    }
    
    setModalVisible(true);
  };

  // 关闭模态框
  const handleCancel = () => {
    setModalVisible(false);
    setEditingCategory(null);
    form.resetFields();
    setFileList([]);
  };

  // 处理表单提交
  const handleSubmit = async (values) => {
    try {
      const categoryData = {
        ...values,
        image: fileList.length > 0 && fileList[0].url ? fileList[0].url : null,
      };

      if (editingCategory) {
        // 更新分类
        await dispatch(updateCategory({ id: editingCategory._id, categoryData }));
        message.success('分类更新成功');
      } else {
        // 创建分类
        await dispatch(createCategory(categoryData));
        message.success('分类创建成功');
      }
      setModalVisible(false);
      dispatch(getAllCategories());
    } catch (error) {
      message.error(`操作失败: ${error.message}`);
    }
  };

  // 处理删除分类
  const handleDelete = async (id) => {
    try {
      await dispatch(deleteCategory(id));
      message.success('分类删除成功');
      dispatch(getAllCategories());
    } catch (error) {
      message.error(`删除失败: ${error.message}`);
    }
  };

  // 自定义上传
  const customUpload = async ({ file, onSuccess, onError }) => {
    try {
      const formData = new FormData();
      formData.append('image', file);
      
      // 这里应该调用实际的API上传图片
      // const response = await uploadCategoryImage(formData);
      
      // 模拟上传成功
      setTimeout(() => {
        const response = {
          url: URL.createObjectURL(file),
        };
        
        setFileList([{
          uid: '-1',
          name: file.name,
          status: 'done',
          url: response.url,
        }]);
        
        onSuccess(response);
        message.success('图片上传成功');
      }, 500);
    } catch (error) {
      onError(error);
      message.error('图片上传失败');
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '分类图片',
      dataIndex: 'image',
      key: 'image',
      render: (image) => (
        image ? (
          <Image
            src={image}
            alt="分类图片"
            width={60}
            height={60}
            style={{ objectFit: 'cover' }}
          />
        ) : (
          <div
            style={{
              width: 60,
              height: 60,
              backgroundColor: '#f0f0f0',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <Text type="secondary">无图片</Text>
          </div>
        )
      ),
    },
    {
      title: '分类名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '商品数量',
      dataIndex: 'productCount',
      key: 'productCount',
      render: (_, record) => record.productCount || 0,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => formatDate(date),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="primary"
            icon={<EditOutlined />}
            size="small"
            onClick={() => showEditModal(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除此分类吗？"
            description="删除分类可能会影响关联的商品。"
            onConfirm={() => handleDelete(record._id)}
            okText="确定"
            cancelText="取消"
            icon={<ExclamationCircleOutlined style={{ color: 'red' }} />}
          >
            <Button
              type="primary"
              danger
              icon={<DeleteOutlined />}
              size="small"
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="category-management">
      <div className="page-header" style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '16px' }}>
        <Title level={2}>分类管理</Title>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={showAddModal}
        >
          添加分类
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={categories}
        rowKey="_id"
        loading={loading}
      />

      <Modal
        title={editingCategory ? '编辑分类' : '添加分类'}
        open={modalVisible}
        onCancel={handleCancel}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="name"
            label="分类名称"
            rules={[{ required: true, message: '请输入分类名称' }]}
          >
            <Input placeholder="请输入分类名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="分类描述"
          >
            <TextArea
              rows={4}
              placeholder="请输入分类描述（可选）"
            />
          </Form.Item>

          <Form.Item
            label="分类图片"
          >
            <Upload
              listType="picture-card"
              fileList={fileList}
              customRequest={customUpload}
              onRemove={() => setFileList([])}
              maxCount={1}
            >
              {fileList.length >= 1 ? null : (
                <div>
                  <PlusOutlined />
                  <div style={{ marginTop: 8 }}>上传图片</div>
                </div>
              )}
            </Upload>
            <Text type="secondary">建议尺寸400x400px，支持jpg、png格式</Text>
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit">
              {editingCategory ? '更新分类' : '添加分类'}
            </Button>
            <Button style={{ marginLeft: 8 }} onClick={handleCancel}>
              取消
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default CategoryManagement;