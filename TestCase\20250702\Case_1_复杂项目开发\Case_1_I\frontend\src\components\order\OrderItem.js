import React from 'react';
import { Card, Typography, Image, Divider, Tag } from 'antd';
import { Link } from 'react-router-dom';
import { formatPrice, formatDate, formatOrderStatus } from '../../utils/formatUtils';

const { Text, Title } = Typography;

const OrderItem = ({ order }) => {
  // 获取订单状态标签颜色
  const getStatusTagColor = (status) => {
    switch (status) {
      case 'pending':
        return 'orange';
      case 'processing':
        return 'blue';
      case 'shipped':
        return 'cyan';
      case 'delivered':
        return 'green';
      case 'cancelled':
        return 'red';
      default:
        return 'default';
    }
  };

  return (
    <Card className="order-item" style={{ marginBottom: '16px' }}>
      {/* 订单头部信息 */}
      <div className="order-header" style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '16px' }}>
        <div>
          <Text type="secondary">订单号: </Text>
          <Link to={`/order/${order._id}`}>
            <Text strong>{order._id}</Text>
          </Link>
        </div>
        <div>
          <Text type="secondary" style={{ marginRight: '8px' }}>下单时间: {formatDate(order.createdAt)}</Text>
          <Tag color={getStatusTagColor(order.status)}>{formatOrderStatus(order.status)}</Tag>
        </div>
      </div>

      <Divider style={{ margin: '12px 0' }} />

      {/* 订单商品列表 */}
      <div className="order-items">
        {order.orderItems.map((item) => (
          <div key={item._id} className="order-product-item" style={{ display: 'flex', marginBottom: '12px' }}>
            <div className="product-image" style={{ marginRight: '16px' }}>
              <Link to={`/product/${item.product._id}`}>
                <Image
                  src={item.product.images && item.product.images.length > 0 ? item.product.images[0] : '/placeholder.png'}
                  alt={item.product.name}
                  width={60}
                  height={60}
                  style={{ objectFit: 'cover' }}
                  preview={false}
                />
              </Link>
            </div>
            <div className="product-info" style={{ flex: 1 }}>
              <Link to={`/product/${item.product._id}`}>
                <Text strong>{item.product.name}</Text>
              </Link>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: '4px' }}>
                <Text type="secondary">数量: {item.quantity}</Text>
                <Text>{formatPrice(item.price)}</Text>
              </div>
            </div>
          </div>
        ))}
      </div>

      <Divider style={{ margin: '12px 0' }} />

      {/* 订单底部信息 */}
      <div className="order-footer" style={{ display: 'flex', justifyContent: 'space-between' }}>
        <div>
          <Text type="secondary">共 {order.orderItems.length} 件商品</Text>
        </div>
        <div style={{ textAlign: 'right' }}>
          <div>
            <Text type="secondary">实付金额: </Text>
            <Text style={{ fontSize: '16px', color: '#f5222d' }} strong>
              {formatPrice(order.totalPrice)}
            </Text>
          </div>
          <div style={{ marginTop: '8px' }}>
            <Link to={`/order/${order._id}`}>查看详情</Link>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default OrderItem;