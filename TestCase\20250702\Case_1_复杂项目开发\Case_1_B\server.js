const express = require('express');
const bodyParser = require('body-parser');
const app = express();

// 中间件
app.use(bodyParser.json());

// 模拟用户数据
let users = [];

// 模拟商品数据
const products = [
    { id: 1, name: "商品1", price: 100 },
    { id: 2, name: "商品2", price: 200 },
    { id: 3, name: "商品3", price: 300 }
];

// 模拟订单数据
let orders = [];

// 用户注册
app.post('/api/register', (req, res) => {
    const { username, email, password } = req.body;
    users.push({ username, email, password });
    res.json({ success: true, message: '注册成功' });
});

// 用户登录
app.post('/api/login', (req, res) => {
    const { username, password } = req.body;
    const user = users.find(u => u.username === username && u.password === password);
    if (user) {
        res.json({ success: true, message: '登录成功' });
    } else {
        res.status(401).json({ success: false, message: '用户名或密码错误' });
    }
});

// 获取商品列表
app.get('/api/products', (req, res) => {
    res.json(products);
});

// 创建订单
app.post('/api/orders', (req, res) => {
    const { items } = req.body;
    const total = items.reduce((sum, item) => sum + item.price, 0);
    const order = {
        id: orders.length + 1,
        items,
        total,
        date: new Date().toLocaleString()
    };
    orders.push(order);
    res.json({ success: true, order });
});

// 模拟支付
app.post('/api/pay', (req, res) => {
    res.json({ success: true, message: '支付成功' });
});

// 启动服务器
const PORT = 3000;
app.listen(PORT, () => {
    console.log(`服务器运行在 http://localhost:${PORT}`);
});