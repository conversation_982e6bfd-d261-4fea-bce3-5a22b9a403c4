import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  InputNumber,
  Select,
  Upload,
  Typography,
  Popconfirm,
  Tag,
  message,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UploadOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { getAllCategories } from '../../redux/slices/categorySlice';
import {
  createProduct,
  updateProduct,
  deleteProduct,
  uploadProductImage,
  deleteProductImage,
} from '../../services/productService';
import { formatPrice, formatDate } from '../../utils/formatUtils';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const ProductManagement = () => {
  const dispatch = useDispatch();
  const { categories } = useSelector((state) => state.category);
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingProduct, setEditingProduct] = useState(null);
  const [form] = Form.useForm();
  const [fileList, setFileList] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 获取所有分类
  useEffect(() => {
    dispatch(getAllCategories());
  }, [dispatch]);

  // 获取产品列表
  const fetchProducts = async (page = 1, pageSize = 10) => {
    setLoading(true);
    try {
      // 这里应该调用实际的API
      // const response = await getProducts({ page, pageSize });
      // setProducts(response.products);
      // setPagination({
      //   current: response.page,
      //   pageSize: response.pageSize,
      //   total: response.total,
      // });
      
      // 模拟数据
      setTimeout(() => {
        const mockProducts = Array.from({ length: 20 }, (_, i) => ({
          _id: `product-${i}`,
          name: `测试商品 ${i}`,
          price: Math.floor(Math.random() * 1000) + 10,
          originalPrice: Math.floor(Math.random() * 1500) + 100,
          category: categories[Math.floor(Math.random() * categories.length)],
          countInStock: Math.floor(Math.random() * 100),
          rating: (Math.random() * 5).toFixed(1),
          numReviews: Math.floor(Math.random() * 100),
          createdAt: new Date().toISOString(),
        }));
        
        setProducts(mockProducts);
        setPagination({
          current: page,
          pageSize: pageSize,
          total: 100,
        });
        setLoading(false);
      }, 500);
    } catch (error) {
      message.error('获取商品列表失败');
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProducts();
  }, []);

  // 处理表格分页变化
  const handleTableChange = (pagination) => {
    fetchProducts(pagination.current, pagination.pageSize);
  };

  // 打开添加商品模态框
  const showAddModal = () => {
    setEditingProduct(null);
    form.resetFields();
    setFileList([]);
    setModalVisible(true);
  };

  // 打开编辑商品模态框
  const showEditModal = (product) => {
    setEditingProduct(product);
    form.setFieldsValue({
      name: product.name,
      price: product.price,
      originalPrice: product.originalPrice,
      category: product.category?._id,
      countInStock: product.countInStock,
      description: product.description,
      specifications: product.specifications,
    });
    
    // 设置图片列表
    if (product.images && product.images.length > 0) {
      const images = product.images.map((url, index) => ({
        uid: `-${index}`,
        name: `image-${index}.jpg`,
        status: 'done',
        url,
      }));
      setFileList(images);
    } else {
      setFileList([]);
    }
    
    setModalVisible(true);
  };

  // 关闭模态框
  const handleCancel = () => {
    setModalVisible(false);
    setEditingProduct(null);
    form.resetFields();
    setFileList([]);
  };

  // 处理表单提交
  const handleSubmit = async (values) => {
    try {
      if (editingProduct) {
        // 更新商品
        await updateProduct(editingProduct._id, values);
        message.success('商品更新成功');
      } else {
        // 创建商品
        await createProduct(values);
        message.success('商品创建成功');
      }
      setModalVisible(false);
      fetchProducts(pagination.current, pagination.pageSize);
    } catch (error) {
      message.error(`操作失败: ${error.message}`);
    }
  };

  // 处理删除商品
  const handleDelete = async (id) => {
    try {
      await deleteProduct(id);
      message.success('商品删除成功');
      fetchProducts(pagination.current, pagination.pageSize);
    } catch (error) {
      message.error(`删除失败: ${error.message}`);
    }
  };

  // 自定义上传
  const customUpload = async ({ file, onSuccess, onError }) => {
    try {
      const formData = new FormData();
      formData.append('image', file);
      
      let response;
      if (editingProduct) {
        response = await uploadProductImage(editingProduct._id, formData);
      } else {
        // 如果是新商品，先创建商品再上传图片
        message.info('请先保存商品信息，然后再上传图片');
        onSuccess();
        return;
      }
      
      onSuccess(response);
      message.success('图片上传成功');
    } catch (error) {
      onError(error);
      message.error('图片上传失败');
    }
  };

  // 处理图片删除
  const handleImageRemove = async (file) => {
    if (editingProduct && file.url) {
      try {
        await deleteProductImage(editingProduct._id, file.url);
        message.success('图片删除成功');
        return true;
      } catch (error) {
        message.error('图片删除失败');
        return false;
      }
    }
    return true;
  };

  // 表格列定义
  const columns = [
    {
      title: '商品名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => <a href={`/product/${record._id}`} target="_blank" rel="noopener noreferrer">{text}</a>,
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      render: (price, record) => (
        <div>
          <Text strong>{formatPrice(price)}</Text>
          {record.originalPrice > price && (
            <Text delete type="secondary" style={{ marginLeft: '8px' }}>
              {formatPrice(record.originalPrice)}
            </Text>
          )}
        </div>
      ),
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      render: (category) => category?.name || '未分类',
    },
    {
      title: '库存',
      dataIndex: 'countInStock',
      key: 'countInStock',
      render: (count) => (
        <Tag color={count > 0 ? 'green' : 'red'}>
          {count > 0 ? `有货(${count})` : '缺货'}
        </Tag>
      ),
    },
    {
      title: '评分',
      dataIndex: 'rating',
      key: 'rating',
      render: (rating, record) => `${rating} (${record.numReviews}条评价)`,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => formatDate(date),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="primary"
            icon={<EditOutlined />}
            size="small"
            onClick={() => showEditModal(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除此商品吗？"
            onConfirm={() => handleDelete(record._id)}
            okText="确定"
            cancelText="取消"
            icon={<ExclamationCircleOutlined style={{ color: 'red' }} />}
          >
            <Button
              type="primary"
              danger
              icon={<DeleteOutlined />}
              size="small"
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="product-management">
      <div className="page-header" style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '16px' }}>
        <Title level={2}>商品管理</Title>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={showAddModal}
        >
          添加商品
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={products}
        rowKey="_id"
        pagination={pagination}
        loading={loading}
        onChange={handleTableChange}
      />

      <Modal
        title={editingProduct ? '编辑商品' : '添加商品'}
        open={modalVisible}
        onCancel={handleCancel}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="name"
            label="商品名称"
            rules={[{ required: true, message: '请输入商品名称' }]}
          >
            <Input placeholder="请输入商品名称" />
          </Form.Item>

          <Form.Item
            name="category"
            label="商品分类"
            rules={[{ required: true, message: '请选择商品分类' }]}
          >
            <Select placeholder="请选择商品分类">
              {categories.map((category) => (
                <Option key={category._id} value={category._id}>
                  {category.name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item label="商品图片">
            <Upload
              listType="picture-card"
              fileList={fileList}
              customRequest={customUpload}
              onRemove={handleImageRemove}
              onChange={({ fileList }) => setFileList(fileList)}
              multiple
              maxCount={5}
            >
              {fileList.length >= 5 ? null : (
                <div>
                  <PlusOutlined />
                  <div style={{ marginTop: 8 }}>上传图片</div>
                </div>
              )}
            </Upload>
            <Text type="secondary">最多上传5张图片，建议尺寸800x800px</Text>
          </Form.Item>

          <Form.Item
            name="price"
            label="售价"
            rules={[{ required: true, message: '请输入商品售价' }]}
          >
            <InputNumber
              min={0}
              step={0.01}
              precision={2}
              style={{ width: '100%' }}
              placeholder="请输入商品售价"
              prefix="¥"
            />
          </Form.Item>

          <Form.Item
            name="originalPrice"
            label="原价"
          >
            <InputNumber
              min={0}
              step={0.01}
              precision={2}
              style={{ width: '100%' }}
              placeholder="请输入商品原价（可选）"
              prefix="¥"
            />
          </Form.Item>

          <Form.Item
            name="countInStock"
            label="库存数量"
            rules={[{ required: true, message: '请输入库存数量' }]}
          >
            <InputNumber
              min={0}
              style={{ width: '100%' }}
              placeholder="请输入库存数量"
            />
          </Form.Item>

          <Form.Item
            name="description"
            label="商品描述"
            rules={[{ required: true, message: '请输入商品描述' }]}
          >
            <TextArea
              rows={4}
              placeholder="请输入商品描述"
            />
          </Form.Item>

          <Form.Item
            name="specifications"
            label="规格参数"
          >
            <TextArea
              rows={4}
              placeholder="请输入商品规格参数（可选）"
            />
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit">
              {editingProduct ? '更新商品' : '添加商品'}
            </Button>
            <Button style={{ marginLeft: 8 }} onClick={handleCancel}>
              取消
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ProductManagement;