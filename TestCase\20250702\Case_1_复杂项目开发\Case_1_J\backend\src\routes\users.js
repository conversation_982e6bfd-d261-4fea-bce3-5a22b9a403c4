const express = require('express');
const User = require('../models/User');
const { authenticateToken, requireAdmin } = require('../middleware/auth');
const { validateAddress } = require('../middleware/validation');

const router = express.Router();

// 获取用户个人信息
router.get('/profile', authenticateToken, async (req, res) => {
  try {
    res.json({
      user: req.user.toPublicJSON()
    });
  } catch (error) {
    console.error('获取用户信息错误:', error);
    res.status(500).json({ message: '获取用户信息失败' });
  }
});

// 更新用户个人信息
router.put('/profile', authenticateToken, async (req, res) => {
  try {
    const { username, phone, avatar, gender, birthday } = req.body;
    const userId = req.user._id;

    // 检查用户名是否已被其他用户使用
    if (username && username !== req.user.username) {
      const existingUser = await User.findOne({ 
        username, 
        _id: { $ne: userId } 
      });
      if (existingUser) {
        return res.status(400).json({ message: '用户名已被使用' });
      }
    }

    const updateData = {};
    if (username) updateData.username = username;
    if (phone) updateData.phone = phone;
    if (avatar) updateData.avatar = avatar;
    if (gender) updateData.gender = gender;
    if (birthday) updateData.birthday = birthday;

    const user = await User.findByIdAndUpdate(
      userId,
      updateData,
      { new: true, runValidators: true }
    );

    res.json({
      message: '个人信息更新成功',
      user: user.toPublicJSON()
    });
  } catch (error) {
    console.error('更新用户信息错误:', error);
    if (error.name === 'ValidationError') {
      return res.status(400).json({ message: '输入数据格式错误' });
    }
    res.status(500).json({ message: '更新用户信息失败' });
  }
});

// 获取用户地址列表
router.get('/addresses', authenticateToken, async (req, res) => {
  try {
    const user = await User.findById(req.user._id).select('addresses');
    res.json({
      addresses: user.addresses
    });
  } catch (error) {
    console.error('获取地址列表错误:', error);
    res.status(500).json({ message: '获取地址列表失败' });
  }
});

// 添加用户地址
router.post('/addresses', authenticateToken, validateAddress, async (req, res) => {
  try {
    const { name, phone, province, city, district, detail, isDefault } = req.body;
    const userId = req.user._id;

    const user = await User.findById(userId);
    
    // 如果设置为默认地址，先取消其他默认地址
    if (isDefault) {
      user.addresses.forEach(addr => {
        addr.isDefault = false;
      });
    }

    user.addresses.push({
      name,
      phone,
      province,
      city,
      district,
      detail,
      isDefault: isDefault || false
    });

    await user.save();

    res.status(201).json({
      message: '地址添加成功',
      addresses: user.addresses
    });
  } catch (error) {
    console.error('添加地址错误:', error);
    res.status(500).json({ message: '添加地址失败' });
  }
});

// 更新用户地址
router.put('/addresses/:addressId', authenticateToken, validateAddress, async (req, res) => {
  try {
    const { addressId } = req.params;
    const { name, phone, province, city, district, detail, isDefault } = req.body;
    const userId = req.user._id;

    const user = await User.findById(userId);
    const address = user.addresses.id(addressId);

    if (!address) {
      return res.status(404).json({ message: '地址不存在' });
    }

    // 如果设置为默认地址，先取消其他默认地址
    if (isDefault) {
      user.addresses.forEach(addr => {
        if (addr._id.toString() !== addressId) {
          addr.isDefault = false;
        }
      });
    }

    address.name = name;
    address.phone = phone;
    address.province = province;
    address.city = city;
    address.district = district;
    address.detail = detail;
    address.isDefault = isDefault || false;

    await user.save();

    res.json({
      message: '地址更新成功',
      addresses: user.addresses
    });
  } catch (error) {
    console.error('更新地址错误:', error);
    res.status(500).json({ message: '更新地址失败' });
  }
});

// 删除用户地址
router.delete('/addresses/:addressId', authenticateToken, async (req, res) => {
  try {
    const { addressId } = req.params;
    const userId = req.user._id;

    const user = await User.findById(userId);
    user.addresses.id(addressId).remove();
    await user.save();

    res.json({
      message: '地址删除成功',
      addresses: user.addresses
    });
  } catch (error) {
    console.error('删除地址错误:', error);
    res.status(500).json({ message: '删除地址失败' });
  }
});

// 管理员：获取用户列表
router.get('/', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { page = 1, limit = 10, search, role, isActive } = req.query;
    const skip = (page - 1) * limit;

    const filter = {};
    if (search) {
      filter.$or = [
        { username: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }
    if (role) filter.role = role;
    if (isActive !== undefined) filter.isActive = isActive === 'true';

    const users = await User.find(filter)
      .select('-password')
      .skip(skip)
      .limit(parseInt(limit))
      .sort({ createdAt: -1 });

    const total = await User.countDocuments(filter);

    res.json({
      users,
      pagination: {
        current: parseInt(page),
        pageSize: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('获取用户列表错误:', error);
    res.status(500).json({ message: '获取用户列表失败' });
  }
});

module.exports = router;
