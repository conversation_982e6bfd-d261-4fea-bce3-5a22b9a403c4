const Order = require('../models/Order');
const Cart = require('../models/Cart');
const Product = require('../models/Product');
const Payment = require('../models/Payment');
const { successResponse, errorResponse, paginatedResponse } = require('../utils/apiResponse');

/**
 * 创建订单
 * @route POST /api/orders
 * @access Private
 */
exports.createOrder = async (req, res, next) => {
  try {
    const { shippingAddress, paymentMethod } = req.body;

    // 获取用户购物车
    const cart = await Cart.findOne({ user: req.user._id }).populate({
      path: 'items.product',
      select: 'name price stock isActive',
    });

    if (!cart || cart.items.length === 0) {
      return errorResponse(res, '购物车为空，无法创建订单', 400);
    }

    // 验证商品库存
    for (const item of cart.items) {
      if (!item.product.isActive) {
        return errorResponse(
          res,
          `商品 ${item.product.name} 已下架，请从购物车中移除`,
          400
        );
      }

      if (item.product.stock < item.quantity) {
        return errorResponse(
          res,
          `商品 ${item.product.name} 库存不足，当前库存: ${item.product.stock}`,
          400
        );
      }
    }

    // 计算价格
    const itemsPrice = cart.totalPrice;
    const shippingPrice = itemsPrice > 100 ? 0 : 10; // 满100免运费
    const taxPrice = itemsPrice * 0.05; // 5%税率
    const totalPrice = itemsPrice + shippingPrice + taxPrice;

    // 创建订单项
    const orderItems = cart.items.map((item) => ({
      product: item.product._id,
      quantity: item.quantity,
      price: item.price,
    }));

    // 创建订单
    const order = await Order.create({
      user: req.user._id,
      items: orderItems,
      shippingAddress,
      paymentMethod,
      itemsPrice,
      shippingPrice,
      taxPrice,
      totalPrice,
    });

    // 更新商品库存
    for (const item of cart.items) {
      const product = await Product.findById(item.product._id);
      product.stock -= item.quantity;
      await product.save();
    }

    // 清空购物车
    cart.items = [];
    cart.totalPrice = 0;
    await cart.save();

    // 返回创建的订单
    const populatedOrder = await Order.findById(order._id).populate({
      path: 'items.product',
      select: 'name images',
    });

    return successResponse(
      res,
      { order: populatedOrder },
      '订单创建成功',
      201
    );
  } catch (error) {
    next(error);
  }
};

/**
 * 获取用户所有订单
 * @route GET /api/orders
 * @access Private
 */
exports.getUserOrders = async (req, res, next) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // 查询条件
    const query = { user: req.user._id };

    // 按状态筛选
    if (req.query.status) {
      query.status = req.query.status;
    }

    // 执行查询
    const orders = await Order.find(query)
      .populate({
        path: 'items.product',
        select: 'name images',
      })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    // 获取总数
    const total = await Order.countDocuments(query);

    return paginatedResponse(
      res,
      orders,
      page,
      limit,
      total,
      '获取订单列表成功'
    );
  } catch (error) {
    next(error);
  }
};

/**
 * 获取单个订单
 * @route GET /api/orders/:id
 * @access Private
 */
exports.getOrderById = async (req, res, next) => {
  try {
    const order = await Order.findById(req.params.id).populate({
      path: 'items.product',
      select: 'name images price',
    });

    if (!order) {
      return errorResponse(res, '订单不存在', 404);
    }

    // 验证订单所有者
    if (order.user.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
      return errorResponse(res, '无权访问此订单', 403);
    }

    return successResponse(res, { order }, '获取订单成功');
  } catch (error) {
    next(error);
  }
};

/**
 * 更新订单状态（管理员）
 * @route PUT /api/orders/:id/status
 * @access Private/Admin
 */
exports.updateOrderStatus = async (req, res, next) => {
  try {
    const { status } = req.body;
    const order = await Order.findById(req.params.id);

    if (!order) {
      return errorResponse(res, '订单不存在', 404);
    }

    // 更新状态
    order.status = status;

    // 如果状态为已发货，更新发货时间
    if (status === 'shipped') {
      order.isDelivered = true;
      order.deliveredAt = Date.now();
    }

    await order.save();

    return successResponse(res, { order }, '订单状态更新成功');
  } catch (error) {
    next(error);
  }
};

/**
 * 取消订单
 * @route PUT /api/orders/:id/cancel
 * @access Private
 */
exports.cancelOrder = async (req, res, next) => {
  try {
    const order = await Order.findById(req.params.id);

    if (!order) {
      return errorResponse(res, '订单不存在', 404);
    }

    // 验证订单所有者
    if (order.user.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
      return errorResponse(res, '无权取消此订单', 403);
    }

    // 只有待处理的订单可以取消
    if (order.status !== 'pending') {
      return errorResponse(res, '只有待处理的订单可以取消', 400);
    }

    // 更新状态
    order.status = 'cancelled';
    await order.save();

    // 恢复商品库存
    for (const item of order.items) {
      const product = await Product.findById(item.product);
      if (product) {
        product.stock += item.quantity;
        await product.save();
      }
    }

    return successResponse(res, { order }, '订单已取消');
  } catch (error) {
    next(error);
  }
};

/**
 * 获取所有订单（管理员）
 * @route GET /api/orders/admin
 * @access Private/Admin
 */
exports.getAllOrders = async (req, res, next) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // 查询条件
    const query = {};

    // 按状态筛选
    if (req.query.status) {
      query.status = req.query.status;
    }

    // 按用户筛选
    if (req.query.user) {
      query.user = req.query.user;
    }

    // 按订单号筛选
    if (req.query.orderNumber) {
      query.orderNumber = { $regex: req.query.orderNumber, $options: 'i' };
    }

    // 按日期范围筛选
    if (req.query.startDate || req.query.endDate) {
      query.createdAt = {};
      if (req.query.startDate) {
        query.createdAt.$gte = new Date(req.query.startDate);
      }
      if (req.query.endDate) {
        query.createdAt.$lte = new Date(req.query.endDate);
      }
    }

    // 执行查询
    const orders = await Order.find(query)
      .populate('user', 'name email')
      .populate({
        path: 'items.product',
        select: 'name images',
      })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    // 获取总数
    const total = await Order.countDocuments(query);

    return paginatedResponse(
      res,
      orders,
      page,
      limit,
      total,
      '获取订单列表成功'
    );
  } catch (error) {
    next(error);
  }
};

/**
 * 获取订单统计（管理员）
 * @route GET /api/orders/stats
 * @access Private/Admin
 */
exports.getOrderStats = async (req, res, next) => {
  try {
    // 总订单数
    const totalOrders = await Order.countDocuments();

    // 总销售额
    const salesResult = await Order.aggregate([
      {
        $match: {
          status: { $nin: ['cancelled'] },
        },
      },
      {
        $group: {
          _id: null,
          totalSales: { $sum: '$totalPrice' },
        },
      },
    ]);
    const totalSales = salesResult.length > 0 ? salesResult[0].totalSales : 0;

    // 各状态订单数量
    const statusCounts = await Order.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
        },
      },
    ]);

    // 最近7天的订单数量
    const last7Days = new Date();
    last7Days.setDate(last7Days.getDate() - 7);

    const dailyOrders = await Order.aggregate([
      {
        $match: {
          createdAt: { $gte: last7Days },
        },
      },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$createdAt' },
          },
          count: { $sum: 1 },
          sales: { $sum: '$totalPrice' },
        },
      },
      { $sort: { _id: 1 } },
    ]);

    return successResponse(
      res,
      {
        totalOrders,
        totalSales,
        statusCounts,
        dailyOrders,
      },
      '获取订单统计成功'
    );
  } catch (error) {
    next(error);
  }
};