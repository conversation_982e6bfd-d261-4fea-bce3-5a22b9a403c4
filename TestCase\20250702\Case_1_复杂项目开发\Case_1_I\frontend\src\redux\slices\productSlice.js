import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import productService from '../../services/productService';

// 初始状态
const initialState = {
  products: [],
  product: null,
  loading: false,
  error: null,
  success: false,
  page: 1,
  pages: 1,
  totalProducts: 0,
  reviews: [],
};

// 异步action - 获取产品列表
export const getProducts = createAsyncThunk(
  'product/getProducts',
  async ({ keyword = '', pageNumber = 1, category = '', sortBy = '', minPrice = '', maxPrice = '' }, { rejectWithValue }) => {
    try {
      return await productService.getProducts(keyword, pageNumber, category, sortBy, minPrice, maxPrice);
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || error.message);
    }
  }
);

// 异步action - 获取产品详情
export const getProductDetails = createAsyncThunk(
  'product/getProductDetails',
  async (id, { rejectWithValue }) => {
    try {
      return await productService.getProductDetails(id);
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || error.message);
    }
  }
);

// 异步action - 创建产品评论
export const createProductReview = createAsyncThunk(
  'product/createProductReview',
  async ({ productId, review }, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      return await productService.createProductReview(
        productId,
        review,
        auth.userInfo.token
      );
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || error.message);
    }
  }
);

// 异步action - 获取产品评论
export const getProductReviews = createAsyncThunk(
  'product/getProductReviews',
  async (productId, { rejectWithValue }) => {
    try {
      return await productService.getProductReviews(productId);
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || error.message);
    }
  }
);

// 创建slice
const productSlice = createSlice({
  name: 'product',
  initialState,
  reducers: {
    clearProductDetails: (state) => {
      state.product = null;
    },
    clearProductError: (state) => {
      state.error = null;
    },
    resetProductSuccess: (state) => {
      state.success = false;
    },
  },
  extraReducers: (builder) => {
    builder
      // 获取产品列表
      .addCase(getProducts.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getProducts.fulfilled, (state, action) => {
        state.loading = false;
        state.products = action.payload.products;
        state.page = action.payload.page;
        state.pages = action.payload.pages;
        state.totalProducts = action.payload.totalProducts;
      })
      .addCase(getProducts.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // 获取产品详情
      .addCase(getProductDetails.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getProductDetails.fulfilled, (state, action) => {
        state.loading = false;
        state.product = action.payload;
      })
      .addCase(getProductDetails.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // 创建产品评论
      .addCase(createProductReview.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = false;
      })
      .addCase(createProductReview.fulfilled, (state) => {
        state.loading = false;
        state.success = true;
      })
      .addCase(createProductReview.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        state.success = false;
      })
      // 获取产品评论
      .addCase(getProductReviews.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getProductReviews.fulfilled, (state, action) => {
        state.loading = false;
        state.reviews = action.payload;
      })
      .addCase(getProductReviews.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { clearProductDetails, clearProductError, resetProductSuccess } = productSlice.actions;
export default productSlice.reducer;