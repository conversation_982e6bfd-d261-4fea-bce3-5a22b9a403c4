import React, { useState, useEffect, useContext } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { Form, Input, Button, Card, Typography, Divider, Alert, Spin } from 'antd';
import { UserOutlined, LockOutlined, GoogleOutlined, FacebookOutlined } from '@ant-design/icons';
import { AuthContext } from '../context/AuthContext';

const { Title, Text } = Typography;

const Login = () => {
  const { login, error, loading, isAuthenticated, setError } = useContext(AuthContext);
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const location = useLocation();
  
  // 获取重定向URL
  const params = new URLSearchParams(location.search);
  const redirectUrl = params.get('redirect') || '/';
  
  // 如果已经登录，则重定向到首页或指定页面
  useEffect(() => {
    if (isAuthenticated) {
      navigate(redirectUrl);
    }
  }, [isAuthenticated, navigate, redirectUrl]);
  
  // 清除错误信息
  useEffect(() => {
    return () => {
      if (setError) {
        setError(null);
      }
    };
  }, [setError]);
  
  // 处理登录提交
  const handleSubmit = async (values) => {
    const { email, password } = values;
    await login(email, password);
  };
  
  return (
    <div style={{ maxWidth: '400px', margin: '0 auto', padding: '40px 0' }}>
      <Card>
        <Title level={2} className="text-center mb-3">登录</Title>
        
        {error && <Alert message={error} type="error" showIcon className="mb-3" />}
        
        <Form
          form={form}
          name="login"
          onFinish={handleSubmit}
          layout="vertical"
        >
          <Form.Item
            name="email"
            label="邮箱"
            rules={[
              { required: true, message: '请输入邮箱地址' },
              { type: 'email', message: '请输入有效的邮箱地址' }
            ]}
          >
            <Input prefix={<UserOutlined />} placeholder="请输入邮箱地址" />
          </Form.Item>
          
          <Form.Item
            name="password"
            label="密码"
            rules={[{ required: true, message: '请输入密码' }]}
          >
            <Input.Password prefix={<LockOutlined />} placeholder="请输入密码" />
          </Form.Item>
          
          <Form.Item>
            <Link to="/forgot-password" className="float-right">
              忘记密码？
            </Link>
          </Form.Item>
          
          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              block
              loading={loading}
            >
              登录
            </Button>
          </Form.Item>
          
          <div className="text-center">
            <Text>还没有账号？</Text>
            <Link to="/register" style={{ marginLeft: '8px' }}>
              立即注册
            </Link>
          </div>
          
          <Divider>或者使用第三方账号登录</Divider>
          
          <div className="flex-center" style={{ gap: '16px' }}>
            <Button
              icon={<GoogleOutlined />}
              shape="circle"
              size="large"
              disabled
            />
            <Button
              icon={<FacebookOutlined />}
              shape="circle"
              size="large"
              disabled
            />
          </div>
        </Form>
      </Card>
    </div>
  );
};

export default Login; 