import React from 'react';
import { <PERSON>, Button, Typography, Divider, Space } from 'antd';
import { ShoppingOutlined } from '@ant-design/icons';
import { Link } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { formatPrice } from '../../utils/formatUtils';

const { Title, Text } = Typography;

const CartSummary = ({ onCheckout }) => {
  const { cartItems, loading } = useSelector((state) => state.cart);
  const { userInfo } = useSelector((state) => state.auth);

  // 计算商品总数
  const totalItems = cartItems.reduce((acc, item) => acc + item.quantity, 0);

  // 计算商品总价
  const subtotal = cartItems.reduce(
    (acc, item) => acc + item.product.price * item.quantity,
    0
  );

  // 计算运费 (示例：订单满99元免运费，否则10元运费)
  const shipping = subtotal >= 99 ? 0 : 10;

  // 计算总价
  const total = subtotal + shipping;

  // 处理结算
  const handleCheckout = () => {
    if (userInfo) {
      onCheckout && onCheckout();
    } else {
      // 如果未登录，跳转到登录页面
      window.location.href = '/login?redirect=/checkout';
    }
  };

  return (
    <Card className="cart-summary" title="订单摘要" bordered={true}>
      <div className="summary-item">
        <Text>商品数量</Text>
        <Text strong>{totalItems} 件</Text>
      </div>

      <div className="summary-item">
        <Text>商品金额</Text>
        <Text>{formatPrice(subtotal)}</Text>
      </div>

      <div className="summary-item">
        <Text>运费</Text>
        <Text>
          {shipping === 0 ? (
            <span style={{ color: '#52c41a' }}>免运费</span>
          ) : (
            formatPrice(shipping)
          )}
        </Text>
      </div>

      {shipping > 0 && subtotal < 99 && (
        <div className="free-shipping-tip" style={{ color: '#1890ff', fontSize: '12px', marginTop: '8px' }}>
          还差 {formatPrice(99 - subtotal)} 即可享受免运费
        </div>
      )}

      <Divider style={{ margin: '12px 0' }} />

      <div className="summary-item" style={{ fontWeight: 'bold' }}>
        <Text strong>应付总额</Text>
        <Text style={{ fontSize: '18px', color: '#f5222d' }}>{formatPrice(total)}</Text>
      </div>

      <Button
        type="primary"
        size="large"
        block
        style={{ marginTop: '16px' }}
        onClick={handleCheckout}
        disabled={cartItems.length === 0 || loading}
        icon={<ShoppingOutlined />}
      >
        去结算
      </Button>

      <div style={{ marginTop: '16px', textAlign: 'center' }}>
        <Link to="/products">继续购物</Link>
      </div>
    </Card>
  );
};

export default CartSummary;