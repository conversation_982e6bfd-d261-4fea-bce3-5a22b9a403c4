const Order = require('../models/Order');
const Payment = require('../models/Payment');
const { successResponse, errorResponse } = require('../utils/apiResponse');

/**
 * 创建支付意图
 * @route POST /api/payment/create-payment-intent
 * @access Private
 */
exports.createPaymentIntent = async (req, res, next) => {
  try {
    const { orderId } = req.body;

    // 查找订单
    const order = await Order.findById(orderId);

    if (!order) {
      return errorResponse(res, '订单不存在', 404);
    }

    // 验证订单所有者
    if (order.user.toString() !== req.user._id.toString()) {
      return errorResponse(res, '无权访问此订单', 403);
    }

    // 检查订单是否已支付
    if (order.isPaid) {
      return errorResponse(res, '订单已支付', 400);
    }

    // 在实际应用中，这里应该调用支付网关API创建支付意图
    // 例如，使用Stripe、支付宝或微信支付等
    // 为了简化，我们只返回一个模拟的支付意图

    // 创建支付记录
    const payment = await Payment.create({
      order: order._id,
      user: req.user._id,
      paymentMethod: order.paymentMethod,
      amount: order.totalPrice,
      status: 'pending',
    });

    // 模拟支付网关返回的数据
    const paymentIntent = {
      id: `pi_${Date.now()}`,
      amount: order.totalPrice,
      currency: 'cny',
      status: 'requires_payment_method',
      client_secret: `pi_${Date.now()}_secret_${Math.random().toString(36).substring(2, 15)}`,
    };

    return successResponse(
      res,
      {
        paymentIntent,
        paymentId: payment._id,
      },
      '支付意图创建成功'
    );
  } catch (error) {
    next(error);
  }
};

/**
 * 确认支付
 * @route POST /api/payment/confirm
 * @access Private
 */
exports.confirmPayment = async (req, res, next) => {
  try {
    const { paymentId, paymentIntentId } = req.body;

    // 查找支付记录
    const payment = await Payment.findById(paymentId);

    if (!payment) {
      return errorResponse(res, '支付记录不存在', 404);
    }

    // 验证支付所有者
    if (payment.user.toString() !== req.user._id.toString()) {
      return errorResponse(res, '无权访问此支付记录', 403);
    }

    // 查找订单
    const order = await Order.findById(payment.order);

    if (!order) {
      return errorResponse(res, '订单不存在', 404);
    }

    // 在实际应用中，这里应该验证支付网关的支付结果
    // 为了简化，我们直接更新支付状态

    // 更新支付记录
    payment.status = 'completed';
    payment.transactionId = paymentIntentId;
    payment.paymentDetails = {
      paymentIntentId,
      paymentMethod: order.paymentMethod,
      paidAt: new Date(),
    };
    await payment.save();

    // 更新订单状态
    order.isPaid = true;
    order.paidAt = Date.now();
    order.status = 'processing';
    order.paymentResult = {
      id: paymentIntentId,
      status: 'succeeded',
      updateTime: new Date().toISOString(),
      email: req.user.email,
    };
    await order.save();

    return successResponse(res, { order, payment }, '支付成功');
  } catch (error) {
    next(error);
  }
};

/**
 * 获取支付状态
 * @route GET /api/payment/:paymentId
 * @access Private
 */
exports.getPaymentStatus = async (req, res, next) => {
  try {
    const payment = await Payment.findById(req.params.paymentId);

    if (!payment) {
      return errorResponse(res, '支付记录不存在', 404);
    }

    // 验证支付所有者
    if (payment.user.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
      return errorResponse(res, '无权访问此支付记录', 403);
    }

    return successResponse(res, { payment }, '获取支付状态成功');
  } catch (error) {
    next(error);
  }
};

/**
 * 退款
 * @route POST /api/payment/refund
 * @access Private/Admin
 */
exports.refundPayment = async (req, res, next) => {
  try {
    const { orderId, reason } = req.body;

    // 查找订单
    const order = await Order.findById(orderId);

    if (!order) {
      return errorResponse(res, '订单不存在', 404);
    }

    // 检查订单是否已支付
    if (!order.isPaid) {
      return errorResponse(res, '订单未支付，无法退款', 400);
    }

    // 查找支付记录
    const payment = await Payment.findOne({ order: orderId });

    if (!payment) {
      return errorResponse(res, '支付记录不存在', 404);
    }

    // 在实际应用中，这里应该调用支付网关API进行退款
    // 为了简化，我们直接更新支付状态

    // 更新支付记录
    payment.status = 'refunded';
    payment.paymentDetails = {
      ...payment.paymentDetails,
      refundReason: reason,
      refundedAt: new Date(),
    };
    await payment.save();

    // 更新订单状态
    order.status = 'cancelled';
    await order.save();

    // 恢复商品库存
    for (const item of order.items) {
      const product = await Product.findById(item.product);
      if (product) {
        product.stock += item.quantity;
        await product.save();
      }
    }

    return successResponse(res, { order, payment }, '退款成功');
  } catch (error) {
    next(error);
  }
};

/**
 * 获取支付方式列表
 * @route GET /api/payment/methods
 * @access Public
 */
exports.getPaymentMethods = async (req, res) => {
  // 返回支持的支付方式列表
  const paymentMethods = [
    {
      id: 'creditCard',
      name: '信用卡支付',
      description: '使用Visa、Mastercard、JCB等信用卡支付',
      icon: 'credit-card',
    },
    {
      id: 'alipay',
      name: '支付宝',
      description: '使用支付宝扫码支付',
      icon: 'alipay',
    },
    {
      id: 'wechatPay',
      name: '微信支付',
      description: '使用微信扫码支付',
      icon: 'wechat',
    },
    {
      id: 'paypal',
      name: 'PayPal',
      description: '使用PayPal账户支付',
      icon: 'paypal',
    },
  ];

  return successResponse(res, { paymentMethods }, '获取支付方式列表成功');
};