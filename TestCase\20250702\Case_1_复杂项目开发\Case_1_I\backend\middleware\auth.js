const jwt = require('jsonwebtoken');
const User = require('../models/User');

// 验证用户是否已登录
exports.protect = async (req, res, next) => {
  try {
    let token;

    // 检查请求头中是否有token
    if (
      req.headers.authorization &&
      req.headers.authorization.startsWith('Bearer')
    ) {
      token = req.headers.authorization.split(' ')[1];
    }

    // 如果没有token，返回未授权错误
    if (!token) {
      return res.status(401).json({
        success: false,
        message: '您未登录，无法访问此资源',
      });
    }

    // 验证token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // 检查用户是否存在
    const user = await User.findById(decoded.id);
    if (!user) {
      return res.status(401).json({
        success: false,
        message: '此token对应的用户不存在',
      });
    }

    // 将用户信息添加到请求对象中
    req.user = user;
    next();
  } catch (error) {
    return res.status(401).json({
      success: false,
      message: '未授权，请重新登录',
    });
  }
};

// 限制角色访问
exports.restrictTo = (...roles) => {
  return (req, res, next) => {
    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: '您没有权限执行此操作',
      });
    }
    next();
  };
};