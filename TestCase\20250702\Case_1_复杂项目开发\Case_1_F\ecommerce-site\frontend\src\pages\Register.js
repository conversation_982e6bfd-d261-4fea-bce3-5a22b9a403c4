import React, { useState, useEffect, useContext } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Form, Input, Button, Card, Typography, Divider, Alert, Checkbox } from 'antd';
import { UserOutlined, LockOutlined, MailOutlined, PhoneOutlined } from '@ant-design/icons';
import { AuthContext } from '../context/AuthContext';

const { Title, Text, Paragraph } = Typography;

const Register = () => {
  const { register, error, loading, isAuthenticated, setError } = useContext(AuthContext);
  const [form] = Form.useForm();
  const navigate = useNavigate();
  
  // 如果已经登录，则重定向到首页
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/');
    }
  }, [isAuthenticated, navigate]);
  
  // 清除错误信息
  useEffect(() => {
    return () => {
      if (setError) {
        setError(null);
      }
    };
  }, [setError]);
  
  // 处理注册提交
  const handleSubmit = async (values) => {
    const { username, email, password, firstName, lastName, phoneNumber } = values;
    await register({
      username,
      email,
      password,
      firstName,
      lastName,
      phoneNumber
    });
  };
  
  return (
    <div style={{ maxWidth: '500px', margin: '0 auto', padding: '40px 0' }}>
      <Card>
        <Title level={2} className="text-center mb-3">注册账号</Title>
        
        {error && <Alert message={error} type="error" showIcon className="mb-3" />}
        
        <Form
          form={form}
          name="register"
          onFinish={handleSubmit}
          layout="vertical"
        >
          <Form.Item
            name="username"
            label="用户名"
            rules={[
              { required: true, message: '请输入用户名' },
              { min: 3, message: '用户名至少需要3个字符' }
            ]}
          >
            <Input prefix={<UserOutlined />} placeholder="请输入用户名" />
          </Form.Item>
          
          <Form.Item
            name="email"
            label="邮箱"
            rules={[
              { required: true, message: '请输入邮箱地址' },
              { type: 'email', message: '请输入有效的邮箱地址' }
            ]}
          >
            <Input prefix={<MailOutlined />} placeholder="请输入邮箱地址" />
          </Form.Item>
          
          <Form.Item
            name="password"
            label="密码"
            rules={[
              { required: true, message: '请输入密码' },
              { min: 6, message: '密码至少需要6个字符' }
            ]}
            hasFeedback
          >
            <Input.Password prefix={<LockOutlined />} placeholder="请输入密码" />
          </Form.Item>
          
          <Form.Item
            name="confirmPassword"
            label="确认密码"
            dependencies={['password']}
            hasFeedback
            rules={[
              { required: true, message: '请确认密码' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('password') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'));
                },
              }),
            ]}
          >
            <Input.Password prefix={<LockOutlined />} placeholder="请确认密码" />
          </Form.Item>
          
          <Form.Item
            name="firstName"
            label="名"
          >
            <Input placeholder="请输入您的名" />
          </Form.Item>
          
          <Form.Item
            name="lastName"
            label="姓"
          >
            <Input placeholder="请输入您的姓" />
          </Form.Item>
          
          <Form.Item
            name="phoneNumber"
            label="手机号码"
          >
            <Input prefix={<PhoneOutlined />} placeholder="请输入手机号码" />
          </Form.Item>
          
          <Form.Item
            name="agreement"
            valuePropName="checked"
            rules={[
              {
                validator: (_, value) =>
                  value ? Promise.resolve() : Promise.reject(new Error('请阅读并同意用户协议')),
              },
            ]}
          >
            <Checkbox>
              我已阅读并同意 <Link to="/terms">用户协议</Link> 和 <Link to="/privacy">隐私政策</Link>
            </Checkbox>
          </Form.Item>
          
          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              block
              loading={loading}
            >
              注册
            </Button>
          </Form.Item>
          
          <div className="text-center">
            <Text>已有账号？</Text>
            <Link to="/login" style={{ marginLeft: '8px' }}>
              立即登录
            </Link>
          </div>
        </Form>
      </Card>
    </div>
  );
};

export default Register; 