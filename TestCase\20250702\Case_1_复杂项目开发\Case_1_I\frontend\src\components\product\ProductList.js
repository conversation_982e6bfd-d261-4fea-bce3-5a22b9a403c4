import React from 'react';
import { Row, Col, Spin } from 'antd';
import ProductCard from './ProductCard';
import EmptyState from '../common/EmptyState';
import Pagination from '../common/Pagination';

const ProductList = ({
  products = [],
  loading = false,
  error = null,
  wishlist = [],
  onAddToWishlist,
  pagination = null,
  onPageChange,
}) => {
  // 如果正在加载，显示加载中
  if (loading) {
    return (
      <div className="centered-container">
        <Spin size="large" />
      </div>
    );
  }

  // 如果有错误，显示错误信息
  if (error) {
    return (
      <EmptyState
        description={`加载失败: ${error}`}
        buttonText="重试"
        onButtonClick={() => window.location.reload()}
      />
    );
  }

  // 如果没有产品，显示空状态
  if (!products || products.length === 0) {
    return (
      <EmptyState
        description="暂无商品"
        buttonText="返回首页"
        buttonLink="/"
      />
    );
  }

  // 检查产品是否在收藏夹中
  const isInWishlist = (productId) => {
    return wishlist.some((item) => item._id === productId || item === productId);
  };

  return (
    <div className="product-list">
      <Row gutter={[16, 16]}>
        {products.map((product) => (
          <Col xs={24} sm={12} md={8} lg={6} key={product._id}>
            <ProductCard
              product={product}
              onAddToWishlist={onAddToWishlist}
              inWishlist={isInWishlist(product._id)}
            />
          </Col>
        ))}
      </Row>

      {/* 分页 */}
      {pagination && (
        <div className="pagination-wrapper" style={{ marginTop: '24px', textAlign: 'center' }}>
          <Pagination
            current={pagination.current}
            total={pagination.total}
            pageSize={pagination.pageSize}
            onChange={onPageChange}
          />
        </div>
      )}
    </div>
  );
};

export default ProductList;