import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, Button, Typography, Space, Spin, QRCode, message, Result } from 'antd';
import { CheckCircleOutlined, CloseCircleOutlined, ReloadOutlined } from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../store';
import { fetchOrderByNo } from '../store/slices/orderSlice';
import paymentService from '../services/paymentService';

const { Title, Text, Countdown } = Typography;

const Payment: React.FC = () => {
  const { orderNo } = useParams<{ orderNo: string }>();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { currentOrder: order, loading } = useSelector((state: RootState) => state.order);
  
  const [paymentData, setPaymentData] = useState<any>(null);
  const [paymentLoading, setPaymentLoading] = useState(false);
  const [paymentStatus, setPaymentStatus] = useState<'pending' | 'paid' | 'failed' | 'timeout'>('pending');
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');

  useEffect(() => {
    if (orderNo) {
      dispatch(fetchOrderByNo(orderNo) as any);
    }
  }, [dispatch, orderNo]);

  useEffect(() => {
    if (order && order.paymentStatus === 'paid') {
      setPaymentStatus('paid');
    } else if (order && order.status === 'pending') {
      initializePayment();
    }
  }, [order]);

  const initializePayment = async () => {
    if (!order) return;

    try {
      setPaymentLoading(true);
      const response = await paymentService.createPayment(order.orderNo, order.paymentMethod);
      setPaymentData(response.paymentData);

      if (order.paymentMethod === 'alipay') {
        // 支付宝支付直接跳转
        await paymentService.handleAlipayPayment(response.paymentData);
      } else if (order.paymentMethod === 'wechat') {
        // 微信支付显示二维码
        const qrCode = await paymentService.handleWechatPayment(response.paymentData);
        setQrCodeUrl(qrCode);
        startPaymentPolling();
      }
    } catch (error: any) {
      message.error(error.message || '支付初始化失败');
      setPaymentStatus('failed');
    } finally {
      setPaymentLoading(false);
    }
  };

  const startPaymentPolling = () => {
    if (!order) return;

    paymentService.pollPaymentStatus(order.orderNo, 30, 3000)
      .then(() => {
        setPaymentStatus('paid');
        message.success('支付成功！');
        setTimeout(() => {
          navigate('/payment/success', { state: { orderNo: order.orderNo } });
        }, 2000);
      })
      .catch((error) => {
        if (error.message === '支付超时') {
          setPaymentStatus('timeout');
        } else {
          setPaymentStatus('failed');
        }
        message.error(error.message || '支付失败');
      });
  };

  const handleRetryPayment = () => {
    setPaymentStatus('pending');
    setPaymentData(null);
    setQrCodeUrl('');
    initializePayment();
  };

  const handleCancelPayment = () => {
    navigate('/orders');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Spin size="large" />
      </div>
    );
  }

  if (!order) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Result
          status="404"
          title="订单不存在"
          subTitle="请检查订单号是否正确"
          extra={
            <Button type="primary" onClick={() => navigate('/orders')}>
              返回订单列表
            </Button>
          }
        />
      </div>
    );
  }

  if (paymentStatus === 'paid') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Result
          status="success"
          title="支付成功！"
          subTitle={`订单 ${order.orderNo} 已支付成功`}
          extra={[
            <Button type="primary" key="orders" onClick={() => navigate('/orders')}>
              查看订单
            </Button>,
            <Button key="home" onClick={() => navigate('/')}>
              返回首页
            </Button>,
          ]}
        />
      </div>
    );
  }

  if (paymentStatus === 'failed') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Result
          status="error"
          title="支付失败"
          subTitle="支付过程中出现错误，请重试"
          extra={[
            <Button type="primary" key="retry" onClick={handleRetryPayment}>
              重新支付
            </Button>,
            <Button key="cancel" onClick={handleCancelPayment}>
              取消支付
            </Button>,
          ]}
        />
      </div>
    );
  }

  if (paymentStatus === 'timeout') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Result
          status="warning"
          title="支付超时"
          subTitle="支付时间已超时，请重新发起支付"
          extra={[
            <Button type="primary" key="retry" onClick={handleRetryPayment}>
              重新支付
            </Button>,
            <Button key="cancel" onClick={handleCancelPayment}>
              取消支付
            </Button>,
          ]}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <Title level={2} className="text-center mb-8">
            订单支付
          </Title>

          {/* 订单信息 */}
          <Card className="mb-6">
            <Space direction="vertical" size="middle" className="w-full">
              <div className="flex justify-between">
                <Text>订单号:</Text>
                <Text strong>{order.orderNo}</Text>
              </div>
              <div className="flex justify-between">
                <Text>支付金额:</Text>
                <Text strong className="text-red-500 text-xl">
                  ¥{order.finalAmount.toFixed(2)}
                </Text>
              </div>
              <div className="flex justify-between">
                <Text>支付方式:</Text>
                <Text>
                  {order.paymentMethod === 'alipay' && '支付宝'}
                  {order.paymentMethod === 'wechat' && '微信支付'}
                  {order.paymentMethod === 'bank_card' && '银行卡'}
                </Text>
              </div>
            </Space>
          </Card>

          {/* 支付区域 */}
          <Card>
            <Spin spinning={paymentLoading}>
              {order.paymentMethod === 'alipay' && (
                <div className="text-center">
                  <div className="text-6xl mb-4">💰</div>
                  <Title level={3}>支付宝支付</Title>
                  <Text className="text-gray-600 mb-6 block">
                    正在跳转到支付宝支付页面...
                  </Text>
                  <Space>
                    <Button type="primary" onClick={initializePayment}>
                      重新发起支付
                    </Button>
                    <Button onClick={handleCancelPayment}>
                      取消支付
                    </Button>
                  </Space>
                </div>
              )}

              {order.paymentMethod === 'wechat' && (
                <div className="text-center">
                  <div className="text-6xl mb-4">💚</div>
                  <Title level={3}>微信支付</Title>
                  <Text className="text-gray-600 mb-6 block">
                    请使用微信扫描下方二维码完成支付
                  </Text>
                  
                  {qrCodeUrl ? (
                    <div className="flex flex-col items-center">
                      <div className="mb-4">
                        <QRCode
                          value={qrCodeUrl}
                          size={200}
                          status={paymentStatus === 'pending' ? 'active' : 'expired'}
                        />
                      </div>
                      <Text className="text-sm text-gray-500 mb-4">
                        支付完成后页面将自动跳转
                      </Text>
                      <Countdown
                        title="支付剩余时间"
                        value={Date.now() + 15 * 60 * 1000}
                        onFinish={() => setPaymentStatus('timeout')}
                        format="mm:ss"
                      />
                    </div>
                  ) : (
                    <div className="py-8">
                      <Spin size="large" />
                      <div className="mt-4">
                        <Text>正在生成支付二维码...</Text>
                      </div>
                    </div>
                  )}

                  <div className="mt-6">
                    <Space>
                      <Button 
                        icon={<ReloadOutlined />} 
                        onClick={handleRetryPayment}
                      >
                        刷新二维码
                      </Button>
                      <Button onClick={handleCancelPayment}>
                        取消支付
                      </Button>
                    </Space>
                  </div>
                </div>
              )}

              {order.paymentMethod === 'bank_card' && (
                <div className="text-center">
                  <div className="text-6xl mb-4">💳</div>
                  <Title level={3}>银行卡支付</Title>
                  <Text className="text-gray-600 mb-6 block">
                    银行卡支付功能暂未开放
                  </Text>
                  <Space>
                    <Button type="primary" onClick={() => navigate('/checkout')}>
                      选择其他支付方式
                    </Button>
                    <Button onClick={handleCancelPayment}>
                      取消支付
                    </Button>
                  </Space>
                </div>
              )}
            </Spin>
          </Card>

          {/* 支付说明 */}
          <Card className="mt-6" title="支付说明">
            <Space direction="vertical" size="small">
              <Text className="text-sm text-gray-600">
                • 请在15分钟内完成支付，超时订单将自动取消
              </Text>
              <Text className="text-sm text-gray-600">
                • 支付成功后，我们将立即为您处理订单
              </Text>
              <Text className="text-sm text-gray-600">
                • 如遇支付问题，请联系客服：400-123-4567
              </Text>
            </Space>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Payment;
