# 数据库配置
MONGODB_URI=mongodb://localhost:27017/ecommerce
DB_NAME=ecommerce

# JWT配置
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRE=7d

# 服务器配置
PORT=3001
NODE_ENV=development

# 支付配置
ALIPAY_APP_ID=your_alipay_app_id
ALIPAY_PRIVATE_KEY=your_alipay_private_key
ALIPAY_PUBLIC_KEY=your_alipay_public_key
ALIPAY_GATEWAY=https://openapi.alipaydev.com/gateway.do

WECHAT_APP_ID=your_wechat_app_id
WECHAT_MCH_ID=your_wechat_mch_id
WECHAT_API_KEY=your_wechat_api_key

# 文件上传配置
UPLOAD_PATH=uploads/
MAX_FILE_SIZE=5242880

# 邮件配置
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_email_password
