import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, Tabs, Button, Typography, Space, Empty, Spin, Tag, Image, Pagination } from 'antd';
import { EyeOutlined, PayCircleOutlined, CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../store';
import { fetchOrders, cancelOrder, confirmOrder } from '../store/slices/orderSlice';
import { Order } from '../store/slices/orderSlice';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

const Orders: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { orders, loading, pagination } = useSelector((state: RootState) => state.order);
  
  const [activeTab, setActiveTab] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);

  useEffect(() => {
    loadOrders();
  }, [activeTab, currentPage]);

  const loadOrders = () => {
    const params: any = {
      page: currentPage,
      limit: 10,
    };

    if (activeTab !== 'all') {
      params.status = activeTab;
    }

    dispatch(fetchOrders(params) as any);
  };

  const handleTabChange = (key: string) => {
    setActiveTab(key);
    setCurrentPage(1);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleViewOrder = (orderNo: string) => {
    navigate(`/orders/${orderNo}`);
  };

  const handlePayOrder = (orderNo: string) => {
    navigate(`/payment/${orderNo}`);
  };

  const handleCancelOrder = async (orderNo: string) => {
    try {
      await dispatch(cancelOrder(orderNo) as any).unwrap();
      loadOrders(); // 重新加载订单列表
    } catch (error) {
      // 错误已在slice中处理
    }
  };

  const handleConfirmOrder = async (orderNo: string) => {
    try {
      await dispatch(confirmOrder(orderNo) as any).unwrap();
      loadOrders(); // 重新加载订单列表
    } catch (error) {
      // 错误已在slice中处理
    }
  };

  const getStatusTag = (status: string) => {
    const statusConfig = {
      pending: { color: 'orange', text: '待付款' },
      paid: { color: 'blue', text: '已付款' },
      shipped: { color: 'cyan', text: '已发货' },
      delivered: { color: 'purple', text: '已送达' },
      completed: { color: 'green', text: '已完成' },
      cancelled: { color: 'red', text: '已取消' },
      refunded: { color: 'magenta', text: '已退款' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const getOrderActions = (order: Order) => {
    const actions = [];

    // 查看详情
    actions.push(
      <Button
        key="view"
        type="text"
        icon={<EyeOutlined />}
        onClick={() => handleViewOrder(order.orderNo)}
      >
        查看详情
      </Button>
    );

    // 根据订单状态显示不同操作
    switch (order.status) {
      case 'pending':
        actions.push(
          <Button
            key="pay"
            type="primary"
            size="small"
            icon={<PayCircleOutlined />}
            onClick={() => handlePayOrder(order.orderNo)}
          >
            立即付款
          </Button>
        );
        actions.push(
          <Button
            key="cancel"
            danger
            size="small"
            icon={<CloseCircleOutlined />}
            onClick={() => handleCancelOrder(order.orderNo)}
          >
            取消订单
          </Button>
        );
        break;
      case 'shipped':
        actions.push(
          <Button
            key="confirm"
            type="primary"
            size="small"
            icon={<CheckCircleOutlined />}
            onClick={() => handleConfirmOrder(order.orderNo)}
          >
            确认收货
          </Button>
        );
        break;
    }

    return actions;
  };

  const renderOrderCard = (order: Order) => (
    <Card key={order._id} className="mb-4">
      {/* 订单头部 */}
      <div className="flex items-center justify-between mb-4 pb-4 border-b">
        <Space>
          <Text strong>订单号: {order.orderNo}</Text>
          {getStatusTag(order.status)}
        </Space>
        <Text className="text-gray-500">
          {dayjs(order.createdAt).format('YYYY-MM-DD HH:mm:ss')}
        </Text>
      </div>

      {/* 商品列表 */}
      <div className="mb-4">
        {order.items.map((item, index) => (
          <div key={index} className="flex items-center space-x-4 mb-3">
            <Image
              src={item.image}
              alt={item.name}
              width={60}
              height={60}
              className="rounded object-cover"
              preview={false}
            />
            <div className="flex-1">
              <Text strong className="block">{item.name}</Text>
              {item.specs.length > 0 && (
                <div className="text-sm text-gray-500">
                  {item.specs.map((spec, i) => (
                    <span key={i} className="mr-2">
                      {spec.name}: {spec.value}
                    </span>
                  ))}
                </div>
              )}
              <div className="text-sm text-gray-500">
                ¥{item.price} × {item.quantity}
              </div>
            </div>
            <div className="text-right">
              <Text strong>¥{item.subtotal.toFixed(2)}</Text>
            </div>
          </div>
        ))}
      </div>

      {/* 订单底部 */}
      <div className="flex items-center justify-between pt-4 border-t">
        <div>
          <Text>
            共 {order.items.length} 件商品，总计: 
            <span className="text-red-500 font-bold ml-1">
              ¥{order.finalAmount.toFixed(2)}
            </span>
          </Text>
        </div>
        <Space>
          {getOrderActions(order)}
        </Space>
      </div>
    </Card>
  );

  const tabItems = [
    { key: 'all', label: '全部订单' },
    { key: 'pending', label: '待付款' },
    { key: 'paid', label: '已付款' },
    { key: 'shipped', label: '已发货' },
    { key: 'delivered', label: '已送达' },
    { key: 'completed', label: '已完成' },
    { key: 'cancelled', label: '已取消' },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <Title level={2} className="mb-6">我的订单</Title>

        <Card>
          <Tabs
            activeKey={activeTab}
            onChange={handleTabChange}
            items={tabItems}
          />

          <Spin spinning={loading}>
            {orders.length > 0 ? (
              <>
                <div className="mt-6">
                  {orders.map(renderOrderCard)}
                </div>

                {/* 分页 */}
                <div className="flex justify-center mt-6">
                  <Pagination
                    current={pagination.current}
                    pageSize={pagination.pageSize}
                    total={pagination.total}
                    onChange={handlePageChange}
                    showSizeChanger={false}
                    showQuickJumper
                    showTotal={(total, range) =>
                      `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                    }
                  />
                </div>
              </>
            ) : (
              <Empty
                description="暂无订单"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              >
                <Button type="primary" onClick={() => navigate('/products')}>
                  去购物
                </Button>
              </Empty>
            )}
          </Spin>
        </Card>
      </div>
    </div>
  );
};

export default Orders;
