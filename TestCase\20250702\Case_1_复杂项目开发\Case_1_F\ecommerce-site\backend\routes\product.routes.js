const express = require('express');
const router = express.Router();
const { check } = require('express-validator');
const {
  getProducts,
  getProduct,
  createProduct,
  updateProduct,
  deleteProduct,
  getFeaturedProducts
} = require('../controllers/product.controller');
const { protect, authorize } = require('../middleware/auth.middleware');

// 获取精选商品路由必须在获取单个商品路由之前定义
router.get('/featured', getFeaturedProducts);

// 公开路由
router.get('/', getProducts);
router.get('/:id', getProduct);

// 受保护的路由 - 仅限管理员访问
router.post(
  '/',
  [
    protect, 
    authorize('admin'),
    check('name', '请提供商品名称').not().isEmpty(),
    check('description', '请提供商品描述').not().isEmpty(),
    check('price', '请提供有效的商品价格').isNumeric(),
    check('category', '请选择商品分类').not().isEmpty(),
    check('stock', '请提供商品库存').isNumeric()
  ],
  createProduct
);

router.put('/:id', protect, authorize('admin'), updateProduct);
router.delete('/:id', protect, authorize('admin'), deleteProduct);

module.exports = router; 