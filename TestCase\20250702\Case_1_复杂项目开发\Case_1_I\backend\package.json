{"name": "ecommerce-backend", "version": "1.0.0", "description": "电商网站后端", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.0", "mongoose": "^7.0.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1"}, "devDependencies": {"nodemon": "^2.0.22"}}