import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { Layout } from 'antd';

// 导入布局组件
import Header from './components/layout/Header';
import Footer from './components/layout/Footer';

// 导入页面组件
import Home from './pages/Home';
import ProductList from './pages/ProductList';
import ProductDetail from './pages/ProductDetail';
import Cart from './pages/Cart';
import Checkout from './pages/Checkout';
import Login from './pages/Login';
import Register from './pages/Register';
import UserProfile from './pages/UserProfile';
import Orders from './pages/Orders';
import OrderDetail from './pages/OrderDetail';
import NotFound from './pages/NotFound';

// 导入上下文
import { AuthProvider } from './context/AuthContext';
import { CartProvider } from './context/CartContext';

// 导入私有路由组件
import PrivateRoute from './components/common/PrivateRoute';

const { Content } = Layout;

function App() {
  return (
    <AuthProvider>
      <CartProvider>
        <Layout>
          <Header />
          <Content>
            <div className="container">
              <Routes>
                <Route path="/" element={<Home />} />
                <Route path="/products" element={<ProductList />} />
                <Route path="/products/:id" element={<ProductDetail />} />
                <Route path="/cart" element={<Cart />} />
                <Route path="/login" element={<Login />} />
                <Route path="/register" element={<Register />} />
                
                <Route
                  path="/checkout"
                  element={
                    <PrivateRoute>
                      <Checkout />
                    </PrivateRoute>
                  }
                />
                <Route
                  path="/profile"
                  element={
                    <PrivateRoute>
                      <UserProfile />
                    </PrivateRoute>
                  }
                />
                <Route
                  path="/orders"
                  element={
                    <PrivateRoute>
                      <Orders />
                    </PrivateRoute>
                  }
                />
                <Route
                  path="/orders/:id"
                  element={
                    <PrivateRoute>
                      <OrderDetail />
                    </PrivateRoute>
                  }
                />
                <Route path="*" element={<NotFound />} />
              </Routes>
            </div>
          </Content>
          <Footer />
        </Layout>
      </CartProvider>
    </AuthProvider>
  );
}

export default App; 