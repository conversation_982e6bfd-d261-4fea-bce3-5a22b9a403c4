import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Typography,
  Tag,
  Popconfirm,
  message,
  Avatar,
  Tooltip,
} from 'antd';
import {
  EditOutlined,
  DeleteOutlined,
  LockOutlined,
  UnlockOutlined,
  ExclamationCircleOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { useDispatch } from 'react-redux';
import { formatDate } from '../../utils/formatUtils';

const { Title } = Typography;
const { Option } = Select;

const UserManagement = () => {
  const dispatch = useDispatch();
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingUser, setEditingUser] = useState(null);
  const [form] = Form.useForm();
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 获取用户列表
  const fetchUsers = async (page = 1, pageSize = 10) => {
    setLoading(true);
    try {
      // 这里应该调用实际的API
      // const response = await getUsers({ page, pageSize });
      // setUsers(response.users);
      // setPagination({
      //   current: response.page,
      //   pageSize: response.pageSize,
      //   total: response.total,
      // });
      
      // 模拟数据
      setTimeout(() => {
        const mockUsers = Array.from({ length: 20 }, (_, i) => ({
          _id: `user-${i}`,
          name: `用户${i}`,
          email: `user${i}@example.com`,
          phone: `1381234${i.toString().padStart(4, '0')}`,
          isAdmin: i % 10 === 0,
          isActive: i % 5 !== 0,
          createdAt: new Date(Date.now() - i * 86400000).toISOString(),
          avatar: i % 3 === 0 ? `https://randomuser.me/api/portraits/${i % 2 ? 'women' : 'men'}/${i % 10}.jpg` : null,
        }));
        
        setUsers(mockUsers);
        setPagination({
          current: page,
          pageSize: pageSize,
          total: 100,
        });
        setLoading(false);
      }, 500);
    } catch (error) {
      message.error('获取用户列表失败');
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  // 处理表格分页变化
  const handleTableChange = (pagination) => {
    fetchUsers(pagination.current, pagination.pageSize);
  };

  // 打开编辑用户模态框
  const showEditModal = (user) => {
    setEditingUser(user);
    form.setFieldsValue({
      name: user.name,
      email: user.email,
      phone: user.phone,
      isAdmin: user.isAdmin ? 'true' : 'false',
      isActive: user.isActive ? 'true' : 'false',
    });
    setModalVisible(true);
  };

  // 关闭模态框
  const handleCancel = () => {
    setModalVisible(false);
    setEditingUser(null);
    form.resetFields();
  };

  // 处理表单提交
  const handleSubmit = async (values) => {
    try {
      // 转换布尔值
      const userData = {
        ...values,
        isAdmin: values.isAdmin === 'true',
        isActive: values.isActive === 'true',
      };

      // 这里应该调用实际的API
      // await updateUser(editingUser._id, userData);
      
      // 模拟更新
      setUsers(users.map(user => 
        user._id === editingUser._id ? { ...user, ...userData } : user
      ));
      
      message.success('用户信息更新成功');
      setModalVisible(false);
    } catch (error) {
      message.error(`更新失败: ${error.message}`);
    }
  };

  // 处理删除用户
  const handleDelete = async (id) => {
    try {
      // 这里应该调用实际的API
      // await deleteUser(id);
      
      // 模拟删除
      setUsers(users.filter(user => user._id !== id));
      
      message.success('用户删除成功');
    } catch (error) {
      message.error(`删除失败: ${error.message}`);
    }
  };

  // 处理激活/禁用用户
  const handleToggleActive = async (user) => {
    try {
      const newStatus = !user.isActive;
      
      // 这里应该调用实际的API
      // await updateUserStatus(user._id, { isActive: newStatus });
      
      // 模拟更新
      setUsers(users.map(u => 
        u._id === user._id ? { ...u, isActive: newStatus } : u
      ));
      
      message.success(`用户已${newStatus ? '激活' : '禁用'}`);
    } catch (error) {
      message.error(`操作失败: ${error.message}`);
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '用户',
      key: 'user',
      render: (_, record) => (
        <Space>
          <Avatar 
            src={record.avatar} 
            icon={!record.avatar && <UserOutlined />}
            size="large"
          />
          <div>
            <div>{record.name}</div>
            <div style={{ fontSize: '12px', color: '#999' }}>{record.email}</div>
          </div>
        </Space>
      ),
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      key: 'phone',
    },
    {
      title: '角色',
      key: 'role',
      render: (_, record) => (
        <Tag color={record.isAdmin ? 'gold' : 'blue'}>
          {record.isAdmin ? '管理员' : '普通用户'}
        </Tag>
      ),
    },
    {
      title: '状态',
      key: 'status',
      render: (_, record) => (
        <Tag color={record.isActive ? 'green' : 'red'}>
          {record.isActive ? '已激活' : '已禁用'}
        </Tag>
      ),
    },
    {
      title: '注册时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => formatDate(date),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="primary"
            icon={<EditOutlined />}
            size="small"
            onClick={() => showEditModal(record)}
          >
            编辑
          </Button>
          
          <Tooltip title={record.isActive ? '禁用用户' : '激活用户'}>
            <Button
              type={record.isActive ? 'default' : 'primary'}
              icon={record.isActive ? <LockOutlined /> : <UnlockOutlined />}
              size="small"
              onClick={() => handleToggleActive(record)}
            >
              {record.isActive ? '禁用' : '激活'}
            </Button>
          </Tooltip>
          
          <Popconfirm
            title="确定要删除此用户吗？"
            description="此操作不可逆，用户的所有数据将被删除。"
            onConfirm={() => handleDelete(record._id)}
            okText="确定"
            cancelText="取消"
            icon={<ExclamationCircleOutlined style={{ color: 'red' }} />}
          >
            <Button
              type="primary"
              danger
              icon={<DeleteOutlined />}
              size="small"
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="user-management">
      <div className="page-header" style={{ marginBottom: '16px' }}>
        <Title level={2}>用户管理</Title>
      </div>

      <Table
        columns={columns}
        dataSource={users}
        rowKey="_id"
        pagination={pagination}
        loading={loading}
        onChange={handleTableChange}
      />

      <Modal
        title="编辑用户"
        open={modalVisible}
        onCancel={handleCancel}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="name"
            label="用户名"
            rules={[{ required: true, message: '请输入用户名' }]}
          >
            <Input placeholder="请输入用户名" />
          </Form.Item>

          <Form.Item
            name="email"
            label="邮箱"
            rules={[
              { required: true, message: '请输入邮箱' },
              { type: 'email', message: '请输入有效的邮箱地址' },
            ]}
          >
            <Input placeholder="请输入邮箱" disabled />
          </Form.Item>

          <Form.Item
            name="phone"
            label="手机号"
            rules={[
              { required: true, message: '请输入手机号' },
              { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号' },
            ]}
          >
            <Input placeholder="请输入手机号" />
          </Form.Item>

          <Form.Item
            name="isAdmin"
            label="用户角色"
            rules={[{ required: true, message: '请选择用户角色' }]}
          >
            <Select placeholder="请选择用户角色">
              <Option value="false">普通用户</Option>
              <Option value="true">管理员</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="isActive"
            label="账号状态"
            rules={[{ required: true, message: '请选择账号状态' }]}
          >
            <Select placeholder="请选择账号状态">
              <Option value="true">已激活</Option>
              <Option value="false">已禁用</Option>
            </Select>
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit">
              更新用户
            </Button>
            <Button style={{ marginLeft: 8 }} onClick={handleCancel}>
              取消
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default UserManagement;