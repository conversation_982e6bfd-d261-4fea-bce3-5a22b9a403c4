const express = require('express');
const router = express.Router();
const {
  createPaymentSession,
  simulatePayment,
  getPaymentMethods
} = require('../controllers/payment.controller');
const { protect } = require('../middleware/auth.middleware');

// 公开路由
router.get('/methods', getPaymentMethods);

// 受保护的路由
router.use(protect);
router.post('/create-payment-session', createPaymentSession);
router.post('/simulate-payment', simulatePayment);

module.exports = router; 