import apiClient from './apiClient';

// 用户登录
const login = async (email, password) => {
  const response = await apiClient.post('/auth/login', { email, password });
  if (response.data) {
    localStorage.setItem('userInfo', JSON.stringify(response.data));
    localStorage.setItem('token', response.data.token);
  }
  return response.data;
};

// 用户注册
const register = async (name, email, password) => {
  const response = await apiClient.post('/auth/register', {
    name,
    email,
    password,
  });
  if (response.data) {
    localStorage.setItem('userInfo', JSON.stringify(response.data));
    localStorage.setItem('token', response.data.token);
  }
  return response.data;
};

// 用户登出
const logout = () => {
  localStorage.removeItem('userInfo');
  localStorage.removeItem('token');
  // 调用后端登出接口（如果需要）
  return apiClient.post('/auth/logout');
};

// 获取用户信息
const getUserProfile = async (token) => {
  const response = await apiClient.get('/auth/me', {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

// 更新用户信息
const updateUserProfile = async (userData, token) => {
  const response = await apiClient.put('/users/profile', userData, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

// 更新用户密码
const updatePassword = async (currentPassword, newPassword, token) => {
  const response = await apiClient.put(
    '/auth/change-password',
    { currentPassword, newPassword },
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    }
  );
  return response.data;
};

// 忘记密码
const forgotPassword = async (email) => {
  const response = await apiClient.post('/auth/forgot-password', { email });
  return response.data;
};

// 重置密码
const resetPassword = async (token, password) => {
  const response = await apiClient.post('/auth/reset-password', {
    token,
    password,
  });
  return response.data;
};

const authService = {
  login,
  register,
  logout,
  getUserProfile,
  updateUserProfile,
  updatePassword,
  forgotPassword,
  resetPassword,
};

export default authService;