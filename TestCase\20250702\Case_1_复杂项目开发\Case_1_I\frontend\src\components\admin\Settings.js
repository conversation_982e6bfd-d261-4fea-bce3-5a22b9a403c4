import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Button,
  Card,
  Tabs,
  Typography,
  Switch,
  InputNumber,
  Select,
  Upload,
  message,
  Divider,
  Space,
  Row,
  Col,
  Spin,
} from 'antd';
import {
  SaveOutlined,
  UploadOutlined,
  PlusOutlined,
  DeleteOutlined,
  ShopOutlined,
  DollarOutlined,
  MailOutlined,
  GlobalOutlined,
} from '@ant-design/icons';
import { useDispatch } from 'react-redux';
import { updateSettings, getSettings } from '../../services/settingsService';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { TextArea } = Input;
const { Option } = Select;

const Settings = () => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [generalForm] = Form.useForm();
  const [paymentForm] = Form.useForm();
  const [shippingForm] = Form.useForm();
  const [emailForm] = Form.useForm();
  const [logoFile, setLogoFile] = useState(null);
  const [faviconFile, setFaviconFile] = useState(null);

  // 获取设置
  const fetchSettings = async () => {
    setLoading(true);
    try {
      // 这里应该调用实际的API
      // const settings = await getSettings();
      
      // 模拟数据
      setTimeout(() => {
        const mockSettings = {
          general: {
            siteName: '电子商城',
            siteDescription: '提供优质电子产品的在线商城',
            logo: 'https://example.com/logo.png',
            favicon: 'https://example.com/favicon.ico',
            contactEmail: '<EMAIL>',
            contactPhone: '************',
            address: '上海市浦东新区张江高科技园区',
            socialLinks: {
              weibo: 'https://weibo.com/example',
              wechat: 'example_wechat',
              facebook: 'https://facebook.com/example',
              twitter: 'https://twitter.com/example',
            },
          },
          payment: {
            currency: 'CNY',
            currencySymbol: '¥',
            alipayEnabled: true,
            alipayConfig: {
              appId: 'test_app_id',
              privateKey: '******',
              publicKey: '******',
            },
            wechatPayEnabled: true,
            wechatPayConfig: {
              appId: 'test_app_id',
              mchId: 'test_mch_id',
              apiKey: '******',
            },
            creditCardEnabled: false,
            creditCardConfig: {
              stripePublicKey: '',
              stripeSecretKey: '',
            },
          },
          shipping: {
            freeShippingThreshold: 99,
            defaultShippingFee: 10,
            enableInternationalShipping: false,
            internationalShippingFee: 50,
            shippingMethods: [
              { id: 'standard', name: '标准配送', fee: 10, days: '3-5' },
              { id: 'express', name: '快速配送', fee: 20, days: '1-2' },
            ],
          },
          email: {
            smtpHost: 'smtp.example.com',
            smtpPort: 587,
            smtpUser: '<EMAIL>',
            smtpPassword: '******',
            fromEmail: '<EMAIL>',
            fromName: '电子商城',
            enableOrderConfirmation: true,
            enableShippingNotification: true,
            enablePasswordReset: true,
          },
        };
        
        // 设置表单初始值
        generalForm.setFieldsValue({
          ...mockSettings.general,
          weibo: mockSettings.general.socialLinks.weibo,
          wechat: mockSettings.general.socialLinks.wechat,
          facebook: mockSettings.general.socialLinks.facebook,
          twitter: mockSettings.general.socialLinks.twitter,
        });
        
        paymentForm.setFieldsValue({
          ...mockSettings.payment,
          alipayAppId: mockSettings.payment.alipayConfig.appId,
          alipayPrivateKey: mockSettings.payment.alipayConfig.privateKey,
          alipayPublicKey: mockSettings.payment.alipayConfig.publicKey,
          wechatPayAppId: mockSettings.payment.wechatPayConfig.appId,
          wechatPayMchId: mockSettings.payment.wechatPayConfig.mchId,
          wechatPayApiKey: mockSettings.payment.wechatPayConfig.apiKey,
          stripePublicKey: mockSettings.payment.creditCardConfig.stripePublicKey,
          stripeSecretKey: mockSettings.payment.creditCardConfig.stripeSecretKey,
        });
        
        shippingForm.setFieldsValue({
          ...mockSettings.shipping,
          shippingMethods: mockSettings.shipping.shippingMethods,
        });
        
        emailForm.setFieldsValue({
          ...mockSettings.email,
        });
        
        setLoading(false);
      }, 1000);
    } catch (error) {
      message.error('获取设置失败');
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSettings();
  }, []);

  // 处理一般设置提交
  const handleGeneralSubmit = async (values) => {
    try {
      const formData = new FormData();
      
      // 添加基本字段
      Object.keys(values).forEach(key => {
        if (key !== 'logo' && key !== 'favicon') {
          formData.append(key, values[key]);
        }
      });
      
      // 添加社交链接
      const socialLinks = {
        weibo: values.weibo,
        wechat: values.wechat,
        facebook: values.facebook,
        twitter: values.twitter,
      };
      formData.append('socialLinks', JSON.stringify(socialLinks));
      
      // 添加文件
      if (logoFile) {
        formData.append('logo', logoFile);
      }
      
      if (faviconFile) {
        formData.append('favicon', faviconFile);
      }
      
      // 这里应该调用实际的API
      // await updateSettings('general', formData);
      
      message.success('一般设置已更新');
    } catch (error) {
      message.error(`更新失败: ${error.message}`);
    }
  };

  // 处理支付设置提交
  const handlePaymentSubmit = async (values) => {
    try {
      // 重组支付配置
      const paymentData = {
        currency: values.currency,
        currencySymbol: values.currencySymbol,
        alipayEnabled: values.alipayEnabled,
        alipayConfig: {
          appId: values.alipayAppId,
          privateKey: values.alipayPrivateKey,
          publicKey: values.alipayPublicKey,
        },
        wechatPayEnabled: values.wechatPayEnabled,
        wechatPayConfig: {
          appId: values.wechatPayAppId,
          mchId: values.wechatPayMchId,
          apiKey: values.wechatPayApiKey,
        },
        creditCardEnabled: values.creditCardEnabled,
        creditCardConfig: {
          stripePublicKey: values.stripePublicKey,
          stripeSecretKey: values.stripeSecretKey,
        },
      };
      
      // 这里应该调用实际的API
      // await updateSettings('payment', paymentData);
      
      message.success('支付设置已更新');
    } catch (error) {
      message.error(`更新失败: ${error.message}`);
    }
  };

  // 处理配送设置提交
  const handleShippingSubmit = async (values) => {
    try {
      // 这里应该调用实际的API
      // await updateSettings('shipping', values);
      
      message.success('配送设置已更新');
    } catch (error) {
      message.error(`更新失败: ${error.message}`);
    }
  };

  // 处理邮件设置提交
  const handleEmailSubmit = async (values) => {
    try {
      // 这里应该调用实际的API
      // await updateSettings('email', values);
      
      message.success('邮件设置已更新');
    } catch (error) {
      message.error(`更新失败: ${error.message}`);
    }
  };

  // 自定义上传组件
  const customUpload = ({ file, onSuccess }) => {
    // 模拟上传成功
    setTimeout(() => {
      onSuccess('ok');
    }, 500);
  };

  // 处理Logo上传
  const handleLogoChange = (info) => {
    if (info.file.status === 'done') {
      setLogoFile(info.file.originFileObj);
      message.success(`${info.file.name} 上传成功`);
    } else if (info.file.status === 'error') {
      message.error(`${info.file.name} 上传失败`);
    }
  };

  // 处理Favicon上传
  const handleFaviconChange = (info) => {
    if (info.file.status === 'done') {
      setFaviconFile(info.file.originFileObj);
      message.success(`${info.file.name} 上传成功`);
    } else if (info.file.status === 'error') {
      message.error(`${info.file.name} 上传失败`);
    }
  };

  return (
    <div className="settings">
      <div className="page-header" style={{ marginBottom: '16px' }}>
        <Title level={2}>系统设置</Title>
      </div>

      <Spin spinning={loading}>
        <Tabs defaultActiveKey="1">
          <TabPane
            tab={
              <span>
                <ShopOutlined />
                一般设置
              </span>
            }
            key="1"
          >
            <Card>
              <Form
                form={generalForm}
                layout="vertical"
                onFinish={handleGeneralSubmit}
              >
                <Title level={4}>基本信息</Title>
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name="siteName"
                      label="网站名称"
                      rules={[{ required: true, message: '请输入网站名称' }]}
                    >
                      <Input placeholder="请输入网站名称" />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="contactEmail"
                      label="联系邮箱"
                      rules={[
                        { required: true, message: '请输入联系邮箱' },
                        { type: 'email', message: '请输入有效的邮箱地址' },
                      ]}
                    >
                      <Input placeholder="请输入联系邮箱" />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name="contactPhone"
                      label="联系电话"
                    >
                      <Input placeholder="请输入联系电话" />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="address"
                      label="公司地址"
                    >
                      <Input placeholder="请输入公司地址" />
                    </Form.Item>
                  </Col>
                </Row>

                <Form.Item
                  name="siteDescription"
                  label="网站描述"
                >
                  <TextArea
                    rows={4}
                    placeholder="请输入网站描述"
                  />
                </Form.Item>

                <Divider />
                <Title level={4}>网站Logo和图标</Title>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name="logo"
                      label="网站Logo"
                      valuePropName="fileList"
                      getValueFromEvent={(e) => {
                        if (Array.isArray(e)) {
                          return e;
                        }
                        return e && e.fileList;
                      }}
                    >
                      <Upload
                        name="logo"
                        listType="picture"
                        maxCount={1}
                        customRequest={customUpload}
                        onChange={handleLogoChange}
                        accept="image/*"
                      >
                        <Button icon={<UploadOutlined />}>上传Logo</Button>
                        <Text type="secondary" style={{ marginLeft: 8 }}>
                          建议尺寸: 200x50px
                        </Text>
                      </Upload>
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="favicon"
                      label="网站图标"
                      valuePropName="fileList"
                      getValueFromEvent={(e) => {
                        if (Array.isArray(e)) {
                          return e;
                        }
                        return e && e.fileList;
                      }}
                    >
                      <Upload
                        name="favicon"
                        listType="picture"
                        maxCount={1}
                        customRequest={customUpload}
                        onChange={handleFaviconChange}
                        accept="image/x-icon,image/png"
                      >
                        <Button icon={<UploadOutlined />}>上传图标</Button>
                        <Text type="secondary" style={{ marginLeft: 8 }}>
                          建议尺寸: 32x32px
                        </Text>
                      </Upload>
                    </Form.Item>
                  </Col>
                </Row>

                <Divider />
                <Title level={4}>社交媒体链接</Title>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name="weibo"
                      label="微博"
                    >
                      <Input placeholder="请输入微博链接" />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="wechat"
                      label="微信公众号"
                    >
                      <Input placeholder="请输入微信公众号ID" />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name="facebook"
                      label="Facebook"
                    >
                      <Input placeholder="请输入Facebook链接" />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="twitter"
                      label="Twitter"
                    >
                      <Input placeholder="请输入Twitter链接" />
                    </Form.Item>
                  </Col>
                </Row>

                <Form.Item>
                  <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
                    保存设置
                  </Button>
                </Form.Item>
              </Form>
            </Card>
          </TabPane>

          <TabPane
            tab={
              <span>
                <DollarOutlined />
                支付设置
              </span>
            }
            key="2"
          >
            <Card>
              <Form
                form={paymentForm}
                layout="vertical"
                onFinish={handlePaymentSubmit}
              >
                <Title level={4}>货币设置</Title>
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name="currency"
                      label="货币"
                      rules={[{ required: true, message: '请选择货币' }]}
                    >
                      <Select placeholder="请选择货币">
                        <Option value="CNY">人民币 (CNY)</Option>
                        <Option value="USD">美元 (USD)</Option>
                        <Option value="EUR">欧元 (EUR)</Option>
                        <Option value="GBP">英镑 (GBP)</Option>
                        <Option value="JPY">日元 (JPY)</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="currencySymbol"
                      label="货币符号"
                      rules={[{ required: true, message: '请输入货币符号' }]}
                    >
                      <Input placeholder="请输入货币符号" />
                    </Form.Item>
                  </Col>
                </Row>

                <Divider />
                <Title level={4}>支付宝设置</Title>

                <Form.Item
                  name="alipayEnabled"
                  label="启用支付宝"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>

                <Form.Item
                  noStyle
                  shouldUpdate={(prevValues, currentValues) => prevValues.alipayEnabled !== currentValues.alipayEnabled}
                >
                  {({ getFieldValue }) => (
                    getFieldValue('alipayEnabled') ? (
                      <>
                        <Row gutter={16}>
                          <Col span={24}>
                            <Form.Item
                              name="alipayAppId"
                              label="支付宝应用ID"
                              rules={[{ required: true, message: '请输入支付宝应用ID' }]}
                            >
                              <Input placeholder="请输入支付宝应用ID" />
                            </Form.Item>
                          </Col>
                        </Row>
                        <Row gutter={16}>
                          <Col span={12}>
                            <Form.Item
                              name="alipayPrivateKey"
                              label="支付宝私钥"
                              rules={[{ required: true, message: '请输入支付宝私钥' }]}
                            >
                              <TextArea
                                rows={4}
                                placeholder="请输入支付宝私钥"
                              />
                            </Form.Item>
                          </Col>
                          <Col span={12}>
                            <Form.Item
                              name="alipayPublicKey"
                              label="支付宝公钥"
                              rules={[{ required: true, message: '请输入支付宝公钥' }]}
                            >
                              <TextArea
                                rows={4}
                                placeholder="请输入支付宝公钥"
                              />
                            </Form.Item>
                          </Col>
                        </Row>
                      </>
                    ) : null
                  )}
                </Form.Item>

                <Divider />
                <Title level={4}>微信支付设置</Title>

                <Form.Item
                  name="wechatPayEnabled"
                  label="启用微信支付"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>

                <Form.Item
                  noStyle
                  shouldUpdate={(prevValues, currentValues) => prevValues.wechatPayEnabled !== currentValues.wechatPayEnabled}
                >
                  {({ getFieldValue }) => (
                    getFieldValue('wechatPayEnabled') ? (
                      <>
                        <Row gutter={16}>
                          <Col span={12}>
                            <Form.Item
                              name="wechatPayAppId"
                              label="微信支付应用ID"
                              rules={[{ required: true, message: '请输入微信支付应用ID' }]}
                            >
                              <Input placeholder="请输入微信支付应用ID" />
                            </Form.Item>
                          </Col>
                          <Col span={12}>
                            <Form.Item
                              name="wechatPayMchId"
                              label="微信支付商户号"
                              rules={[{ required: true, message: '请输入微信支付商户号' }]}
                            >
                              <Input placeholder="请输入微信支付商户号" />
                            </Form.Item>
                          </Col>
                        </Row>
                        <Row gutter={16}>
                          <Col span={24}>
                            <Form.Item
                              name="wechatPayApiKey"
                              label="微信支付API密钥"
                              rules={[{ required: true, message: '请输入微信支付API密钥' }]}
                            >
                              <Input.Password placeholder="请输入微信支付API密钥" />
                            </Form.Item>
                          </Col>
                        </Row>
                      </>
                    ) : null
                  )}
                </Form.Item>

                <Divider />
                <Title level={4}>信用卡支付设置</Title>

                <Form.Item
                  name="creditCardEnabled"
                  label="启用信用卡支付"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>

                <Form.Item
                  noStyle
                  shouldUpdate={(prevValues, currentValues) => prevValues.creditCardEnabled !== currentValues.creditCardEnabled}
                >
                  {({ getFieldValue }) => (
                    getFieldValue('creditCardEnabled') ? (
                      <>
                        <Row gutter={16}>
                          <Col span={12}>
                            <Form.Item
                              name="stripePublicKey"
                              label="Stripe公钥"
                              rules={[{ required: true, message: '请输入Stripe公钥' }]}
                            >
                              <Input placeholder="请输入Stripe公钥" />
                            </Form.Item>
                          </Col>
                          <Col span={12}>
                            <Form.Item
                              name="stripeSecretKey"
                              label="Stripe密钥"
                              rules={[{ required: true, message: '请输入Stripe密钥' }]}
                            >
                              <Input.Password placeholder="请输入Stripe密钥" />
                            </Form.Item>
                          </Col>
                        </Row>
                      </>
                    ) : null
                  )}
                </Form.Item>

                <Form.Item>
                  <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
                    保存设置
                  </Button>
                </Form.Item>
              </Form>
            </Card>
          </TabPane>

          <TabPane
            tab={
              <span>
                <CarOutlined />
                配送设置
              </span>
            }
            key="3"
          >
            <Card>
              <Form
                form={shippingForm}
                layout="vertical"
                onFinish={handleShippingSubmit}
              >
                <Title level={4}>基本配送设置</Title>
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name="freeShippingThreshold"
                      label="免运费阈值"
                      rules={[{ required: true, message: '请输入免运费阈值' }]}
                      tooltip="订单金额超过此值将免运费"
                    >
                      <InputNumber
                        min={0}
                        step={1}
                        style={{ width: '100%' }}
                        prefix="¥"
                        placeholder="请输入免运费阈值"
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="defaultShippingFee"
                      label="默认运费"
                      rules={[{ required: true, message: '请输入默认运费' }]}
                    >
                      <InputNumber
                        min={0}
                        step={1}
                        style={{ width: '100%' }}
                        prefix="¥"
                        placeholder="请输入默认运费"
                      />
                    </Form.Item>
                  </Col>
                </Row>

                <Form.Item
                  name="enableInternationalShipping"
                  label="启用国际配送"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>

                <Form.Item
                  noStyle
                  shouldUpdate={(prevValues, currentValues) => prevValues.enableInternationalShipping !== currentValues.enableInternationalShipping}
                >
                  {({ getFieldValue }) => (
                    getFieldValue('enableInternationalShipping') ? (
                      <Form.Item
                        name="internationalShippingFee"
                        label="国际配送费用"
                        rules={[{ required: true, message: '请输入国际配送费用' }]}
                      >
                        <InputNumber
                          min={0}
                          step={1}
                          style={{ width: '100%' }}
                          prefix="¥"
                          placeholder="请输入国际配送费用"
                        />
                      </Form.Item>
                    ) : null
                  )}
                </Form.Item>

                <Divider />
                <Title level={4}>配送方式</Title>

                <Form.List name="shippingMethods">
                  {(fields, { add, remove }) => (
                    <>
                      {fields.map(({ key, name, ...restField }) => (
                        <div key={key} style={{ marginBottom: 16, border: '1px dashed #d9d9d9', padding: 16, borderRadius: 4 }}>
                          <Row gutter={16}>
                            <Col span={8}>
                              <Form.Item
                                {...restField}
                                name={[name, 'name']}
                                label="配送方式名称"
                                rules={[{ required: true, message: '请输入配送方式名称' }]}
                              >
                                <Input placeholder="请输入配送方式名称" />
                              </Form.Item>
                            </Col>
                            <Col span={6}>
                              <Form.Item
                                {...restField}
                                name={[name, 'fee']}
                                label="配送费用"
                                rules={[{ required: true, message: '请输入配送费用' }]}
                              >
                                <InputNumber
                                  min={0}
                                  step={1}
                                  style={{ width: '100%' }}
                                  prefix="¥"
                                  placeholder="配送费用"
                                />
                              </Form.Item>
                            </Col>
                            <Col span={6}>
                              <Form.Item
                                {...restField}
                                name={[name, 'days']}
                                label="预计送达时间(天)"
                                rules={[{ required: true, message: '请输入预计送达时间' }]}
                              >
                                <Input placeholder="例如: 1-3" />
                              </Form.Item>
                            </Col>
                            <Col span={4} style={{ display: 'flex', alignItems: 'flex-end' }}>
                              <Button
                                type="dashed"
                                danger
                                onClick={() => remove(name)}
                                icon={<DeleteOutlined />}
                                style={{ marginBottom: 24 }}
                              >
                                删除
                              </Button>
                            </Col>
                          </Row>
                        </div>
                      ))}
                      <Form.Item>
                        <Button
                          type="dashed"
                          onClick={() => add({ name: '', fee: 0, days: '' })}
                          block
                          icon={<PlusOutlined />}
                        >
                          添加配送方式
                        </Button>
                      </Form.Item>
                    </>
                  )}
                </Form.List>

                <Form.Item>
                  <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
                    保存设置
                  </Button>
                </Form.Item>
              </Form>
            </Card>
          </TabPane>

          <TabPane
            tab={
              <span>
                <MailOutlined />
                邮件设置
              </span>
            }
            key="4"
          >
            <Card>
              <Form
                form={emailForm}
                layout="vertical"
                onFinish={handleEmailSubmit}
              >
                <Title level={4}>SMTP设置</Title>
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name="smtpHost"
                      label="SMTP服务器"
                      rules={[{ required: true, message: '请输入SMTP服务器地址' }]}
                    >
                      <Input placeholder="例如: smtp.example.com" />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="smtpPort"
                      label="SMTP端口"
                      rules={[{ required: true, message: '请输入SMTP端口' }]}
                    >
                      <InputNumber
                        min={1}
                        max={65535}
                        style={{ width: '100%' }}
                        placeholder="例如: 587"
                      />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name="smtpUser"
                      label="SMTP用户名"
                      rules={[{ required: true, message: '请输入SMTP用户名' }]}
                    >
                      <Input placeholder="请输入SMTP用户名" />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="smtpPassword"
                      label="SMTP密码"
                      rules={[{ required: true, message: '请输入SMTP密码' }]}
                    >
                      <Input.Password placeholder="请输入SMTP密码" />
                    </Form.Item>
                  </Col>
                </Row>

                <Divider />
                <Title level={4}>发件人设置</Title>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name="fromEmail"
                      label="发件人邮箱"
                      rules={[
                        { required: true, message: '请输入发件人邮箱' },
                        { type: 'email', message: '请输入有效的邮箱地址' },
                      ]}
                    >
                      <Input placeholder="请输入发件人邮箱" />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="fromName"
                      label="发件人名称"
                      rules={[{ required: true, message: '请输入发件人名称' }]}
                    >
                      <Input placeholder="请输入发件人名称" />
                    </Form.Item>
                  </Col>
                </Row>

                <Divider />
                <Title level={4}>邮件通知设置</Title>

                <Row gutter={16}>
                  <Col span={8}>
                    <Form.Item
                      name="enableOrderConfirmation"
                      label="订单确认邮件"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      name="enableShippingNotification"
                      label="发货通知邮件"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      name="enablePasswordReset"
                      label="密码重置邮件"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                </Row>

                <Form.Item>
                  <Space>
                    <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
                      保存设置
                    </Button>
                    <Button
                      onClick={() => {
                        message.info('测试邮件已发送，请检查您的邮箱');
                      }}
                    >
                      发送测试邮件
                    </Button>
                  </Space>
                </Form.Item>
              </Form>
            </Card>
          </TabPane>
        </Tabs>
      </Spin>
    </div>
  );
};

export default Settings;