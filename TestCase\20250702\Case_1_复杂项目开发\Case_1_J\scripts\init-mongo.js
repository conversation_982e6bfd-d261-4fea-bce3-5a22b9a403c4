// MongoDB 初始化脚本

// 切换到 ecommerce 数据库
db = db.getSiblingDB('ecommerce');

// 创建用户
db.createUser({
  user: 'ecommerce_user',
  pwd: 'ecommerce_password',
  roles: [
    {
      role: 'readWrite',
      db: 'ecommerce'
    }
  ]
});

// 创建分类集合并插入初始数据
db.categories.insertMany([
  {
    name: '电子产品',
    slug: 'electronics',
    description: '各类电子产品',
    level: 1,
    sort: 1,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: '服装鞋帽',
    slug: 'clothing',
    description: '时尚服装和鞋帽',
    level: 1,
    sort: 2,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: '家居用品',
    slug: 'home',
    description: '家居生活用品',
    level: 1,
    sort: 3,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: '图书音像',
    slug: 'books',
    description: '图书和音像制品',
    level: 1,
    sort: 4,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  }
]);

// 创建管理员用户
const bcrypt = require('bcryptjs');
const adminPassword = bcrypt.hashSync('admin123', 12);

db.users.insertOne({
  username: 'admin',
  email: '<EMAIL>',
  password: adminPassword,
  role: 'admin',
  isActive: true,
  addresses: [],
  createdAt: new Date(),
  updatedAt: new Date()
});

// 创建演示用户
const demoPassword = bcrypt.hashSync('123456', 12);

db.users.insertOne({
  username: 'demo',
  email: '<EMAIL>',
  password: demoPassword,
  role: 'user',
  isActive: true,
  addresses: [
    {
      name: '张三',
      phone: '13800138000',
      province: '北京市',
      city: '北京市',
      district: '朝阳区',
      detail: '某某街道某某小区1号楼101室',
      isDefault: true
    }
  ],
  createdAt: new Date(),
  updatedAt: new Date()
});

// 创建索引
db.users.createIndex({ email: 1 }, { unique: true });
db.users.createIndex({ username: 1 }, { unique: true });
db.categories.createIndex({ slug: 1 }, { unique: true });
db.products.createIndex({ name: 'text', description: 'text', tags: 'text' });
db.products.createIndex({ category: 1 });
db.products.createIndex({ status: 1 });
db.orders.createIndex({ user: 1 });
db.orders.createIndex({ orderNo: 1 }, { unique: true });
db.orders.createIndex({ status: 1 });

print('MongoDB 初始化完成！');
print('管理员账户: <EMAIL> / admin123');
print('演示账户: <EMAIL> / 123456');
