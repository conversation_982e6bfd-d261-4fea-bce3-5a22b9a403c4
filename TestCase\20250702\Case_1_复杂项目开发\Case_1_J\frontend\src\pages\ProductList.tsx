import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Row, Col, Card, Button, Pagination, Spin, Empty, Select, Input, Space, Typography } from 'antd';
import { ShoppingCartOutlined, EyeOutlined, SearchOutlined } from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../store';
import { fetchProducts, fetchCategories, setFilters } from '../store/slices/productSlice';
import { addToCart } from '../store/slices/cartSlice';
import { Product } from '../store/slices/productSlice';

const { Meta } = Card;
const { Title } = Typography;
const { Option } = Select;

const ProductList: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [searchParams, setSearchParams] = useSearchParams();
  const { products, categories, loading, pagination, filters } = useSelector((state: RootState) => state.product);

  const [localFilters, setLocalFilters] = useState({
    search: searchParams.get('search') || '',
    category: searchParams.get('category') || '',
    sort: searchParams.get('sort') || 'createdAt',
    order: searchParams.get('order') || 'desc',
  });

  useEffect(() => {
    dispatch(fetchCategories() as any);
  }, [dispatch]);

  useEffect(() => {
    const params = {
      page: parseInt(searchParams.get('page') || '1'),
      limit: 12,
      search: searchParams.get('search') || '',
      category: searchParams.get('category') || '',
      sort: searchParams.get('sort') || 'createdAt',
      order: searchParams.get('order') || 'desc',
    };

    dispatch(setFilters(params));
    dispatch(fetchProducts(params) as any);
  }, [dispatch, searchParams]);

  const handleFilterChange = (key: string, value: string) => {
    const newFilters = { ...localFilters, [key]: value };
    setLocalFilters(newFilters);

    const newSearchParams = new URLSearchParams(searchParams);
    if (value) {
      newSearchParams.set(key, value);
    } else {
      newSearchParams.delete(key);
    }
    newSearchParams.set('page', '1'); // 重置到第一页

    setSearchParams(newSearchParams);
  };

  const handlePageChange = (page: number) => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set('page', page.toString());
    setSearchParams(newSearchParams);
  };

  const handleAddToCart = (product: Product) => {
    if (product.skus && product.skus.length > 0) {
      const firstSku = product.skus[0];
      dispatch(addToCart({
        productId: product._id,
        sku: firstSku.sku,
        name: product.name,
        image: product.images[0],
        price: firstSku.price,
        quantity: 1,
        specs: firstSku.specs,
        stock: firstSku.stock,
      }));
    }
  };

  const handleViewProduct = (productId: string) => {
    navigate(`/products/${productId}`);
  };

  const renderProductCard = (product: Product) => (
    <Card
      key={product._id}
      hoverable
      className="product-card h-full"
      cover={
        <img
          alt={product.name}
          src={product.images[0] || '/placeholder.jpg'}
          className="product-image"
          onClick={() => handleViewProduct(product._id)}
        />
      }
      actions={[
        <Button
          type="text"
          icon={<EyeOutlined />}
          onClick={() => handleViewProduct(product._id)}
        >
          查看
        </Button>,
        <Button
          type="text"
          icon={<ShoppingCartOutlined />}
          onClick={() => handleAddToCart(product)}
          disabled={product.totalStock === 0}
        >
          {product.totalStock === 0 ? '缺货' : '加购物车'}
        </Button>,
      ]}
    >
      <Meta
        title={
          <div className="truncate" title={product.name}>
            {product.name}
          </div>
        }
        description={
          <Space direction="vertical" size="small" className="w-full">
            <div className="flex items-center justify-between">
              <span className="price">¥{product.basePrice}</span>
              {product.originalPrice && product.originalPrice > product.basePrice && (
                <span className="original-price">¥{product.originalPrice}</span>
              )}
            </div>
            <div className="flex items-center justify-between text-sm text-gray-500">
              <span>销量: {product.sales}</span>
              <span>评分: {product.rating.toFixed(1)}</span>
            </div>
            <div className="text-sm text-gray-500">
              库存: {product.totalStock}
            </div>
          </Space>
        }
      />
    </Card>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <Title level={2} className="mb-6">商品列表</Title>

        {/* 筛选器 */}
        <Card className="mb-6">
          <Row gutter={[16, 16]} align="middle">
            <Col xs={24} sm={12} md={8} lg={6}>
              <Input
                placeholder="搜索商品"
                prefix={<SearchOutlined />}
                value={localFilters.search}
                onChange={(e) => setLocalFilters({ ...localFilters, search: e.target.value })}
                onPressEnter={(e) => handleFilterChange('search', (e.target as HTMLInputElement).value)}
                allowClear
              />
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Select
                placeholder="选择分类"
                value={localFilters.category || undefined}
                onChange={(value) => handleFilterChange('category', value || '')}
                allowClear
                className="w-full"
              >
                {categories.map((category) => (
                  <Option key={category._id} value={category._id}>
                    {category.name}
                  </Option>
                ))}
              </Select>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Select
                value={`${localFilters.sort}-${localFilters.order}`}
                onChange={(value) => {
                  const [sort, order] = value.split('-');
                  handleFilterChange('sort', sort);
                  handleFilterChange('order', order);
                }}
                className="w-full"
              >
                <Option value="createdAt-desc">最新上架</Option>
                <Option value="sales-desc">销量最高</Option>
                <Option value="rating-desc">评分最高</Option>
                <Option value="basePrice-asc">价格从低到高</Option>
                <Option value="basePrice-desc">价格从高到低</Option>
              </Select>
            </Col>
          </Row>
        </Card>

        {/* 商品列表 */}
        <Spin spinning={loading}>
          {products.length > 0 ? (
            <>
              <Row gutter={[16, 16]}>
                {products.map((product) => (
                  <Col key={product._id} xs={12} sm={8} md={6} lg={6}>
                    {renderProductCard(product)}
                  </Col>
                ))}
              </Row>

              {/* 分页 */}
              <div className="flex justify-center mt-8">
                <Pagination
                  current={pagination.current}
                  pageSize={pagination.pageSize}
                  total={pagination.total}
                  onChange={handlePageChange}
                  showSizeChanger={false}
                  showQuickJumper
                  showTotal={(total, range) =>
                    `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                  }
                />
              </div>
            </>
          ) : (
            <Empty
              description="暂无商品"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          )}
        </Spin>
      </div>
    </div>
  );
};

export default ProductList;
