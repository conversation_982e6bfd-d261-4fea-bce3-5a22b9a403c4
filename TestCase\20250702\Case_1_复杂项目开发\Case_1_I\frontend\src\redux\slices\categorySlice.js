import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import categoryService from '../../services/categoryService';

// 初始状态
const initialState = {
  categories: [],
  categoryTree: [],
  category: null,
  loading: false,
  error: null,
  success: false,
};

// 异步action - 获取所有分类
export const getCategories = createAsyncThunk(
  'category/getCategories',
  async (_, { rejectWithValue }) => {
    try {
      return await categoryService.getCategories();
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || error.message);
    }
  }
);

// 异步action - 获取分类树
export const getCategoryTree = createAsyncThunk(
  'category/getCategoryTree',
  async (_, { rejectWithValue }) => {
    try {
      return await categoryService.getCategoryTree();
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || error.message);
    }
  }
);

// 异步action - 获取分类详情
export const getCategoryDetails = createAsyncThunk(
  'category/getCategoryDetails',
  async (id, { rejectWithValue }) => {
    try {
      return await categoryService.getCategoryDetails(id);
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || error.message);
    }
  }
);

// 异步action - 创建分类（管理员）
export const createCategory = createAsyncThunk(
  'category/createCategory',
  async (categoryData, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      return await categoryService.createCategory(
        categoryData,
        auth.userInfo.token
      );
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || error.message);
    }
  }
);

// 异步action - 更新分类（管理员）
export const updateCategory = createAsyncThunk(
  'category/updateCategory',
  async ({ id, categoryData }, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      return await categoryService.updateCategory(
        id,
        categoryData,
        auth.userInfo.token
      );
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || error.message);
    }
  }
);

// 异步action - 删除分类（管理员）
export const deleteCategory = createAsyncThunk(
  'category/deleteCategory',
  async (id, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      return await categoryService.deleteCategory(id, auth.userInfo.token);
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || error.message);
    }
  }
);

// 创建slice
const categorySlice = createSlice({
  name: 'category',
  initialState,
  reducers: {
    clearCategoryDetails: (state) => {
      state.category = null;
    },
    clearCategoryError: (state) => {
      state.error = null;
    },
    resetCategorySuccess: (state) => {
      state.success = false;
    },
  },
  extraReducers: (builder) => {
    builder
      // 获取所有分类
      .addCase(getCategories.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getCategories.fulfilled, (state, action) => {
        state.loading = false;
        state.categories = action.payload;
      })
      .addCase(getCategories.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // 获取分类树
      .addCase(getCategoryTree.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getCategoryTree.fulfilled, (state, action) => {
        state.loading = false;
        state.categoryTree = action.payload;
      })
      .addCase(getCategoryTree.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // 获取分类详情
      .addCase(getCategoryDetails.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getCategoryDetails.fulfilled, (state, action) => {
        state.loading = false;
        state.category = action.payload;
      })
      .addCase(getCategoryDetails.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // 创建分类（管理员）
      .addCase(createCategory.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = false;
      })
      .addCase(createCategory.fulfilled, (state) => {
        state.loading = false;
        state.success = true;
      })
      .addCase(createCategory.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        state.success = false;
      })
      // 更新分类（管理员）
      .addCase(updateCategory.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = false;
      })
      .addCase(updateCategory.fulfilled, (state, action) => {
        state.loading = false;
        state.category = action.payload;
        state.success = true;
      })
      .addCase(updateCategory.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        state.success = false;
      })
      // 删除分类（管理员）
      .addCase(deleteCategory.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = false;
      })
      .addCase(deleteCategory.fulfilled, (state) => {
        state.loading = false;
        state.success = true;
      })
      .addCase(deleteCategory.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        state.success = false;
      });
  },
});

export const { clearCategoryDetails, clearCategoryError, resetCategorySuccess } = categorySlice.actions;
export default categorySlice.reducer;