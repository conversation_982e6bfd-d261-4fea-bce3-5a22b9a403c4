// 验证电子邮件
export const validateEmail = (email) => {
  const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return re.test(email);
};

// 验证密码强度
export const validatePassword = (password) => {
  // 至少8个字符，包含至少一个大写字母，一个小写字母和一个数字
  const re = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/;
  return re.test(password);
};

// 验证手机号码（中国大陆）
export const validatePhone = (phone) => {
  const re = /^1[3-9]\d{9}$/;
  return re.test(phone);
};

// 验证邮政编码（中国大陆）
export const validatePostalCode = (code) => {
  const re = /^\d{6}$/;
  return re.test(code);
};

// 验证URL
export const validateUrl = (url) => {
  try {
    new URL(url);
    return true;
  } catch (e) {
    return false;
  }
};

// 验证身份证号码（中国大陆）
export const validateIdCard = (idCard) => {
  const re = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
  return re.test(idCard);
};

// 验证表单字段是否为空
export const validateRequired = (value) => {
  if (value === null || value === undefined) return false;
  if (typeof value === 'string') return value.trim() !== '';
  return true;
};

// 验证数字范围
export const validateNumberRange = (value, min, max) => {
  const num = Number(value);
  if (isNaN(num)) return false;
  if (min !== undefined && num < min) return false;
  if (max !== undefined && num > max) return false;
  return true;
};

// 验证字符串长度
export const validateLength = (value, min, max) => {
  if (!value) return min === 0;
  if (min !== undefined && value.length < min) return false;
  if (max !== undefined && value.length > max) return false;
  return true;
};