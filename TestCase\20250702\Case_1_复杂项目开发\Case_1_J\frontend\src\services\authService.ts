import { request } from './api';
import { User } from '../store/slices/authSlice';

interface LoginCredentials {
  email: string;
  password: string;
}

interface RegisterData {
  username: string;
  email: string;
  password: string;
  phone?: string;
}

interface AuthResponse {
  message: string;
  token: string;
  user: User;
}

interface UserResponse {
  user: User;
}

class AuthService {
  // 用户登录
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    return request.post('/auth/login', credentials);
  }

  // 用户注册
  async register(userData: RegisterData): Promise<AuthResponse> {
    return request.post('/auth/register', userData);
  }

  // 获取当前用户信息
  async getCurrentUser(): Promise<UserResponse> {
    return request.get('/auth/me');
  }

  // 刷新token
  async refreshToken(): Promise<{ message: string; token: string }> {
    return request.post('/auth/refresh');
  }

  // 登出
  async logout(): Promise<{ message: string }> {
    return request.post('/auth/logout');
  }

  // 更新个人信息
  async updateProfile(userData: Partial<User>): Promise<UserResponse> {
    return request.put('/users/profile', userData);
  }

  // 检查token是否有效
  isTokenValid(): boolean {
    const token = localStorage.getItem('token');
    if (!token) return false;

    try {
      // 简单检查token格式（实际项目中应该验证token的有效性）
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Date.now() / 1000;
      return payload.exp > currentTime;
    } catch {
      return false;
    }
  }

  // 获取token
  getToken(): string | null {
    return localStorage.getItem('token');
  }

  // 设置token
  setToken(token: string): void {
    localStorage.setItem('token', token);
  }

  // 清除token
  clearToken(): void {
    localStorage.removeItem('token');
  }
}

export default new AuthService();
