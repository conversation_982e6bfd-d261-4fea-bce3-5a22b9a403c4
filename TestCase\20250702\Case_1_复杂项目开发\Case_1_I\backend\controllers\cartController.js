const Cart = require('../models/Cart');
const Product = require('../models/Product');
const { successResponse, errorResponse } = require('../utils/apiResponse');

/**
 * 获取用户购物车
 * @route GET /api/cart
 * @access Private
 */
exports.getCart = async (req, res, next) => {
  try {
    let cart = await Cart.findOne({ user: req.user._id }).populate({
      path: 'items.product',
      select: 'name price images stock isActive',
    });

    // 如果购物车不存在，创建一个新的
    if (!cart) {
      cart = await Cart.create({
        user: req.user._id,
        items: [],
        totalPrice: 0,
      });
    }

    // 过滤掉不可用的商品
    const validItems = cart.items.filter(
      (item) => item.product && item.product.isActive && item.product.stock > 0
    );

    // 如果有无效商品，更新购物车
    if (validItems.length !== cart.items.length) {
      cart.items = validItems;
      await cart.save();
    }

    return successResponse(res, { cart }, '获取购物车成功');
  } catch (error) {
    next(error);
  }
};

/**
 * 添加商品到购物车
 * @route POST /api/cart
 * @access Private
 */
exports.addToCart = async (req, res, next) => {
  try {
    const { productId, quantity } = req.body;

    // 验证商品
    const product = await Product.findById(productId);
    if (!product) {
      return errorResponse(res, '商品不存在', 404);
    }

    if (!product.isActive) {
      return errorResponse(res, '该商品已下架', 400);
    }

    if (product.stock < quantity) {
      return errorResponse(res, '商品库存不足', 400);
    }

    // 查找用户购物车
    let cart = await Cart.findOne({ user: req.user._id });

    // 如果购物车不存在，创建一个新的
    if (!cart) {
      cart = await Cart.create({
        user: req.user._id,
        items: [],
        totalPrice: 0,
      });
    }

    // 检查商品是否已在购物车中
    const itemIndex = cart.items.findIndex(
      (item) => item.product.toString() === productId
    );

    if (itemIndex > -1) {
      // 商品已存在，更新数量
      cart.items[itemIndex].quantity += quantity;
    } else {
      // 商品不存在，添加到购物车
      cart.items.push({
        product: productId,
        quantity,
        price: product.price,
      });
    }

    // 保存购物车
    await cart.save();

    // 返回更新后的购物车
    cart = await Cart.findById(cart._id).populate({
      path: 'items.product',
      select: 'name price images stock',
    });

    return successResponse(res, { cart }, '商品已添加到购物车');
  } catch (error) {
    next(error);
  }
};

/**
 * 更新购物车商品数量
 * @route PUT /api/cart/:itemId
 * @access Private
 */
exports.updateCartItem = async (req, res, next) => {
  try {
    const { quantity } = req.body;
    const { itemId } = req.params;

    // 查找用户购物车
    const cart = await Cart.findOne({ user: req.user._id });

    if (!cart) {
      return errorResponse(res, '购物车不存在', 404);
    }

    // 查找购物车项
    const item = cart.items.id(itemId);
    if (!item) {
      return errorResponse(res, '购物车项不存在', 404);
    }

    // 验证商品库存
    const product = await Product.findById(item.product);
    if (!product) {
      return errorResponse(res, '商品不存在', 404);
    }

    if (product.stock < quantity) {
      return errorResponse(res, '商品库存不足', 400);
    }

    // 更新数量
    item.quantity = quantity;

    // 保存购物车
    await cart.save();

    // 返回更新后的购物车
    const updatedCart = await Cart.findById(cart._id).populate({
      path: 'items.product',
      select: 'name price images stock',
    });

    return successResponse(res, { cart: updatedCart }, '购物车已更新');
  } catch (error) {
    next(error);
  }
};

/**
 * 从购物车中删除商品
 * @route DELETE /api/cart/:itemId
 * @access Private
 */
exports.removeFromCart = async (req, res, next) => {
  try {
    const { itemId } = req.params;

    // 查找用户购物车
    const cart = await Cart.findOne({ user: req.user._id });

    if (!cart) {
      return errorResponse(res, '购物车不存在', 404);
    }

    // 查找购物车项
    const item = cart.items.id(itemId);
    if (!item) {
      return errorResponse(res, '购物车项不存在', 404);
    }

    // 删除购物车项
    item.remove();

    // 保存购物车
    await cart.save();

    // 返回更新后的购物车
    const updatedCart = await Cart.findById(cart._id).populate({
      path: 'items.product',
      select: 'name price images stock',
    });

    return successResponse(res, { cart: updatedCart }, '商品已从购物车中删除');
  } catch (error) {
    next(error);
  }
};

/**
 * 清空购物车
 * @route DELETE /api/cart
 * @access Private
 */
exports.clearCart = async (req, res, next) => {
  try {
    // 查找用户购物车
    const cart = await Cart.findOne({ user: req.user._id });

    if (!cart) {
      return errorResponse(res, '购物车不存在', 404);
    }

    // 清空购物车项
    cart.items = [];
    cart.totalPrice = 0;

    // 保存购物车
    await cart.save();

    return successResponse(res, { cart }, '购物车已清空');
  } catch (error) {
    next(error);
  }
};