const jwt = require('jsonwebtoken');
const User = require('../models/user.model');

// 保护需要登录的路由
exports.protect = async (req, res, next) => {
  try {
    let token;

    // 从请求头获取令牌
    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1];
    } 
    // 也可以从cookie获取令牌
    // else if (req.cookies.token) {
    //   token = req.cookies.token;
    // }

    // 检查令牌是否存在
    if (!token) {
      return res.status(401).json({
        success: false,
        message: '没有访问权限，请登录'
      });
    }

    try {
      // 验证令牌
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your_jwt_secret_key');

      // 获取用户信息
      req.user = await User.findById(decoded.id);

      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: '找不到与此令牌关联的用户'
        });
      }

      next();
    } catch (err) {
      return res.status(401).json({
        success: false,
        message: '无效的令牌，请重新登录'
      });
    }
  } catch (err) {
    console.error('认证中间件错误:', err);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后再试'
    });
  }
};

// 授权角色访问
exports.authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user.role || !roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: `用户角色 ${req.user.role} 无权访问此资源`
      });
    }
    next();
  };
}; 