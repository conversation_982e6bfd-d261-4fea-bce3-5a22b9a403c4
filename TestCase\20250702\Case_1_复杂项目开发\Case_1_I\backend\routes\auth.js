const express = require('express');
const router = express.Router();
const authController = require('../controllers/authController');
const { protect } = require('../middleware/auth');

// 注册新用户
router.post('/register', authController.register);

// 用户登录
router.post('/login', authController.login);

// 获取当前用户信息
router.get('/me', protect, authController.getCurrentUser);

// 修改密码
router.put('/change-password', protect, authController.changePassword);

// 忘记密码（发送重置链接）
router.post('/forgot-password', authController.forgotPassword);

// 重置密码
router.post('/reset-password/:token', authController.resetPassword);

// 退出登录
router.post('/logout', protect, authController.logout);

module.exports = router;