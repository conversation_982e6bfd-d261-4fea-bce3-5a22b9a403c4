import React from 'react';
import { Layout, Row, Col, Typography, Space, Divider } from 'antd';
import {
  FacebookOutlined,
  TwitterOutlined,
  InstagramOutlined,
  YoutubeOutlined,
  MailOutlined,
  PhoneOutlined,
  HomeOutlined,
} from '@ant-design/icons';
import { Link } from 'react-router-dom';

const { Footer } = Layout;
const { Title, Text } = Typography;

const AppFooter = () => {
  return (
    <Footer className="app-footer">
      <div className="footer-content">
        <Row gutter={[24, 24]}>
          <Col xs={24} sm={12} md={6}>
            <div className="footer-section">
              <Title level={4}>关于我们</Title>
              <Text>我们是一家专注于提供高品质商品的电商平台，致力于为用户提供最好的购物体验。</Text>
            </div>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <div className="footer-section">
              <Title level={4}>快速链接</Title>
              <ul className="footer-links">
                <li>
                  <Link to="/">首页</Link>
                </li>
                <li>
                  <Link to="/products">商品</Link>
                </li>
                <li>
                  <Link to="/cart">购物车</Link>
                </li>
                <li>
                  <Link to="/profile">个人中心</Link>
                </li>
              </ul>
            </div>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <div className="footer-section">
              <Title level={4}>客户服务</Title>
              <ul className="footer-links">
                <li>
                  <Link to="/faq">常见问题</Link>
                </li>
                <li>
                  <Link to="/shipping">配送信息</Link>
                </li>
                <li>
                  <Link to="/returns">退换政策</Link>
                </li>
                <li>
                  <Link to="/contact">联系我们</Link>
                </li>
              </ul>
            </div>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <div className="footer-section">
              <Title level={4}>联系方式</Title>
              <Space direction="vertical">
                <Text>
                  <HomeOutlined /> 地址：北京市朝阳区某某街道123号
                </Text>
                <Text>
                  <PhoneOutlined /> 电话：************
                </Text>
                <Text>
                  <MailOutlined /> 邮箱：<EMAIL>
                </Text>
              </Space>
              <div className="social-icons">
                <Space size="large">
                  <a href="https://facebook.com" target="_blank" rel="noopener noreferrer">
                    <FacebookOutlined />
                  </a>
                  <a href="https://twitter.com" target="_blank" rel="noopener noreferrer">
                    <TwitterOutlined />
                  </a>
                  <a href="https://instagram.com" target="_blank" rel="noopener noreferrer">
                    <InstagramOutlined />
                  </a>
                  <a href="https://youtube.com" target="_blank" rel="noopener noreferrer">
                    <YoutubeOutlined />
                  </a>
                </Space>
              </div>
            </div>
          </Col>
        </Row>
        <Divider />
        <div className="footer-bottom">
          <Text>© {new Date().getFullYear()} 电商网站. 保留所有权利.</Text>
        </div>
      </div>
    </Footer>
  );
};

export default AppFooter;