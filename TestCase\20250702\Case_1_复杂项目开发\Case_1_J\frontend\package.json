{"name": "ecommerce-frontend", "version": "1.0.0", "description": "电商网站前端应用", "private": true, "dependencies": {"@types/node": "^20.5.0", "@types/react": "^18.2.20", "@types/react-dom": "^18.2.7", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "typescript": "^5.1.6", "antd": "^5.8.4", "react-router-dom": "^6.15.0", "axios": "^1.5.0", "@reduxjs/toolkit": "^1.9.5", "react-redux": "^8.1.2", "redux-persist": "^6.0.0", "dayjs": "^1.11.9", "lodash": "^4.17.21", "@types/lodash": "^4.14.196"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:3001"}