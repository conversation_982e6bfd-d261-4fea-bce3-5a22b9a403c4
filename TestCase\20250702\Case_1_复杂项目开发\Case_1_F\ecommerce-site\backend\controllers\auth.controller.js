const User = require('../models/user.model');
const { validationResult } = require('express-validator');
const jwt = require('jsonwebtoken');

// @desc    注册用户
// @route   POST /api/auth/register
// @access  公开
exports.register = async (req, res) => {
  try {
    // 验证请求数据
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }

    const { username, email, password, firstName, lastName, phoneNumber } = req.body;

    // 检查用户是否已存在
    let user = await User.findOne({ email });
    if (user) {
      return res.status(400).json({ success: false, message: '该邮箱已被注册' });
    }

    user = await User.findOne({ username });
    if (user) {
      return res.status(400).json({ success: false, message: '该用户名已被使用' });
    }

    // 创建新用户
    user = new User({
      username,
      email,
      password,
      firstName,
      lastName,
      phoneNumber
    });

    await user.save();

    // 生成JWT令牌
    sendTokenResponse(user, 201, res);
  } catch (err) {
    console.error('注册失败:', err);
    res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
  }
};

// @desc    用户登录
// @route   POST /api/auth/login
// @access  公开
exports.login = async (req, res) => {
  try {
    const { email, password } = req.body;

    // 验证请求数据
    if (!email || !password) {
      return res.status(400).json({ success: false, message: '请提供邮箱和密码' });
    }

    // 检查用户是否存在
    const user = await User.findOne({ email }).select('+password');
    if (!user) {
      return res.status(401).json({ success: false, message: '邮箱或密码错误' });
    }

    // 验证密码
    const isMatch = await user.matchPassword(password);
    if (!isMatch) {
      return res.status(401).json({ success: false, message: '邮箱或密码错误' });
    }

    // 生成JWT令牌
    sendTokenResponse(user, 200, res);
  } catch (err) {
    console.error('登录失败:', err);
    res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
  }
};

// @desc    获取当前登录用户信息
// @route   GET /api/auth/me
// @access  私有
exports.getMe = async (req, res) => {
  try {
    const user = await User.findById(req.user.id);
    res.status(200).json({ success: true, data: user });
  } catch (err) {
    console.error('获取用户信息失败:', err);
    res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
  }
};

// @desc    更新用户信息
// @route   PUT /api/auth/updatedetails
// @access  私有
exports.updateDetails = async (req, res) => {
  try {
    const { firstName, lastName, email, phoneNumber, address } = req.body;
    
    const updateFields = {};
    if (firstName) updateFields.firstName = firstName;
    if (lastName) updateFields.lastName = lastName;
    if (email) updateFields.email = email;
    if (phoneNumber) updateFields.phoneNumber = phoneNumber;
    if (address) updateFields.address = address;
    
    const user = await User.findByIdAndUpdate(
      req.user.id, 
      updateFields, 
      { new: true, runValidators: true }
    );

    res.status(200).json({ success: true, data: user });
  } catch (err) {
    console.error('更新用户信息失败:', err);
    res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
  }
};

// @desc    更新密码
// @route   PUT /api/auth/updatepassword
// @access  私有
exports.updatePassword = async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;

    if (!currentPassword || !newPassword) {
      return res.status(400).json({ success: false, message: '请提供当前密码和新密码' });
    }

    // 验证当前密码
    const user = await User.findById(req.user.id).select('+password');
    const isMatch = await user.matchPassword(currentPassword);
    if (!isMatch) {
      return res.status(401).json({ success: false, message: '当前密码错误' });
    }

    // 更新密码
    user.password = newPassword;
    await user.save();

    sendTokenResponse(user, 200, res);
  } catch (err) {
    console.error('更新密码失败:', err);
    res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
  }
};

// @desc    退出登录
// @route   GET /api/auth/logout
// @access  私有
exports.logout = (req, res) => {
  res.status(200).json({ success: true, message: '退出登录成功' });
};

// 生成JWT令牌并发送响应
const sendTokenResponse = (user, statusCode, res) => {
  // 创建令牌
  const token = user.getSignedJwtToken();

  const options = {
    expires: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30天
    httpOnly: true
  };

  // 在生产环境中使用HTTPS
  if (process.env.NODE_ENV === 'production') {
    options.secure = true;
  }

  // 剔除敏感字段
  const userData = user.toObject();
  delete userData.password;

  res.status(statusCode).json({
    success: true,
    token,
    user: userData
  });
}; 