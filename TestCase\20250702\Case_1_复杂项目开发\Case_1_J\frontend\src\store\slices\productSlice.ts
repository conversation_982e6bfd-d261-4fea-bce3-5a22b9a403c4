import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import productService from '../../services/productService';

export interface Product {
  _id: string;
  name: string;
  description: string;
  category: {
    _id: string;
    name: string;
    slug: string;
  };
  brand: string;
  images: string[];
  basePrice: number;
  originalPrice?: number;
  skus: Array<{
    sku: string;
    specs: Array<{ name: string; value: string }>;
    price: number;
    stock: number;
    image?: string;
  }>;
  totalStock: number;
  sales: number;
  rating: number;
  reviewCount: number;
  tags: string[];
  status: 'active' | 'inactive' | 'out_of_stock';
  createdAt: string;
}

export interface Category {
  _id: string;
  name: string;
  slug: string;
  description?: string;
  image?: string;
  parent?: string;
  level: number;
  children?: Category[];
}

interface ProductState {
  products: Product[];
  currentProduct: Product | null;
  categories: Category[];
  hotProducts: Product[];
  recommendedProducts: Product[];
  loading: boolean;
  error: string | null;
  pagination: {
    current: number;
    pageSize: number;
    total: number;
    pages: number;
  };
  filters: {
    search?: string;
    category?: string;
    brand?: string;
    minPrice?: number;
    maxPrice?: number;
    sort?: string;
    order?: 'asc' | 'desc';
  };
}

const initialState: ProductState = {
  products: [],
  currentProduct: null,
  categories: [],
  hotProducts: [],
  recommendedProducts: [],
  loading: false,
  error: null,
  pagination: {
    current: 1,
    pageSize: 12,
    total: 0,
    pages: 0,
  },
  filters: {},
};

// 异步actions
export const fetchProducts = createAsyncThunk(
  'product/fetchProducts',
  async (params: any, { rejectWithValue }) => {
    try {
      const response = await productService.getProducts(params);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const fetchProductById = createAsyncThunk(
  'product/fetchProductById',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await productService.getProductById(id);
      return response.product;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const fetchCategories = createAsyncThunk(
  'product/fetchCategories',
  async (_, { rejectWithValue }) => {
    try {
      const response = await productService.getCategories();
      return response.categories;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const fetchHotProducts = createAsyncThunk(
  'product/fetchHotProducts',
  async (limit: number = 8, { rejectWithValue }) => {
    try {
      const response = await productService.getHotProducts(limit);
      return response.products;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const fetchRecommendedProducts = createAsyncThunk(
  'product/fetchRecommendedProducts',
  async (limit: number = 8, { rejectWithValue }) => {
    try {
      const response = await productService.getRecommendedProducts(limit);
      return response.products;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

const productSlice = createSlice({
  name: 'product',
  initialState,
  reducers: {
    setFilters: (state, action: PayloadAction<any>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = {};
    },
    clearCurrentProduct: (state) => {
      state.currentProduct = null;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // 获取商品列表
      .addCase(fetchProducts.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchProducts.fulfilled, (state, action) => {
        state.loading = false;
        state.products = action.payload.products;
        state.pagination = action.payload.pagination;
        state.error = null;
      })
      .addCase(fetchProducts.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // 获取商品详情
      .addCase(fetchProductById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchProductById.fulfilled, (state, action) => {
        state.loading = false;
        state.currentProduct = action.payload;
        state.error = null;
      })
      .addCase(fetchProductById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // 获取分类列表
      .addCase(fetchCategories.fulfilled, (state, action) => {
        state.categories = action.payload;
      })
      // 获取热门商品
      .addCase(fetchHotProducts.fulfilled, (state, action) => {
        state.hotProducts = action.payload;
      })
      // 获取推荐商品
      .addCase(fetchRecommendedProducts.fulfilled, (state, action) => {
        state.recommendedProducts = action.payload;
      });
  },
});

export const { setFilters, clearFilters, clearCurrentProduct, clearError } = productSlice.actions;
export default productSlice.reducer;
